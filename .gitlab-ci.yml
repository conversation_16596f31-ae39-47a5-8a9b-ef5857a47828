stages:
  - buildjob
  - msg_job
  - msg_job_failure

buildjob:
  stage: buildjob
  before_script:
    - mvn -f ./pom.xml clean install
    - docker-compose -f docker-compose_dev.yml down
  script:
    - docker-compose -f docker-compose_dev.yml up -d --build
  after_script:
    - docker image prune -f

msg_job:
  stage: msg_job
  script: curl 'https://oapi.dingtalk.com/robot/send?access_token=d11de9f5aa35ab1d2f379131d03319f49c409ac359e2b83ef8c5e89ad7472d13' -H 'Content-Type:application/json' -d '{"msgtype":"text","text":{"content":"项目[JY-CPDS-SUPERVISE_develop]构建成功。"},"at":{"isAtAll":false}}'
  when: on_success

msg_job_failure:
  stage: msg_job_failure
  script: curl 'https://oapi.dingtalk.com/robot/send?access_token=d11de9f5aa35ab1d2f379131d03319f49c409ac359e2b83ef8c5e89ad7472d13' -H 'Content-Type:application/json' -d '{"msgtype":"link","link":{"text":"项目[JY-CPDS-SUPERVISE_develop]构建失败","title":"项目[JY-CPDS-SUPERVISE_develop]构建失败","picUrl":"https://ss0.bdstatic.com/70cFvHSh_Q1YnxGkpoWK1HF6hhy/it/u=1682497276,953611760&fm=26&gp=0.jpg","messageUrl":"http://**************:81/JY-CPDS/JY-CPDS-SUPERVISE/-/jobs"},"at":{"isAtAll":true}}'
  when: on_failure
