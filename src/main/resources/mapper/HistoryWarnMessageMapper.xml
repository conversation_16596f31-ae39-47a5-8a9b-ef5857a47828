<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IHistoryWarnMessageDao">
    <!--结果映射 -->
    <resultMap type="jylink.cpds.domain.HistoryWarnMessage" id="HistoryWarnMessageInstance">
        <id property="id" column="uuid" />
        <result property="tunnelId" column="tunnel_id" />
        <result property="surveyWaterMileage" column="survey_water_mileage" />
        <result property="orgCode" column="org_code" />
        <result property="orgName" column="org_name" />
        <result property="messageTitle" column="message_title" />
        <result property="messageContent" column="message_content" />
        <result property="dataId" column="data_id" />
        <result property="messageLevel" column="message_level" />
        <result property="messageType" column="message_type" />
        <result property="triggerPersonId" column="trigger_person_id" />
        <result property="triggerPersonName" column="trigger_person_name" />
        <result property="triggerTime" column="trigger_time" />
        <result property="isCancel" column="is_cancel" />
        <result property="cancelTime" column="cancel_time" />
        <result property="cancelPersonId" column="cancel_person_id" />
        <result property="cancelPersonName" column="cancel_person_name" />
        <result property="warnMessageId" column="warn_message_id" />
        <result property="createTime" column="create_time" />
        <result property="delFlag" column="del_flag" />
        <result property="messageRank" column="message_rank" />
        <result property="handingMethod" column="handing_method" />
        <result property="mineCityzoneName" column="Mine_CityZone_NAME" />
        <result property="zoneCountyIdName" column="ZONE_COUNTY_ID_NAME" />
        <result property="replyFlag" column="reply_flag" />
        <result property="extendFields" column="extend_fields" />
        <result property="messageDetail" column="message_detail" />
    </resultMap>

    <resultMap type="jylink.cpds.serviceModel.dto.WarnAbnormalDto" id="WarnAbnormalInstance">
        <result property="time" column="create_time" />
        <result property="orgCode" column="org_code" />
        <result property="orgName" column="org_name" />
        <result property="content" column="message_content" />
        <result property="position" column="survey_water_mileage" />
        <result property="type" column="message_type" />
        <result property="isHandle" column="is_handle" />
        <result property="handingMethod" column="handing_method" />
        <result property="mineCityzoneName" column="Mine_CityZone_NAME" />
        <result property="zoneCountyIdName" column="ZONE_COUNTY_ID_NAME" />
        <result property="workName" column="work_name" />
    </resultMap>

    <resultMap type="jylink.cpds.serviceModel.dto.AnomalyAnalysisInfoDto" id="AnomalyAnalysisInfoDto">
        <result property="geologicalAnomalyNum" column="geologicalAnomalyNum" />
        <result property="orgCode" column="org_code" />
        <result property="orgName" column="org_name" />
        <result property="otherAnomalyNum" column="warnNum" />
        <result property="total" column="total" />
    </resultMap>

    <resultMap type="jylink.cpds.serviceModel.dto.AnomalyAnalysisDto" id="AnomalyAnalysisDto">
        <result property="warnNum" column="warnNum" />
        <result property="handleNum" column="handleNum" />
        <result property="noHandleNum" column="noHandleNum" />
    </resultMap>

    <resultMap type="jylink.cpds.serviceModel.dto.WarnAbnormalNumberDto" id="BaseItemInstance">
        <result property="handleNumber" column="handleNumber" />
        <result property="noHandleNumber" column="noHandleNumber" />
        <result property="excavation" column="excavation" />
        <result property="water" column="water" />
        <result property="total" column="total" />
        <result property="evacWarn" column="evacWarn" />
    </resultMap>

    <!-- 添加数据 -->
    <insert id="add">
        insert
        into tfs_history_warn_message
        (uuid,
         tunnel_id,
         survey_water_mileage,
         org_code,
         org_name,
         message_title,
         message_content,
         data_id,
         message_level,
         message_type,
         trigger_person_id,
         trigger_person_name,
         trigger_time,
         is_cancel,
         cancel_time,
         cancel_person_id,
         cancel_person_name,
         warn_message_id,
         create_time,
         message_rank,
         del_flag)
        values (#{instance.id},
                #{instance.tunnelId},
                #{instance.surveyWaterMileage},
                #{instance.orgCode},
                #{instance.orgName},
                #{instance.messageTitle},
                #{instance.messageContent},
                #{instance.dataId},
                #{instance.messageLevel},
                #{instance.messageType},
                #{instance.triggerPersonId},
                #{instance.triggerPersonName},
                #{instance.triggerTime},
                #{instance.isCancel},
                #{instance.cancelTime},
                #{instance.cancelPersonId},
                #{instance.cancelPersonName},
                #{instance.warnMessageId},
                #{instance.createTime},
                #{instance.messageRank},
                0)
    </insert>

    <!-- 添加数据 -->
    <insert id="addAll">
        insert
        into tfs_history_warn_message
        (uuid,
         tunnel_id,
         survey_water_mileage,
         org_code,
         org_name,
         message_title,
         message_content,
         data_id,
         message_level,
         message_type,
         trigger_person_id,
         trigger_person_name,
         trigger_time,
         is_cancel,
         cancel_time,
         cancel_person_id,
         cancel_person_name,
         warn_message_id,
         create_time,
         message_rank,
         del_flag)
        values
        <foreach collection="list" item="instance" separator=",">
        (#{instance.id},
                #{instance.tunnelId},
                #{instance.surveyWaterMileage},
                #{instance.orgCode},
                #{instance.orgName},
                #{instance.messageTitle},
                #{instance.messageContent},
                #{instance.dataId},
                #{instance.messageLevel},
                #{instance.messageType},
                #{instance.triggerPersonId},
                #{instance.triggerPersonName},
                #{instance.triggerTime},
                #{instance.isCancel},
                #{instance.cancelTime},
                #{instance.cancelPersonId},
                #{instance.cancelPersonName},
                #{instance.warnMessageId},
                #{instance.createTime},
                #{instance.messageRank},
                0)
        </foreach>
    </insert>

    <!-- 根据Id查询数据 -->
    <select id="getById" resultMap="HistoryWarnMessageInstance">
        SELECT
            t.uuid,
            t.org_code,
            t.org_name,
            t.tunnel_id,
            IFNULL( de.work_name, face.work_name ) AS work_name,
            t.data_id,
            t.survey_water_mileage,
            t.trigger_time,
            t.is_cancel,
            t.message_type,
            t.message_title,
            t.message_content,
            t.handing_method,
            t.message_detail,
            t.cancel_time,
            t.cancel_person_id,
            t.cancel_person_name,
            t.extend_fields,
            t.warn_message_id
        FROM
            (
                SELECT
                    uuid,
                    org_code,
                    org_name,
                    tunnel_id,
                    data_id,
                    survey_water_mileage,
                    create_time AS trigger_time,
                    0 AS is_cancel,
                    message_type,
                    message_title,
                    message_content,
                    "" handing_method,
                    message_detail,
                    NULL AS cancel_time,
                    NULL AS cancel_person_id,
                    NULL AS cancel_person_name,
                    extend_fields,
                    uuid AS warn_message_id
                FROM
                    tfs_warn_message
                WHERE
                    del_flag = 0
                  AND uuid = #{id} UNION ALL
                SELECT
                    uuid,
                    org_code,
                    org_name,
                    tunnel_id,
                    data_id,
                    survey_water_mileage,
                    trigger_time,
                    is_cancel,
                    message_type,
                    message_title,
                    message_content,
                    handing_method,
                    message_detail,
                    cancel_time,
                    cancel_person_id,
                    cancel_person_name,
                    extend_fields,
                    warn_message_id
                FROM
                    tfs_history_warn_message
                WHERE
                    del_flag = 0
                  AND is_cancel = 1
                  AND uuid = #{id}
            ) t
                LEFT JOIN tfs_tunnel_designs de ON de.uuid = t.tunnel_id
                AND de.del_flag = 0
                AND de.STATUS = 2
                LEFT JOIN tfs_work_face face ON face.uuid = t.tunnel_id
    </select>

    <!-- 根据机构编码查询数据 -->
    <select id="getByOrgCode" resultMap="HistoryWarnMessageInstance">
        select uuid,
               tunnel_id,
               survey_water_mileage,
               org_code,
               org_name,
               message_title,
               message_content,
               data_id,
               message_level,
               message_type,
               trigger_person_id,
               trigger_person_name,
               trigger_time,
               is_cancel,
               cancel_time,
               cancel_person_id,
               cancel_person_name,
               warn_message_id,
               create_time,
               message_rank,
               message_detail,
               extend_fields,
               del_flag
        from tfs_history_warn_message
        where org_code = #{orgCode}
          and del_flag = 0
        order by create_time desc
    </select>

    <!-- 根据条件查询数据 -->
    <select id="getParam" resultMap="HistoryWarnMessageInstance">
        select * from (SELECT
        tunnel_id,
        create_time,
        org_code,
        org_name,
        survey_water_mileage,
        message_type,
        message_content,
        message_title,
        trigger_time,
        cancel_time,
        del_flag
        FROM
        tfs_history_warn_message
        WHERE
        ( message_type = 1 OR message_type = 0 ) UNION ALL
        SELECT
        tunnel_id,
        create_time,
        org_code,
        org_name,
        survey_water_mileage,
        '5' message_type,
        alarm_message message_content,
        alarm_message message_title,
        alarm_time trigger_time,
        cancel_time,
        del_flag
        FROM
        tfs_evacuate_warn where alarm_status = 0 ) as c
        where org_code = #{orgCode}
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()">
            and
            trigger_time between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (trigger_time,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (trigger_time,'%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="messageType != null ">
            and
            message_type = #{messageType}
        </if>
        <if test="orgName != null and orgName != ''">
            and org_name like concat ('%',#{orgName},'%')
        </if>
        and del_flag = 0
        order by create_time desc
    </select>
    <select id="getParamByOrgCodeList" resultMap="HistoryWarnMessageInstance">
        select * from (SELECT
        tunnel_id,
        create_time,
        org_code,
        org_name,
        survey_water_mileage,
        message_type,
        message_content,
        message_title,
        trigger_time,
        cancel_time,
        del_flag
        FROM
        tfs_history_warn_message
        WHERE
        ( message_type = 1 OR message_type = 0 ) UNION ALL
        SELECT
        tunnel_id,
        create_time,
        org_code,
        org_name,
        survey_water_mileage,
        '5' message_type,
        alarm_message message_content,
        alarm_message message_title,
        alarm_time trigger_time,
        cancel_time,
        del_flag
        FROM
        tfs_evacuate_warn where alarm_status = 0 ) as c
        where org_code
        in (
        <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach>
        )
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()">
            and
            DATE_FORMAT (trigger_time,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (trigger_time,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (trigger_time,'%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="messageType != null ">
            and
            message_type = #{messageType}
        </if>
        <if test="orgName != null and orgName != ''">
            and org_name like concat ('%',#{orgName},'%')
        </if>
        and del_flag = 0
        order by create_time desc
    </select>

    <!-- 根据条件查询数据条数 -->
    <select id="getParamCount" resultType="long">
        select count(1) from (SELECT
        tunnel_id,
        create_time,
        org_code,
        org_name,
        survey_water_mileage,
        message_type,
        message_content,
        trigger_time,
        cancel_time,
        del_flag
        FROM
        tfs_history_warn_message
        WHERE
        ( message_type = 1 OR message_type = 0 ) UNION ALL
        SELECT
        tunnel_id,
        create_time,
        org_code,
        org_name,
        survey_water_mileage,
        '5' message_type,
        alarm_message message_content,
        alarm_time trigger_time,
        cancel_time,
        del_flag
        FROM
        tfs_evacuate_warn where alarm_status = 0 ) as c
        where org_code = #{orgCode}
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()">
            and
            DATE_FORMAT (trigger_time,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (trigger_time,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (trigger_time,'%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="messageType != null ">
            and
            message_type = #{messageType}
        </if>
        <if test="orgName != null and orgName != ''">
            and org_name like concat ('%',#{orgName},'%')
        </if>
        and del_flag = 0
    </select>

    <select id="getParamCountByOrgCodeList" resultType="long">
        select count(1) from (SELECT
        tunnel_id,
        create_time,
        org_code,
        org_name,
        survey_water_mileage,
        message_type,
        message_content,
        trigger_time,
        cancel_time,
        del_flag
        FROM
        tfs_history_warn_message
        WHERE
        ( message_type = 1 OR message_type = 0 ) UNION ALL
        SELECT
        tunnel_id,
        create_time,
        org_code,
        org_name,
        survey_water_mileage,
        '5' message_type,
        alarm_message message_content,
        alarm_time trigger_time,
        cancel_time,
        del_flag
        FROM
        tfs_evacuate_warn where alarm_status = 0 ) as c
        where org_code in (<foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach>)
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()">
            and
            DATE_FORMAT (trigger_time,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (trigger_time,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (trigger_time,'%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="messageType != null ">
            and
            message_type = #{messageType}
        </if>
        <if test="orgName != null and orgName != ''">
            and org_name like concat ('%',#{orgName},'%')
        </if>
        and del_flag = 0

    </select>

    <!-- 根据机构编码查询数据 -->
    <select id="getCount" resultType="long">
        select
        count(*)
        from tfs_history_warn_message
        where org_code = #{orgCode}
        and del_flag = 0
    </select>

    <!-- 更新数据 -->
    <update id="update">
        update tfs_history_warn_message
        set tunnel_id=#{instance.tunnelId},
        survey_water_mileage=#{instance.surveyWaterMileage},
        message_title=#{instance.messageTitle},
        message_content=#{instance.messageContent},
        data_id=#{instance.dataId},
        message_level=#{instance.messageLevel},
        message_type=#{instance.messageType},
        trigger_person_id=#{instance.triggerPersonId},
        trigger_person_name=#{instance.triggerPersonName},
        trigger_time=#{instance.triggerTime},
        is_cancel=#{instance.isCancel},
        cancel_time=#{instance.cancelTime},
        cancel_person_id=#{instance.cancelPersonId},
        cancel_person_name=#{instance.cancelPersonName},
        warn_message_id=#{instance.warnMessageId},
        reply_flag=#{instance.replyFlag}
        where uuid=#{instance.id}
        and org_code=#{instance.orgCode}
        and del_flag = 0
    </update>

    <!-- 删除数据 -->
    <update id="delete">
        update tfs_history_warn_message set del_flag = 1 where uuid = #{id} and del_flag = 0 and org_code = #{orgCode};
    </update>

    <!-- 查询数据是否存在 -->
    <select id="anyById" resultType="boolean">
        select (
                   select count(*) from tfs_history_warn_message where org_code = #{orgCode} and del_flag = 0 and uuid = #{id} and org_code = #{orgCode}
               ) > 0;
    </select>

    <!-- 查询数据是否存在 -->
    <select id="anyByTunnelId" resultType="boolean">
        select (
                   select count(*) from tfs_history_warn_message where org_code = #{orgCode} and del_flag = 0 and tunnel_id = #{tunnelId} and message_type=#{messageType}
               ) > 0;
    </select>

    <!-- 根据探水设计Id查询对象 -->
    <select id="getByTunnelId" resultMap="HistoryWarnMessageInstance">
        select
        uuid,
        tunnel_id,
        survey_water_mileage,
        org_code,
        org_name,
        message_title,
        message_content,
        data_id,
        message_level,
        message_type,
        trigger_person_id,
        trigger_person_name,
        trigger_time,
        is_cancel,
        cancel_time,
        cancel_person_id,
        cancel_person_name,
        warn_message_id,
        create_time,
        message_rank,
        message_detail,
        extend_fields,
        del_flag
        from tfs_history_warn_message
        where tunnel_id = #{tunnelId}
        and org_code=#{orgCode}
        and message_type=#{messageType}
        and del_flag = 0
    </select>

    <!-- 根据探水设计Id查询对象 -->
    <select id="getByDataId" resultMap="HistoryWarnMessageInstance">
        select
        uuid,
        tunnel_id,
        survey_water_mileage,
        org_code,
        org_name,
        message_title,
        message_content,
        data_id,
        message_level,
        message_type,
        trigger_person_id,
        trigger_person_name,
        trigger_time,
        is_cancel,
        cancel_time,
        cancel_person_id,
        cancel_person_name,
        warn_message_id,
        create_time,
        message_rank,
        message_detail,
        extend_fields,
        del_flag
        from tfs_history_warn_message
        where data_id = #{dataId}
        and org_code=#{orgCode}
        and message_type=#{messageType}
        and del_flag = 0
    </select>

    <!-- 同时查询警告表和历史警告表数据分页 -->
    <select id="getWarnAbnormal" resultMap="WarnAbnormalInstance">
        SELECT
            c.tunnel_id,
            c.create_time,
            c.org_code,
            c.org_name,
            c.survey_water_mileage,
            c.message_type,
            c.message_content,
            c.handing_method,
            c.is_handle,
            dcm.Mine_CityZone_NAME,
            dcm.ZONE_COUNTY_ID_NAME,
            design.work_name
        FROM
            (
                SELECT
                    tunnel_id,
                    create_time,
                    org_code,
                    org_name,
                    survey_water_mileage,
                    message_type,
                    message_content,
                    del_flag,
                    '' AS handing_method,
                    FALSE AS is_handle
                FROM
                    tfs_warn_message
                WHERE
                    message_type IN ( 0, 1 )
                  AND del_flag = 0 UNION ALL
                SELECT
                    tunnel_id,
                    trigger_time AS create_time,
                    org_code,
                    org_name,
                    survey_water_mileage,
                    message_type,
                    message_content,
                    del_flag,
                    handing_method,
                    TRUE AS is_handle
                FROM
                    tfs_history_warn_message
                WHERE
                    message_type IN ( 0, 1 )
                  AND del_flag = 0 UNION ALL
                SELECT
                    tunnel_id,
                    create_time,
                    org_code,
                    org_name,
                    survey_water_mileage,
                    '2' message_type,
                    alarm_message message_content,
                    del_flag,
                    CASE

                        WHEN alarm_status = '1' THEN
                            '' ELSE CONCAT( alarm_user_name, '在', DATE_FORMAT( cancel_time, '%Y年%m月%d日' ), '执行消警操作' )
                        END AS handing_method,
                    CASE
                        alarm_status
                        WHEN '0' THEN
                            TRUE ELSE FALSE
                        END AS is_handle
                FROM
                    tfs_evacuate_warn
                WHERE
                    del_flag = 0
            ) AS c
                LEFT JOIN dcm_mine_info_mj dcm ON c.org_code = dcm.cmpi_dmpro
                LEFT JOIN tfs_tunnel_designs design ON c.tunnel_id = design.uuid
                AND design.del_flag = 0
        where c.org_code in (<foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()">
            and
            DATE_FORMAT (c.create_time,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (c.create_time,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (c.create_time,'%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="orgName != null and !orgName.isEmpty()">
            and
            c.org_name like concat ('%',#{orgName},'%')
        </if>
        <if test="type != null ">
            and
            c.message_type = #{type}
        </if>
        <if test="types != null and types.size > 0 ">
            and
            c.message_type in ( <foreach collection="types" separator="," item="messageType">#{messageType}</foreach> )
        </if>
        <if test="isHandle != null ">
            and
            c.is_handle = #{isHandle}
        </if>
        <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
            and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
        </if>
        <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
            and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
        </if>
        <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
            and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
        </if>
        ORDER BY
            c.create_time DESC
    </select>

    <!-- 同时查询警告表和历史警告表数据分页 -->
    <select id="getNum" resultType="long">
        SELECT
        count(*)
        FROM
        (
        SELECT
        create_time,
        org_code,
        org_name,
        survey_water_mileage,
        message_type,
        message_content,
        del_flag,
        FALSE AS is_handle
        FROM
        tfs_warn_message
        WHERE
        message_type IN ( 0, 1 )
        AND del_flag = 0 UNION
        SELECT
        trigger_time as create_time,
        org_code,
        org_name,
        survey_water_mileage,
        message_type,
        message_content,
        del_flag,
        TRUE AS is_handle
        FROM
        tfs_history_warn_message
        WHERE
        message_type IN ( 0, 1 )
        AND del_flag = 0
        union all
        select create_time,
        org_code,
        org_name,
        survey_water_mileage,
        '2' message_type,
        alarm_message message_content,
        del_flag,
        case  alarm_status when '0' then true
        else false end   AS is_handle
        from tfs_evacuate_warn where del_flag = 0
        ) AS c left join dcm_mine_info_mj dcm on c.org_code = dcm.cmpi_dmpro
        where
        del_flag = 0
        and org_code in (<foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()">
            and
            DATE_FORMAT (create_time,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (create_time,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (create_time,'%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="orgName != null and !orgName.isEmpty()">
            and
            org_name like concat ('%',#{orgName},'%')
        </if>
        <if test="type != null ">
            and
            message_type = #{type}
        </if>
        <if test="types != null and types.size > 0 ">
            and
            message_type in ( <foreach collection="types" separator="," item="messageType">#{messageType}</foreach> )
        </if>
        <if test="isHandle != null ">
            and
            is_handle = #{isHandle}
        </if>
        <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
            and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
        </if>
        <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
            and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
        </if>
        <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
            and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
        </if>
    </select>

    <!-- 查询警告异常个数 -->
    <select id="getWarnAbnormalNumber" resultMap="BaseItemInstance">
        SELECT
        total,
        excavation,
        water,
        evacWarn,
        noHandleNumber,
        total - noHandleNumber AS handleNumber
        FROM
        (
        SELECT
        count( 1 ) AS total,
        sum( CASE WHEN message_type = 0 THEN 1 ELSE 0 END ) AS excavation,
        sum( CASE WHEN message_type = 1 THEN 1 ELSE 0 END ) AS water,
        sum( CASE WHEN message_type = 2 THEN 1 ELSE 0 END ) AS evacWarn,
        sum( CASE WHEN handle = 0 THEN 1 ELSE 0 END ) noHandleNumber
        FROM
        (
        SELECT
        message_type,
        0 AS handle
        FROM
        tfs_warn_message
        WHERE
        message_type IN ( 0, 1 )
        and del_flag=0
        and org_code in (<foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
        <if test="startDate!=null and !startDate.isEmpty()">
           AND DATE_FORMAT ( create_time, '%Y-%m-%d' )>= #{startDate}
        </if>
        <if test="endDate != null and !endDate.isEmpty()" >
            AND DATE_FORMAT ( create_time, '%Y-%m-%d' )&lt;= #{endDate}
        </if>
        <if test="orgName != null and !orgName.isEmpty()">
            and
            org_name like concat ('%',#{orgName},'%')
        </if>
        UNION ALL
        SELECT
        message_type,
        1 AS handle
        FROM
        tfs_history_warn_message
        WHERE
        message_type IN ( 0, 1 )
        and del_flag=0
        and org_code in (<foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
        <if test="startDate!=null and !startDate.isEmpty()">
            AND DATE_FORMAT ( trigger_time, '%Y-%m-%d' )>= #{startDate}
        </if>
        <if test="endDate != null and !endDate.isEmpty()" >
            AND DATE_FORMAT ( trigger_time, '%Y-%m-%d' )&lt;= #{endDate}
        </if>
        <if test="orgName != null and !orgName.isEmpty()">
            and
            org_name like concat ('%',#{orgName},'%')
        </if>
        UNION ALL
        select
        '2' message_type,
        (case  alarm_status when '0' then 1
        else 0 end)   AS handle
        from tfs_evacuate_warn
        where del_flag = 0
        and org_code in (<foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
        <if test="startDate!=null and !startDate.isEmpty()">
            AND DATE_FORMAT ( create_time, '%Y-%m-%d' )>= #{startDate}
        </if>
        <if test="endDate != null and !endDate.isEmpty()" >
            AND DATE_FORMAT ( create_time, '%Y-%m-%d' )&lt;= #{endDate}
        </if>
        <if test="orgName != null and !orgName.isEmpty()">
            and
            org_name like concat ('%',#{orgName},'%')
        </if>
        ) t
        )t
    </select>

    <select id="getAnomalyInfo" resultMap="AnomalyAnalysisInfoDto">
        SELECT
            count( t.org_code ) warnNum,
            IFNULL( m.number, 0 ) geologicalAnomalyNum,
            count( t.org_code ) + IFNULL( m.number, 0 ) AS total,
            mine.org_code,
            mine.org_name
        FROM
            tfs_mine_info mine
                LEFT JOIN (
                SELECT
                    org_code,
                    org_name,
                    create_time,
                    FALSE AS is_handle
                FROM
                    tfs_warn_message
                WHERE
                    message_type IN ( 0, 1 )
                  AND del_flag = 0 UNION ALL
                SELECT
                    org_code,
                    org_name,
                    trigger_time AS create_time,
                    TRUE AS is_handle
                FROM
                    tfs_history_warn_message
                WHERE
                    message_type IN ( 0, 1 )
                  AND del_flag = 0 UNION ALL
                SELECT
                    org_code,
                    org_name,
                    create_time,
                    CASE
                        alarm_status
                        WHEN '0' THEN
                            TRUE ELSE FALSE
                        END AS is_handle
                FROM
                    tfs_evacuate_warn
                WHERE
                    del_flag = 0
            ) t ON t.org_code = mine.org_code
                LEFT JOIN ( SELECT org_code, count( DISTINCT drain_id ) number FROM tfs_hole_details_report WHERE del_flag = 0 AND abnormal_type >= 0 GROUP BY org_code ) m ON m.org_code = mine.org_code
        WHERE
            mine.del_flag = 0
            and mine.org_code IN (<foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
            <if test="isHandle != null ">
                and t.is_handle = #{isHandle}
            </if>
            <if test="time != null and time == 0">
                and  TO_DAYS(NOW( ))- TO_DAYS( t.create_time ) &lt;= 1
            </if>
            <if test="time != null and time == 1">
                and  TO_DAYS(NOW( ))- TO_DAYS( t.create_time ) > 1
            </if>
        GROUP BY
            mine.org_code
        ORDER BY
            mine.org_code
    </select>

    <select id="getAnomalyCount" resultType="long">
        SELECT
            COUNT( DISTINCT mine.org_code )
        FROM
            tfs_mine_info mine
                LEFT JOIN (
                SELECT
                    org_code,
                    create_time,
                    FALSE AS is_handle
                FROM
                    tfs_warn_message
                WHERE
                    message_type IN ( 0, 1 ) UNION ALL
                SELECT
                    org_code,
                    trigger_time AS create_time,
                    TRUE AS is_handle
                FROM
                    tfs_history_warn_message
                WHERE
                    message_type IN ( 0, 1 )
                  AND del_flag = 0 UNION ALL
                SELECT
                    org_code,
                    create_time,
                    CASE
                        alarm_status
                        WHEN '0' THEN
                            TRUE ELSE FALSE
                        END AS is_handle
                FROM
                    tfs_evacuate_warn
                WHERE
                    del_flag = 0
            ) t ON t.org_code = mine.org_code
        WHERE
        mine.org_code IN (<foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
        and mine.del_flag = 0
        <if test="isHandle != null ">
            and t.is_handle = #{isHandle}
        </if>
        <if test="time != null and time == 0">
            and  TO_DAYS(NOW( ))- TO_DAYS( t.create_time ) &lt;= 1
        </if>
        <if test="time != null and time == 1">
            and  TO_DAYS(NOW( ))- TO_DAYS( t.create_time ) > 1
        </if>
    </select>

    <select id="getAnomalyNum" resultMap="AnomalyAnalysisDto">
        SELECT
            sum( CASE WHEN is_handle IS TRUE THEN 1 ELSE 0 END ) handleNum,
            sum( CASE WHEN is_handle IS FALSE THEN 1 ELSE 0 END ) warnNum,
            0 AS noHandleNum
        FROM
            tfs_mine_info mine
                INNER JOIN (
                SELECT
                    org_code,
                    create_time,
                    FALSE AS is_handle
                FROM
                    tfs_warn_message
                WHERE
                    message_type IN ( 0, 1 )
                  AND del_flag = 0 UNION ALL
                SELECT
                    org_code,
                    trigger_time AS create_time,
                    TRUE AS is_handle
                FROM
                    tfs_history_warn_message
                WHERE
                    message_type IN ( 0, 1 )
                  AND del_flag = 0 UNION ALL
                SELECT
                    org_code,
                    create_time,
                    CASE
                        alarm_status
                        WHEN '0' THEN
                            TRUE ELSE FALSE
                        END AS is_handle
                FROM
                    tfs_evacuate_warn
                WHERE
                    del_flag = 0
            ) t ON t.org_code = mine.org_code
        WHERE
                mine.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
                and mine.del_flag = 0
    </select>

    <select id="getAnomalyNumber" resultMap="AnomalyAnalysisDto">
        SELECT
            sum( CASE WHEN is_handle IS TRUE THEN 1 ELSE 0 END ) handleNum,
            sum( CASE WHEN is_handle IS FALSE THEN 1 ELSE 0 END ) warnNum,
            0 AS noHandleNum
        FROM
            tfs_mine_info mine
                INNER JOIN (
                SELECT
                    org_code,
                    create_time,
                    FALSE AS is_handle
                FROM
                    tfs_warn_message
                WHERE
                    ( message_type = 1 OR message_type = 0 )
                  AND del_flag = 0 UNION ALL
                SELECT
                    org_code,
                    trigger_time AS create_time,
                    TRUE AS is_handle
                FROM
                    tfs_history_warn_message
                WHERE
                    ( message_type = 1 OR message_type = 0 )
                  AND del_flag = 0 UNION ALL
                SELECT
                    org_code,
                    create_time,
                    CASE
                        alarm_status
                        WHEN '0' THEN
                            TRUE ELSE FALSE
                        END AS is_handle
                FROM
                    tfs_evacuate_warn
                WHERE
                    del_flag = 0
            ) t ON t.org_code = mine.org_code
        WHERE
            mine.org_code = #{orgCode}
          AND mine.del_flag = 0
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()">
            and
            DATE_FORMAT (t.create_time,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (t.create_time,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (t.create_time,'%Y-%m-%d') &gt;= #{startDate}
        </if>
    </select>

    <!-- 根据机构编码查询数据 -->
    <select id="getByOrgCodesAndTypes" resultMap="HistoryWarnMessageInstance">
        select uuid,
               tunnel_id,
               survey_water_mileage,
               org_code,
               org_name,
               message_title,
               message_content,
               data_id,
               message_level,
               message_type,
               trigger_person_id,
               trigger_person_name,
               trigger_time,
               is_cancel,
               cancel_time,
               cancel_person_id,
               cancel_person_name,
               warn_message_id,
               create_time,
               message_rank,
               del_flag
        from tfs_history_warn_message
        where org_code in (
        <foreach collection="orgCodes" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        )
        and message_type in ( <foreach collection="messageTypes" item="messageType" separator=",">#{messageType}</foreach> )
        and del_flag = 0
        and message_content NOT REGEXP '当班探放水特工定位异常'
          <if test="cancelFlag">
              and is_cancel = #{cancelFlag}
          </if>
        order by create_time desc
    </select>

    <select id="getWarnList" resultMap="HistoryWarnMessageInstance">
        select t.*,
               IFNULL( de.work_name, face.work_name ) AS work_name,
                dcm.Mine_CityZone_NAME,
                dcm.ZONE_COUNTY_ID_NAME
        from (
        SELECT
        uuid,
        org_code,
        org_name,
        tunnel_id,
               data_id,
        survey_water_mileage,
        create_time as trigger_time,
        0 AS is_cancel,
        message_type,
        message_title,
        message_content,
        "" handing_method,
        message_detail,
        null as cancel_time,
        null as cancel_person_id,
        null as cancel_person_name,
        extend_fields,
        uuid as warn_message_id
        FROM
        tfs_warn_message
        WHERE
        del_flag = 0
        <if test="messageTypes != null and messageTypes.size > 0">AND message_type IN (
            <foreach collection="messageTypes" item="messageType" separator=",">#{messageType}</foreach>
            )
        </if>
          AND org_code in (
        <foreach collection="orgCodes" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        )
        and message_content NOT REGEXP '当班探放水特工定位异常'
          <if test="type != null and type ==0">and create_time > DATE_ADD( now(), INTERVAL -24 HOUR )
          </if>
        <if test="type != null and type ==1">AND DATE_FORMAT( create_time, '%Y-%m-%d' ) > DATE_FORMAT( DATE_ADD( now(), INTERVAL -
            7 DAY ), '%Y-%m-%d' )
        </if>
        <if test="startDate != null and startDate != ''">
            and DATE_FORMAT(create_time,'%Y-%m-%d')>=#{startDate}
        </if>
        <if test="endDate != null and endDate != '' ">
            and DATE_FORMAT(create_time,'%Y-%m-%d')&lt;=#{endDate}
        </if>
        <if test="orgName != null and orgName != '' ">
            and INSTR(org_name,#{orgName})
        </if>
        <if test="workName != null and workName != '' ">
            and INSTR(message_content,#{workName})
        </if>
        UNION ALL
        SELECT
            uuid,
            org_code,
            org_name,
            tunnel_id,

               data_id,
            survey_water_mileage,
            trigger_time ,
            is_cancel,
            message_type,
            message_title,
            message_content,
        handing_method,
        message_detail,
        cancel_time,
        cancel_person_id,
        cancel_person_name,
        extend_fields,
        warn_message_id
        FROM
            tfs_history_warn_message
        WHERE
            del_flag = 0
          and is_cancel = 1
        <if test="messageTypes != null and messageTypes.size > 0">
            AND message_type IN ( <foreach collection="messageTypes" item="messageType" separator=",">#{messageType}</foreach> )
        </if>
        AND org_code in (
        <foreach collection="orgCodes" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        )
        and message_content NOT REGEXP '当班探放水特工定位异常'
        <if test="type != null and type ==0">
            and trigger_time > DATE_ADD( now(), INTERVAL -24 HOUR )
        </if>
        <if test="type != null and type ==1">
            AND DATE_FORMAT( trigger_time, '%Y-%m-%d' ) > DATE_FORMAT(
            DATE_ADD( now(), INTERVAL - 7 DAY ),
            '%Y-%m-%d'
            )
        </if>
        <if test="startDate != null and startDate != ''">
            and DATE_FORMAT(trigger_time,'%Y-%m-%d')>=#{startDate}
        </if>
        <if test="endDate != null and endDate != '' ">
            and DATE_FORMAT(trigger_time,'%Y-%m-%d')&lt;=#{endDate}
        </if>
        <if test="orgName != null and orgName != '' ">
            and INSTR(org_name,#{orgName})
        </if>
        <if test="workName != null and workName != '' ">
            and INSTR(message_content,#{workName})
        </if>
    ) t left join tfs_tunnel_designs de on de.uuid = t.tunnel_id and de.del_flag = 0
        AND de.STATUS = 2
        LEFT JOIN tfs_work_face face ON face.uuid = t.tunnel_id
        LEFT JOIN dcm_mine_info_mj dcm ON dcm.cmpi_dmpro = t.org_code
        <where>
            <if test="handleType != null">
                and is_cancel = #{handleType}
            </if>
            <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
                and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
            </if>
            <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
                and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
            </if>
            <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
                and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
            </if>
        </where>
     order by trigger_time desc
    </select>











    <select id="getWarnCountGroupByOrgCode" resultType="jylink.cpds.serviceModel.dto.WarnMessageCountDto">

     select org_code, is_cancel, count(1) as all_count from(
        select t.*,
        IFNULL( de.work_name, face.work_name ) AS work_name,
        dcm.Mine_CityZone_NAME,
        dcm.ZONE_COUNTY_ID_NAME
        from (
        SELECT
        uuid,
        org_code,
        org_name,
        tunnel_id,
        data_id,
        survey_water_mileage,
        create_time as trigger_time,
        0 AS is_cancel,
        message_type,
        message_title,
        message_content,
        "" handing_method,
        message_detail,
        null as cancel_time,
        null as cancel_person_id,
        null as cancel_person_name,
        extend_fields,
        uuid as warn_message_id
        FROM
        tfs_warn_message
        WHERE
        del_flag = 0
        and message_content NOT REGEXP '当班探放水特工定位异常'
        <if test="messageTypes != null and messageTypes.size > 0">AND message_type IN (
            <foreach collection="messageTypes" item="messageType" separator=",">#{messageType}</foreach>
            )
        </if>
        AND org_code in (
        <foreach collection="orgCodes" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        )
        <if test="type != null and type ==0">and create_time > DATE_ADD( now(), INTERVAL -24 HOUR )
        </if>
        <if test="type != null and type ==1">AND DATE_FORMAT( create_time, '%Y-%m-%d' ) > DATE_FORMAT( DATE_ADD( now(),
            INTERVAL -
            7 DAY ), '%Y-%m-%d' )
        </if>
        <if test="startDate != null and startDate != ''">
            and DATE_FORMAT(create_time,'%Y-%m-%d')>=#{startDate}
        </if>
        <if test="endDate != null and endDate != '' ">
            and DATE_FORMAT(create_time,'%Y-%m-%d')&lt;=#{endDate}
        </if>
        <if test="orgName != null and orgName != '' ">
            and INSTR(org_name,#{orgName})
        </if>
        <if test="workName != null and workName != '' ">
            and INSTR(message_content,#{workName})
        </if>
        UNION ALL
        SELECT
        uuid,
        org_code,
        org_name,
        tunnel_id,

        data_id,
        survey_water_mileage,
        trigger_time ,
        is_cancel,
        message_type,
        message_title,
        message_content,
        handing_method,
        message_detail,
        cancel_time,
        cancel_person_id,
        cancel_person_name,
        extend_fields,
        warn_message_id
        FROM
        tfs_history_warn_message
        WHERE
        del_flag = 0
        and message_content NOT REGEXP '当班探放水特工定位异常'
        and is_cancel = 1
        <if test="messageTypes != null and messageTypes.size > 0">
            AND message_type IN ( <foreach collection="messageTypes" item="messageType" separator=",">#{messageType}</foreach> )
        </if>
        AND org_code in (
        <foreach collection="orgCodes" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        )
        <if test="type != null and type ==0">
            and trigger_time > DATE_ADD( now(), INTERVAL -24 HOUR )
        </if>
        <if test="type != null and type ==1">
            AND DATE_FORMAT( trigger_time, '%Y-%m-%d' ) > DATE_FORMAT(
            DATE_ADD( now(), INTERVAL - 7 DAY ),
            '%Y-%m-%d'
            )
        </if>
        <if test="startDate != null and startDate != ''">
            and DATE_FORMAT(trigger_time,'%Y-%m-%d')>=#{startDate}
        </if>
        <if test="endDate != null and endDate != '' ">
            and DATE_FORMAT(trigger_time,'%Y-%m-%d')&lt;=#{endDate}
        </if>
        <if test="orgName != null and orgName != '' ">
            and INSTR(org_name,#{orgName})
        </if>
        <if test="workName != null and workName != '' ">
            and INSTR(message_content,#{workName})
        </if>
        ) t left join tfs_tunnel_designs de on de.uuid = t.tunnel_id and de.del_flag = 0
        AND de.STATUS = 2
        LEFT JOIN tfs_work_face face ON face.uuid = t.tunnel_id
        LEFT JOIN dcm_mine_info_mj dcm ON dcm.cmpi_dmpro = t.org_code
        <where>
            <if test="handleType != null">
                and is_cancel = #{handleType}
            </if>
            <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
                and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
            </if>
            <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
                and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
            </if>
            <if test="mineProvzoneCode != null and mineProvzoneCode != '' "></if>
        </where>        and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
     ) as tdfd group by org_code, is_cancel
    </select>






    <select id="getWarnListGroupByOrgCode" resultMap="HistoryWarnMessageInstance">
        select t.*,
        IFNULL( de.work_name, face.work_name ) AS work_name,
        dcm.Mine_CityZone_NAME,
        dcm.ZONE_COUNTY_ID_NAME
        from (
        SELECT
        uuid,
        org_code,
        org_name,
        tunnel_id,
        data_id,
        survey_water_mileage,
        create_time as trigger_time,
        0 AS is_cancel,
        message_type,
        message_title,
        message_content,
        "" handing_method,
        message_detail,
        null as cancel_time,
        null as cancel_person_id,
        null as cancel_person_name,
        extend_fields,
        uuid as warn_message_id
        FROM
        tfs_warn_message
        WHERE
        del_flag = 0
        and message_content NOT REGEXP '当班探放水特工定位异常'
        <if test="messageTypes != null and messageTypes.size > 0">AND message_type IN (
            <foreach collection="messageTypes" item="messageType" separator=",">#{messageType}</foreach>
            )
        </if>
        AND org_code in (
        <foreach collection="orgCodes" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        )
        <if test="type != null and type ==0">and create_time > DATE_ADD( now(), INTERVAL -24 HOUR )
        </if>
        <if test="type != null and type ==1">AND DATE_FORMAT( create_time, '%Y-%m-%d' ) > DATE_FORMAT( DATE_ADD( now(), INTERVAL -
            7 DAY ), '%Y-%m-%d' )
        </if>
        <if test="startDate != null and startDate != ''">
            and DATE_FORMAT(create_time,'%Y-%m-%d')>=#{startDate}
        </if>
        <if test="endDate != null and endDate != '' ">
            and DATE_FORMAT(create_time,'%Y-%m-%d')&lt;=#{endDate}
        </if>
        <if test="orgName != null and orgName != '' ">
            and INSTR(org_name,#{orgName})
        </if>
        UNION ALL
        SELECT
        uuid,
        org_code,
        org_name,
        tunnel_id,

        data_id,
        survey_water_mileage,
        trigger_time ,
        is_cancel,
        message_type,
        message_title,
        message_content,
        handing_method,
        message_detail,
        cancel_time,
        cancel_person_id,
        cancel_person_name,
        extend_fields,
        warn_message_id
        FROM
        tfs_history_warn_message
        WHERE
        del_flag = 0
        and is_cancel = 1
        and message_content NOT REGEXP '当班探放水特工定位异常'
        <if test="messageTypes != null and messageTypes.size > 0">
            AND message_type IN ( <foreach collection="messageTypes" item="messageType" separator=",">#{messageType}</foreach> )
        </if>
        AND org_code in (
        <foreach collection="orgCodes" separator="," item="orgCode">
            #{orgCode}
        </foreach>
        )
        <if test="type != null and type ==0">
            and trigger_time > DATE_ADD( now(), INTERVAL -24 HOUR )
        </if>
        <if test="type != null and type ==1">
            AND DATE_FORMAT( trigger_time, '%Y-%m-%d' ) > DATE_FORMAT(
            DATE_ADD( now(), INTERVAL - 7 DAY ),
            '%Y-%m-%d'
            )
        </if>
        <if test="startDate != null and startDate != ''">
            and DATE_FORMAT(trigger_time,'%Y-%m-%d')>=#{startDate}
        </if>
        <if test="endDate != null and endDate != '' ">
            and DATE_FORMAT(trigger_time,'%Y-%m-%d')&lt;=#{endDate}
        </if>
        <if test="orgName != null and orgName != '' ">
            and INSTR(org_name,#{orgName})
        </if>
        ) t left join tfs_tunnel_designs de on de.uuid = t.tunnel_id and de.del_flag = 0
        AND de.STATUS = 2
        LEFT JOIN tfs_work_face face ON face.uuid = t.tunnel_id
        LEFT JOIN dcm_mine_info_mj dcm ON dcm.cmpi_dmpro = t.org_code
        <where>
            <if test="handleType != null">
                and is_cancel = #{handleType}
            </if>
            <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
                and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
            </if>
            <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
                and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
            </if>
            <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
                and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
            </if>
            <if test="workName != null and workName != '' ">
                and tunnel_id = (select uuid from tfs_tunnel_designs where work_name=#{workName} and org_code=#{selectOrgCode} and del_flag=0)
            </if>
        </where>
        order by trigger_time desc
        limit #{currentPage},#{currentPageSize}
    </select>

























    <select id="getWarnCount" resultType="jylink.cpds.serviceModel.dto.WarnAbnormalOrgListDto">
        SELECT
            COUNT( DISTINCT t.org_code ) mineNum,
            SUM( CASE WHEN is_cancel = 0 THEN 1 ELSE 0 END ) warnNum,
            SUM( CASE WHEN is_cancel = 1 THEN 1 ELSE 0 END ) handleNum,
            COUNT(*) total
        FROM
            (
                SELECT
                    org_code,
                    tunnel_id,
                    0 AS is_cancel,
                    message_type
                FROM
                    tfs_warn_message
                WHERE
                    del_flag = 0
                <if test="messageTypes != null and messageTypes.size > 0">AND message_type IN (
                    <foreach collection="messageTypes" item="messageType" separator=",">#{messageType}</foreach>
                    )
                </if>
                AND org_code in (
                <foreach collection="orgCodes" separator="," item="orgCode">
                    #{orgCode}
                </foreach>
                )
                and  message_content NOT REGEXP '当班探放水特工定位异常'
                <if test="type != null and type ==0">
                  and create_time > DATE_ADD( now(), INTERVAL -24 HOUR )
                </if>
                <if test="type != null and type ==1">
                  AND DATE_FORMAT( create_time, '%Y-%m-%d' ) > DATE_FORMAT( DATE_ADD( now(), INTERVAL -
                    7 DAY ), '%Y-%m-%d' )
                </if>
                <if test="startDate != null and startDate != ''">
                    and DATE_FORMAT(create_time,'%Y-%m-%d')>=#{startDate}
                </if>
                <if test="endDate != null and endDate != '' ">
                    and DATE_FORMAT(create_time,'%Y-%m-%d')&lt;=#{endDate}
                </if>
                <if test="orgName != null and orgName != '' ">
                    and INSTR(org_name,#{orgName})
                </if>
                <if test="workName != null and workName != '' ">
                    and INSTR(message_content,#{workName})
                </if>
                UNION ALL
                SELECT
                    org_code,
                    tunnel_id,
                    is_cancel,
                    message_type
                FROM
                    tfs_history_warn_message
                WHERE
                    del_flag = 0
                  AND is_cancel = 1
                  and message_content NOT REGEXP '当班探放水特工定位异常'
                    <if test="messageTypes != null and messageTypes.size > 0">
                        AND message_type IN ( <foreach collection="messageTypes" item="messageType" separator=",">#{messageType}</foreach> )
                    </if>
                    AND org_code in (
                    <foreach collection="orgCodes" separator="," item="orgCode">
                        #{orgCode}
                    </foreach>
                    )
                    <if test="type != null and type ==0">
                        and trigger_time > DATE_ADD( now(), INTERVAL -24 HOUR )
                    </if>
                    <if test="type != null and type ==1">
                        AND DATE_FORMAT( trigger_time, '%Y-%m-%d' ) > DATE_FORMAT(
                        DATE_ADD( now(), INTERVAL - 7 DAY ),
                        '%Y-%m-%d'
                        )
                    </if>
                    <if test="startDate != null and startDate != ''">
                        and DATE_FORMAT(trigger_time,'%Y-%m-%d')>=#{startDate}
                    </if>
                    <if test="endDate != null and endDate != '' ">
                        and DATE_FORMAT(trigger_time,'%Y-%m-%d')&lt;=#{endDate}
                    </if>
                    <if test="orgName != null and orgName != '' ">
                        and INSTR(org_name,#{orgName})
                    </if>
                    <if test="workName != null and workName != '' ">
                        and INSTR(message_content,#{workName})
                    </if>
            ) t
                LEFT JOIN tfs_tunnel_designs de ON de.uuid = t.tunnel_id
                AND de.STATUS = 2
                AND de.detection_status = 0
                LEFT JOIN dcm_mine_info_mj dcm ON dcm.cmpi_dmpro = t.org_code and de.del_flag = 0

            <where>
                <if test="handleType != null">
                    and is_cancel = #{handleType}
                </if>
                <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
                    and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
                </if>
                <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
                    and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
                </if>
                <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
                    and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
                </if>
            </where>
    </select>

    <select id="getBydataIdList" resultMap="HistoryWarnMessageInstance">
        select
        uuid,
        tunnel_id,
        survey_water_mileage,
        org_code,
        org_name,
        message_title,
        message_content,
        data_id,
        message_level,
        message_type,
        trigger_person_id,
        trigger_person_name,
        trigger_time,
        is_cancel,
        cancel_time,
        cancel_person_id,
        cancel_person_name,
        warn_message_id,
        create_time,
        message_rank,
        handing_method,
        message_detail,
        extend_fields,
        del_flag
        from tfs_history_warn_message
        where warn_message_id IN ( <foreach collection="dataIdList" item="orgCode" separator=",">#{orgCode}</foreach>)
    </select>

    <select id="getListByDrainId" resultMap="HistoryWarnMessageInstance">
        SELECT
            warn.uuid,
            warn.org_code,
            warn.org_name,
            warn.tunnel_id,
            warn.data_id,
            warn.survey_water_mileage,
            warn.trigger_time,
            warn.is_cancel,
            warn.message_type,
            warn.message_title,
            warn.message_content,
            warn.handing_method,
            warn.message_detail,
            warn.extend_fields,
            warn.warn_message_id
        FROM
            tfs_history_warn_message warn
        LEFT JOIN tfs_drain_accounts dra ON dra.check_plan_id = warn.data_id
        WHERE
            warn.del_flag = 0
          AND dra.uuid IN ( <foreach collection="drainIds" item="drainId" separator=",">#{drainId}</foreach> )
        UNION ALL
        (
        SELECT
            uuid,
            org_code,
            org_name,
            tunnel_id,
            data_id,
            survey_water_mileage,
            trigger_time,
            is_cancel,
            message_type,
            message_title,
            message_content,
            handing_method,
            message_detail,
            extend_fields,
            warn_message_id
        FROM
            tfs_history_warn_message
        WHERE
            del_flag = 0
            AND data_id IN ( <foreach collection="drainIds" item="drainId" separator=",">#{drainId}</foreach> )
        )
        ORDER BY
            trigger_time DESC
    </select>

</mapper>
