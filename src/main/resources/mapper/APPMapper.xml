<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IAPPDao">
    <!--结果映射 -->
    <resultMap type="jylink.cpds.serviceModel.dto.APPAcceptanceDto" id="AcceptancesPendingInstance">
        <result property="workName" column="work_name"/>
        <result property="surveyWaterMileage" column="survey_water_mileage"/>
        <result property="surveyWaterDate" column="survey_water_date"/>
        <result property="holeNumber" column="hole_no"/>
        <result property="userName" column="user_name"/>
        <result property="initiationTime" column="approved_time"/>
        <result property="workOrgName" column="work_org_name"/>
        <result property="approveId" column="uuid"/>
    </resultMap>

    <!--结果映射 -->
    <resultMap type="jylink.cpds.serviceModel.dto.APPAnalysisPendingDto" id="AnalysisPendingInstance">
        <result property="moduleId" column="uuid"/>
        <result property="id" column="drain_id"/>
        <result property="workName" column="work_name"/>
        <result property="surveyWaterMileage" column="survey_water_mileage"/>
        <result property="surveyWaterDate" column="survey_water_date"/>
        <result property="moduleName" column="module_name"/>
        <result property="workOrgName" column="work_org_name"/>
    </resultMap>


    <!--获取待审批列表-->
    <select id="getAcceptancesPending" resultMap="AcceptancesPendingInstance">
        select a.uuid,c.work_name,c.survey_water_mileage,c.survey_water_date,group_concat(d.hole_no ORDER BY d.hole_no) as hole_no
        from tfs_acceptance_approves a
        inner join tfs_drain_accounts c on a.drain_id=c.uuid
        left outer join tfs_acceptance_checks d on d.approve_id=a.uuid
        where a.org_code=#{orgCode}
        and a.status=1
        and a.uuid in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
        <if test="endTime != null ">
            and DATE_FORMAT(c.survey_water_date,'%Y-%m-%d')&lt;=#{endTime}
        </if>
        <if test="startTime != null">
            and DATE_FORMAT(c.survey_water_date,'%Y-%m-%d')>=#{startTime}
        </if>
        <if test="workName != null and !workName.isEmpty()">
            and c.work_name=#{workName}
        </if>
        and a.del_flag=0
        GROUP BY  a.uuid,c.work_name,c.survey_water_mileage,c.survey_water_date
    </select>

    <!-- 查询数据总条数 -->
    <select id="getCount" resultType="long">
        select count(1)
        from tfs_acceptance_approves a
        inner join tfs_drain_accounts c on a.drain_id=c.uuid
        where a.org_code=#{orgCode}
        and a.uuid in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
        and a.status=1
        <if test="endTime != null ">
            and c.survey_water_date &lt;=#{endTime}
        </if>
        <if test="startTime != null ">
            and c.survey_water_date>=#{startTime}
        </if>
        <if test="workName != null and !workName.isEmpty()">
            and c.work_name=#{workName}
        </if>
        and a.del_flag=0
    </select>
    
    <select id="getAnalysisPending" resultMap="AnalysisPendingInstance">
        select t.uuid,t.drain_id,c.work_name,c.survey_water_mileage,c.survey_water_date,d.work_org_name,t.module_name
        from (
        select a.drain_id,a.uuid,'transferDetection' as module_name
        from tfs_transfer_detections a
        where a.del_flag=0 and a.status=1
        union all
        select a.drain_id,a.uuid,'allowableNotice' as module_name
        from tfs_allowable_notices a
        where a.del_flag=0 and a.status=1
        ) t  inner join tfs_drain_accounts c on t.drain_id=c.uuid
        inner join tfs_check_plans d on c.check_plan_id=d.uuid
        where c.org_code=#{orgCode}
        and t.uuid in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
        <if test="endTime != null ">
            and DATE_FORMAT(c.survey_water_date,'%Y-%m-%d')&lt;=#{endTime}
        </if>
        <if test="startTime != null ">
            and DATE_FORMAT(c.survey_water_date,'%Y-%m-%d')>=#{startTime}
        </if>
        <if test="workName != null and !workName.isEmpty()">
            and c.work_name=#{workName}
        </if>
        and c.del_flag=0
        order by c.survey_water_date
    </select>

    <!-- 查询数据总条数 -->
    <select id="getAnalysisCount" resultType="long">
        select count(1)
        from (
        select a.drain_id,a.uuid,'transferDetection' as module_name
        from tfs_transfer_detections a
        where a.del_flag=0 and a.status=1
        union all
        select a.drain_id,a.uuid,'allowableNotice' as module_name
        from tfs_allowable_notices a
        where a.del_flag=0 and a.status=1
        ) t  inner join tfs_drain_accounts c on t.drain_id=c.uuid
        inner join tfs_check_plans d on c.check_plan_id=d.uuid
        where c.org_code=#{orgCode}
        and t.uuid in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
        <if test="endTime != null ">
            and DATE_FORMAT(c.survey_water_date,'%Y-%m-%d')&lt;=#{endTime}
        </if>
        <if test="startTime != null ">
            and DATE_FORMAT(c.survey_water_date,'%Y-%m-%d')>=#{startTime}
        </if>
        <if test="workName != null and !workName.isEmpty()">
            and c.work_name=#{workName}
        </if>
        and c.del_flag=0
    </select>
</mapper>
