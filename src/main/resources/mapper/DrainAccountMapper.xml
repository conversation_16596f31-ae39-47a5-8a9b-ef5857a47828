<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IDrainAccountDao">
    <resultMap type="jylink.cpds.domain.DrainAccount" id="DrainAccountInstance">
        <id property="id" column="uuid"></id>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tunnelId" column="tunnel_id"/>
        <result property="checkPlanId" column="check_plan_id"/>
        <result property="workName" column="work_name"/>
        <result property="workCode" column="work_code"/>
        <result property="surveyWaterMileage" column="survey_water_mileage"/>
        <result property="surveyWaterDate" column="survey_water_date"/>
        <result property="isAddStopNotice" column="is_add_stop_notice"/>
        <result property="isAddSurveyNotice" column="is_add_survey_notice"/>
        <result property="isAddAcceptanceCheck" column="is_add_acceptance_check"/>
        <result property="isAddTransferDetection" column="is_add_transfer_detection"/>
        <result property="isAddAllowableDriving" column="is_add_allowable_driving"/>
        <result property="isAddSummaryNotice" column="is_add_summary_notice"/>
        <result property="isViewed" column="is_viewed"/>
        <result property="status" column="status"/>
        <result property="uploadTime" column="upload_time"/>
        <result property="userId" column="user_id"/>
        <result property="isAccomplish" column="is_accomplish"/>
        <result property="coalAngle" column="coal_angle"/>
        <result property="allowableNoticeContent" column="allowable_notice_content"/>
        <result property="cameraIp" column="camera_ip" />
    </resultMap>

    <!--结果映射 -->
    <resultMap type="jylink.cpds.serviceModel.BaseItem" id="BaseItemInstance">
        <result property="key" column="abnormal_type"/>
        <result property="value1" column="abnormal_name"/>
        <result property="total" column="total" />
    </resultMap>

    <!--根据id进行查询-->
    <select id="getById" resultMap="DrainAccountInstance">
        select a.uuid,
        a.org_code,
        a.org_name,
        a.create_time,
        a.del_flag,
        a.tunnel_id,
        a.check_plan_id,
        a.work_name,
        a.work_code,
        a.survey_water_mileage,
        a.survey_water_date,
        a.is_add_stop_notice,
        a.is_add_survey_notice,
        a.coal_angle,
        a.diy_column,
        a.camera_ip,
        case when (SELECT COUNT(1) FROM tfs_acceptance_approves d WHERE d.drain_id = a.uuid AND d.del_flag = 0 AND d.`status`=2)>0 then 1 else 0 end is_add_acceptance_check,
        case when b.uuid is null then 0 else 1 end is_add_transfer_detection,
        case when c.uuid is null then 0 else 1 end is_add_allowable_driving,
        a.status,
        a.is_viewed,
        a.upload_time,
        a.user_id,
        a.is_accomplish
        from tfs_drain_accounts a
        left outer join tfs_transfer_detections b on a.uuid= b.drain_id and b.`status`=2
        left outer join tfs_allowable_notices c on a.uuid=c.drain_id and c.`status`=2
        where
        a.uuid=#{id}
        and  a.del_flag=0
        order by a.survey_water_date desc,a.survey_water_mileage desc
    </select>

    <!--根据checkPlanId进行查询-->
    <select id="getBycheckPlanId" resultMap="DrainAccountInstance">
          select
          uuid,
          org_code,
          org_name,
          create_time,
          del_flag,
          tunnel_id,
          check_plan_id,
          work_name,
          work_code,
          survey_water_mileage,
          survey_water_date,
          is_add_stop_notice,
          is_add_survey_notice,
          is_add_acceptance_check,
          is_add_transfer_detection,
          is_add_allowable_driving,
          is_add_summary_notice,
          status,
          is_viewed,
          upload_time,
          user_id,
          camera_ip,
          is_accomplish
          from tfs_drain_accounts
          where check_plan_id = #{checkPlanId}  and del_flag = 0 and  org_code=#{orgCode}
    </select>

    <!--根据id集合进行查询-->
    <select id="getByListId" resultMap="DrainAccountInstance">
          select
          uuid,
          org_code,
          org_name,
          create_time,
          del_flag,
          tunnel_id,
          check_plan_id,
          work_name,
          work_code,
          survey_water_mileage,
          survey_water_date,
          is_add_stop_notice,
          is_add_survey_notice,
          is_add_acceptance_check,
          is_add_transfer_detection,
          is_add_allowable_driving,
          is_add_summary_notice,
          status,
          is_viewed,
          upload_time,
          user_id,
          diy_column,
          camera_ip,
          is_accomplish
          from tfs_drain_accounts
          where
          uuid in ( <foreach collection="ids" item="id" separator=",">#{id}</foreach> )
          and del_flag = 0 and  org_code=#{orgCode}
    </select>

    <!--根据id集合进行查询-->
    <select id="getByIds" resultMap="DrainAccountInstance">
        select
        uuid,
        org_code,
        org_name,
        create_time,
        del_flag,
        tunnel_id,
        check_plan_id,
        work_name,
        work_code,
        survey_water_mileage,
        survey_water_date,
        is_add_stop_notice,
        is_add_survey_notice,
        is_add_acceptance_check,
        is_add_transfer_detection,
        is_add_allowable_driving,
        is_add_summary_notice,
        status,
        is_viewed,
        upload_time,
        user_id,
        camera_ip,
        is_accomplish
        from tfs_drain_accounts
        where
        uuid in ( <foreach collection="ids" item="id" separator=",">#{id}</foreach> )
        and del_flag = 0
    </select>

    <!--根据机构编号进行查询-->
    <select id="getOrgCode" resultMap="DrainAccountInstance">
          select
          uuid,
          org_code,
          org_name,
          create_time,
          del_flag,
          tunnel_id,
          check_plan_id,
          work_name,
          work_code,
          survey_water_mileage,
          survey_water_date,
          is_add_stop_notice,
          is_add_survey_notice,
          is_add_acceptance_check,
          is_add_transfer_detection,
          is_add_allowable_driving,
          is_add_summary_notice,
          status,
          is_viewed,
          upload_time,
          user_id,
          camera_ip,
          is_accomplish
          from tfs_drain_accounts
          where org_code=#{orgCode} and del_flag=0
          order by work_name
    </select>

    <!--根据工作面名称查询-->
    <select id="getByWorkName" resultMap="DrainAccountInstance">
          select uuid,
          org_code,
          org_name,
          create_time,
          del_flag,
          tunnel_id,
          check_plan_id,
          work_name,
          work_code,
          survey_water_mileage,
          survey_water_date,
          is_add_stop_notice,
          is_add_survey_notice,
          is_add_acceptance_check,
          is_add_transfer_detection,
          is_add_allowable_driving,
          is_add_summary_notice,
          status,
          is_viewed,
          upload_time,
          user_id,
                 camera_ip,
          is_accomplish
          from tfs_drain_accounts
          where work_name = #{workName} and org_code = #{orgCode} and del_flag = 0
          order by survey_water_date desc
    </select>

    <!--根据探水时间查询-->
    <select id="getBySurveyWaterDate" resultMap="DrainAccountInstance">
          select uuid,
          org_code,
          org_name,
          create_time,
          del_flag,
          tunnel_id,
          check_plan_id,
          work_name,
          work_code,
          survey_water_mileage,
          survey_water_date,
          is_add_stop_notice,
          is_add_survey_notice,
          is_add_acceptance_check,
          is_add_transfer_detection,
          is_add_allowable_driving,
          is_add_summary_notice,
          status,
          is_viewed,
          upload_time,
          user_id,
                 camera_ip,
          is_accomplish
          from tfs_drain_accounts
          where  DATE_FORMAT(survey_water_date,'%Y') =  #{surveyWaterDate}  and org_code = #{orgCode} and del_flag = 0
          order by survey_water_date
    </select>

    <!--根据工作面名称和探水时间查询-->
    <select id="getByWorkAndDate" resultMap="DrainAccountInstance">
        select  a.uuid,
        a.org_code,
        a.org_name,
        a.create_time,
        a.del_flag,
        a.tunnel_id,
        a.check_plan_id,
        a.work_name,
        a.work_code,
        a.survey_water_mileage,
        a.survey_water_date,
        a.is_add_stop_notice,
        a.is_add_survey_notice,
        case when (SELECT COUNT(1) FROM tfs_acceptance_approves d WHERE d.drain_id = a.uuid AND d.del_flag = 0 AND d.`status`=2)>0 then 1 else 0 end is_add_acceptance_check,
        case when b.uuid is null then 0 else 1 end is_add_transfer_detection,
        case when c.uuid is null then 0 else 1 end is_add_allowable_driving,
        a.status,
        a.is_viewed,
        a.upload_time,
        a.user_id,
        a.camera_ip,
        a.is_accomplish
        from tfs_drain_accounts a
        left outer join tfs_transfer_detections b on a.uuid= b.drain_id and b.`status`=2
        left outer join tfs_allowable_notices c on a.uuid=c.drain_id and c.`status`=2
        where
        a.org_code = #{orgCode}
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()" >
            and
            DATE_FORMAT (a.survey_water_date,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (a.survey_water_date,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (a.survey_water_date,'%Y-%m-%d') >= #{startDate}
        </if>
        and a.del_flag = 0
        <if test="workName != null and !workName.isEmpty()">
            and
            a.work_name = #{workName}
        </if>
        <if test="status != null">
            and
            a.status = #{status}
        </if>

        order by a.survey_water_date desc,a.survey_water_mileage desc
    </select>

    <!--根据工作面名称和探水里程查询-->
    <select id="getByWorkNameAndSurveyWaterMileage" resultMap="DrainAccountInstance">
        select uuid,
        org_code,
        org_name,
        create_time,
        del_flag,
        tunnel_id,
        check_plan_id,
        work_name,
        work_code,
        survey_water_mileage,
        survey_water_date,
        is_add_stop_notice,
        is_add_survey_notice,
        is_add_acceptance_check,
        is_add_transfer_detection,
        is_add_allowable_driving,
        is_add_summary_notice,
        status,
        is_viewed,
        upload_time,
        user_id,
        camera_ip,
        is_accomplish
        from tfs_drain_accounts
        where  org_code = #{orgCode} and del_flag = 0
        <if test="workName != null and !workName.isEmpty()">
            and
            work_name = #{workName}
        </if>
        and
        survey_water_mileage = #{surveyWaterMileage}
    </select>

    <!--根据探水时间查询探水里程-->
    <select id="getSurveyWaterMileageTree" resultMap="DrainAccountInstance">
        select
        uuid,
        org_code,
        org_name,
        create_time,
        del_flag,
        tunnel_id,
        check_plan_id,
        work_name,
        work_code,
        survey_water_mileage,
        survey_water_date,
        is_add_stop_notice,
        is_add_survey_notice,
        is_add_acceptance_check,
        is_add_transfer_detection,
        is_add_allowable_driving,
        is_add_summary_notice,
        status,
        is_viewed,
        upload_time,
        user_id,
        camera_ip,
        is_accomplish
        from
        tfs_drain_accounts
        where
        org_code = #{orgCode}
        and del_flag = 0
        and is_add_acceptance_check = TRUE
        order by survey_water_date desc,tunnel_id
    </select>

    <!--获取探水里程-->
    <select id="getByWorkForMileage" resultMap="DrainAccountInstance">
        select
         uuid,
        org_code,
        org_name,
        create_time,
        del_flag,
        tunnel_id,
        check_plan_id,
        work_name,
        work_code,
        survey_water_mileage,
        survey_water_date,
        is_add_stop_notice,
        is_add_survey_notice,
        is_add_acceptance_check,
        is_add_transfer_detection,
        is_add_allowable_driving,
        is_add_summary_notice,
        status,
        is_viewed,
        upload_time,
        user_id,
         camera_ip,
        is_accomplish
        from tfs_drain_accounts
        where work_name = #{workName} and org_code = #{orgCode} and del_flag = 0
        and is_add_transfer_detection = TRUE
        and is_add_stop_notice = TRUE
        and is_add_survey_notice = TRUE
        and is_add_acceptance_check = TRUE
        and is_add_allowable_driving = TRUE
        order by survey_water_date
    </select>
    <!--添加应用实例-->
    <insert id="add">
        insert into tfs_drain_accounts (
        uuid,
        org_code,
        org_name,
        create_time,
        del_flag,
        tunnel_id,
        check_plan_id,
        work_name,
        work_code,
        survey_water_mileage,
        survey_water_date,
        is_add_stop_notice,
        is_add_survey_notice,
        is_add_acceptance_check,
        is_add_transfer_detection,
        is_add_allowable_driving,
        is_add_summary_notice,
        status,
        is_viewed,
        is_accomplish
        )
        values (
        #{instance.id},
        #{instance.orgCode},
        #{instance.orgName},
        #{instance.createTime},
        #{instance.delFlag},
        #{instance.tunnelId},
        #{instance.checkPlanId},
        #{instance.workName},
        #{instance.workCode},
        #{instance.surveyWaterMileage},
        #{instance.surveyWaterDate},
        #{instance.isAddStopNotice},
        #{instance.isAddSurveyNotice},
        #{instance.isAddAcceptanceCheck},
        #{instance.isAddTransferDetection },
        #{instance.isAddAllowableDriving },
        #{instance.isAddSummaryNotice },
        #{instance.status},
        #{instance.isViewed},
        #{instance.isAccomplish}
        )
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update tfs_drain_accounts
        set
        work_name = #{instance.workName},
        work_code = #{instance.workCode},
        tunnel_id = #{instance.tunnelId},
        check_plan_id = #{instance.checkPlanId},
        survey_water_mileage = #{instance.surveyWaterMileage},
        survey_water_date = #{instance.surveyWaterDate}
        where
        uuid = #{instance.id} and del_flag = 0
    </update>

    <!-- 更新子表对应数据 -->
    <update id="updateSublist">
        update tfs_drain_accounts
        set
        <!--<if test="isAddStopNotice ==true ">-->
            <!--is_add_stop_notice= #{isAddStopNotice},-->
        <!--</if>-->
        <!--<if test="isAddSurveyNotice  ==true">-->
            <!--is_add_survey_notice= #{isAddSurveyNotice},-->
        <!--</if>-->
        <if test="isAddAcceptanceCheck  ==true">
            is_add_acceptance_check= #{isAddAcceptanceCheck},
        </if>
        <if test="isAddTransferDetection ==true">
            is_add_transfer_detection= #{isAddTransferDetection},
        </if>
        <if test="isAddAllowableDriving  ==true">
            is_add_allowable_driving= #{isAddAllowableDriving },
        </if>
        <if test="isAddSummaryNotice ==true">
            is_add_summary_notice=  #{isAddSummaryNotice },
        </if>
        del_flag = 0
        where
        uuid = #{id} and del_flag = 0
    </update>

    <!-- 更新子表对应数据 -->
    <update id="updateDrainStatus">
        update tfs_drain_accounts
        set
         status=9
        where
        uuid = #{id} and del_flag = 0
    </update>

    <!-- 删除数据 -->
    <update id="delete">
        update tfs_drain_accounts set del_flag = 1 where uuid = #{id} and org_code = #{orgCode} and del_flag = 0;
    </update>

    <!-- 修改上报状态 -->
    <update id="upload">
        update tfs_drain_accounts
        set status = 4 , upload_time = #{uploadTime}
        where uuid = #{id}
        and org_code = #{orgCode}
        and del_flag = 0
        AND is_add_transfer_detection = TRUE
       <!-- AND is_add_stop_notice = TRUE
        AND is_add_survey_notice = TRUE-->
        AND is_add_acceptance_check = TRUE
        AND is_add_allowable_driving = TRUE
    </update>

    <!-- 是否完成通知单 -->
    <select id="anyUploaded" resultType="boolean">
        select (
          select count(*) from tfs_drain_accounts
            where uuid = #{id}
            and org_code = #{orgCode}
            and del_flag = 0
            AND is_add_transfer_detection = TRUE
            <!--AND is_add_stop_notice = TRUE
            AND is_add_survey_notice = TRUE-->
            AND is_add_acceptance_check = TRUE
            AND is_add_allowable_driving = TRUE
        )>0;
    </select>

    <!-- 生成台账 -->
    <update id="uploadDrainid">
        update tfs_drain_accounts
        set is_accomplish = #{isAccomplish},
        status = 5
        where uuid = #{id}
        and org_code = #{orgCode}
        and del_flag = 0
        AND is_add_transfer_detection = TRUE
       <!-- AND is_add_stop_notice = TRUE
        AND is_add_survey_notice = TRUE-->
        AND is_add_acceptance_check = TRUE
        AND is_add_allowable_driving = TRUE
    </update>

    <!-- 修改查看状态 -->
    <update id="view">
        update tfs_drain_accounts
        set is_viewed = 1
        where uuid = #{id}  and del_flag = 0
    </update>


    <!-- 获取所有工作面名称 -->
    <select id="getWorkName" resultType="String">
        select work_name from tfs_drain_accounts where org_code =#{orgCode} and del_flag = 0
        group by work_name
    </select>

    <!-- 获取所有探水时间 -->
    <select id="getSurveyWaterDate" resultType="String">
        select survey_water_date from tfs_drain_accounts where org_code =#{orgCode} and del_flag = 0
        group by survey_water_date
    </select>

    <!-- 根据工作面名称获取所有探水里程 -->
    <select id="getSurveyWaterMileage" resultType="int">
        select survey_water_mileage from tfs_drain_accounts
        where org_code =#{orgCode}
        and del_flag = 0
        <if test="workName != null and !workName.isEmpty()">
            and
            work_name = #{workName}
        </if>
        group by survey_water_mileage desc
    </select>

    <!-- 根据探水设计的工作面名称获取探水概括 -->
    <select id="getTransferGeneralByWorkName" resultMap="DrainAccountInstance">
          select
          uuid,
          org_code,
          org_name,
          create_time,
          del_flag,
          tunnel_id,
          check_plan_id,
          work_name,
          work_code,
          survey_water_mileage,
          survey_water_date,
          is_add_stop_notice,
          is_add_survey_notice,
          is_add_acceptance_check,
          is_add_transfer_detection,
          is_add_allowable_driving,
          is_add_summary_notice,
          status,
          is_viewed,
          upload_time,
          user_id,
          is_accomplish
          from tfs_drain_accounts
		  where
		  org_code =#{orgCode}
		  and work_name = #{workName}
		  and del_flag = 0
		  and is_add_transfer_detection=true
		  order by survey_water_date desc
		  LIMIT 1;
    </select>

    <!-- 根据探水设计id获取探水概括所需信息 -->
    <select id="getTransferGeneralById" resultMap="DrainAccountInstance">
          select
          uuid,
          org_code,
          org_name,
          create_time,
          del_flag,
          tunnel_id,
          check_plan_id,
          work_name,
          work_code,
          survey_water_mileage,
          survey_water_date,
          is_add_stop_notice,
          is_add_survey_notice,
          is_add_acceptance_check,
          is_add_transfer_detection,
          is_add_allowable_driving,
          is_add_summary_notice,
          status,
          is_viewed,
          upload_time,
          user_id,
          is_accomplish
          from tfs_drain_accounts
		  where
		  org_code =#{orgCode}
		  and tunnel_id = #{tunnelId}
		  and del_flag = 0
		  AND del_flag = FALSE
          AND is_add_transfer_detection = TRUE
          AND is_add_stop_notice = TRUE
          AND is_add_survey_notice = TRUE
          AND is_add_acceptance_check = TRUE
          AND is_add_allowable_driving = TRUE
          AND survey_water_date IN (
            SELECT
                MAX( survey_water_date )
            FROM
                tfs_drain_accounts
            WHERE
                org_code = #{orgCode}
                AND tunnel_id = #{tunnelId}
                AND del_flag = FALSE
                AND is_add_transfer_detection = TRUE
--                 AND is_add_stop_notice = TRUE
--                 AND is_add_survey_notice = TRUE
                AND is_add_acceptance_check = TRUE
                AND is_add_allowable_driving = TRUE
            )
    </select>

    <!-- 根据机构编码查询探水概括所需要的信息 -->
    <select id="getByIdTransferGeneral" resultMap="DrainAccountInstance">
        SELECT
            uuid,
            org_code,
            org_name,
            create_time,
            del_flag,
            tunnel_id,
            check_plan_id,
            work_name,
            work_code,
            survey_water_mileage,
            survey_water_date,
            is_add_stop_notice,
            is_add_survey_notice,
            is_add_acceptance_check,
            is_add_transfer_detection,
            is_add_allowable_driving,
            is_add_summary_notice,
            STATUS,
            is_viewed,
            upload_time,
            user_id,
            is_accomplish
        FROM
            tfs_drain_accounts
        WHERE
            org_code = #{orgCode}
            AND del_flag = FALSE
            AND is_add_transfer_detection = TRUE
--             AND is_add_stop_notice = TRUE
--             AND is_add_survey_notice = TRUE
            AND is_add_acceptance_check = TRUE
            AND is_add_allowable_driving = TRUE
            AND survey_water_date IN (
            SELECT
                MAX( survey_water_date )
            FROM
                tfs_drain_accounts
            WHERE
                org_code = #{orgCode}
                AND del_flag = FALSE
                AND is_add_transfer_detection = TRUE
--                 AND is_add_stop_notice = TRUE
--                 AND is_add_survey_notice = TRUE
                AND is_add_acceptance_check = TRUE
                AND is_add_allowable_driving = TRUE
            GROUP BY
                tunnel_id
            )
        ORDER BY
            survey_water_date DESC
    </select>

    <!-- 判断数据是否存在 -->
    <select id="anyById" resultType="boolean">
        select (
          select count(*) from tfs_drain_accounts where uuid = #{id}  and del_flag = 0
        )>0;
    </select>

    <!-- 判断数据是否被查看 -->
    <select id="isViewed" resultType="boolean">
        select (
          select count(*) from tfs_drain_accounts where uuid = #{id} and org_code =#{orgCode} and is_viewed = 0 and del_flag=0
        )>0;
    </select>

    <!-- 查询数据状态 -->
    <select id="getDataStatus" resultType="int">
        select status from tfs_drain_accounts where uuid = #{id} and org_code = #{orgCode} and del_flag = 0;
    </select>

    <!-- 查询数据条数 -->
    <select id="getCount" resultType="long">
        select
        count(*)
        from
        tfs_drain_accounts
        where
        org_code = #{orgCode}
        and
        del_flag = 0
        <if test="workName != null and !workName.isEmpty()">
            and
            work_name = #{workName}
        </if>
        <if test="surveyWaterDate != null">
            and
            year (survey_water_date) = #{surveyWaterDate}
        </if>
    </select>

    <!-- 查询数据条数  生成台账的 -->
    <select id="getCountByStatus" resultType="long">
        select
        count(*)
        from
        tfs_drain_accounts
        where
        org_code = #{orgCode}
        and
        del_flag = 0
        <if test="workName != null and !workName.isEmpty()">
            and
            work_name = #{workName}
        </if>
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()" >
            and
            DATE_FORMAT (survey_water_date,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (survey_water_date,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (survey_water_date,'%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="status != null">
            and
            status = #{status}
        </if>
        <!--<if test="isAccomplishStatus != null">
            and
            is_accomplish = #{isAccomplishStatus}
        </if>-->
    </select>

    <!-- 查询报表数据条数 -->
    <select id="getFormCount" resultType="long">
        select
        count(*)
        from
        tfs_drain_accounts
        where
        org_code = #{orgCode}
        and
        del_flag = 0
        <if test="workName != null and !workName.isEmpty()">
            and
            work_name = #{workName}
        </if>
        <if test="surveyWaterDate != null">
            and
            year (survey_water_date) = #{surveyWaterDate}
        </if>
        <!--<if test="isAddStopNotice ==true ">-->
            <!--and-->
            <!--is_add_stop_notice= #{isAddStopNotice}-->
        <!--</if>-->
        <!--<if test="isAddSurveyNotice  ==true">-->
            <!--and-->
            <!--is_add_survey_notice= #{isAddSurveyNotice}-->
        <!--</if>-->
        <if test="isAddAcceptanceCheck  ==true">
            and
            is_add_acceptance_check= #{isAddAcceptanceCheck}
        </if>
        <if test="isAddTransferDetection ==true">
            and
            is_add_transfer_detection= #{isAddTransferDetection}
        </if>
        <if test="isAddAllowableDriving  ==true">
            and
            is_add_allowable_driving= #{isAddAllowableDriving }
        </if>
        <if test="isAddSummaryNotice ==true">
            and
            is_add_summary_notice=  #{isAddSummaryNotice }
        </if>
    </select>

    <!--表单查询-->
    <select id="getForm" resultMap="DrainAccountInstance">
        select
        uuid,
        org_code,
        work_name,
        survey_water_mileage,
        survey_water_date,
        is_add_stop_notice,
        is_add_survey_notice,
        is_add_acceptance_check,
        is_add_transfer_detection,
        is_add_allowable_driving,
        is_add_summary_notice,
        status,
        is_viewed,
        upload_time,
        user_id,
        is_accomplish
        from tfs_drain_accounts
        where
        org_code = #{orgCode}
        and
        del_flag = 0
        <if test="workName != null and !workName.isEmpty()">
            and
            work_name = #{workName}
        </if>
        <if test="surveyWaterDate != null">
            and
            year (survey_water_date) = #{surveyWaterDate}
        </if>
        <!--<if test="isAddStopNotice ==true ">-->
            <!--and-->
            <!--is_add_stop_notice= #{isAddStopNotice}-->
        <!--</if>-->
        <!--<if test="isAddSurveyNotice  ==true">-->
            <!--and-->
            <!--is_add_survey_notice= #{isAddSurveyNotice}-->
        <!--</if>-->
        <if test="isAddAcceptanceCheck  ==true">
            and
            is_add_acceptance_check= #{isAddAcceptanceCheck}
        </if>
        <if test="isAddTransferDetection ==true">
            and
            is_add_transfer_detection= #{isAddTransferDetection}
        </if>
        <if test="isAddAllowableDriving  ==true">
            and
            is_add_allowable_driving= #{isAddAllowableDriving }
        </if>
        <if test="isAddSummaryNotice ==true">
            and
            is_add_summary_notice=  #{isAddSummaryNotice }
        </if>
        order by survey_water_date desc
    </select>

    <!-- 修改验收表的添加状态 -->
    <update id="updateAcceptStatus">
        update tfs_drain_accounts set is_add_acceptance_check = #{acceptStatus} where uuid = #{id} and org_code = #{orgCode} and del_flag = 0;
    </update>

    <!-- 根据探水设计Id获取数据 -->
    <select id="getByTunnelId" resultMap="DrainAccountInstance">
        select
          uuid,
          org_code,
          org_name,
          create_time,
          del_flag,
          tunnel_id,
          check_plan_id,
          work_name,
          work_code,
          survey_water_mileage,
          survey_water_date,
          is_add_stop_notice,
          is_add_survey_notice,
          is_add_acceptance_check,
          is_add_transfer_detection,
          is_add_allowable_driving,
          is_add_summary_notice,
          status,
          is_viewed,
          upload_time,
          user_id,
               camera_ip,
          is_accomplish
          from tfs_drain_accounts
          where
          del_flag = 0
          and
          tunnel_id = #{tunnelId}
          <if test="cameraIp != null and cameraIp != ''">
              and camera_ip = #{cameraIp}
          </if>
          order by
          survey_water_mileage
    </select>

    <!-- 根据探水设计Id和是否完成获取数据 -->
    <select id="getByTunnelIdAndAccomplish" resultMap="DrainAccountInstance">
        select
          check_plan_id
          from tfs_drain_accounts
          where
          del_flag = 0
          and
          org_code=#{orgCode}
          and
          tunnel_id = #{tunnelId}
          and
          is_accomplish = 1
          order by
          survey_water_mileage
    </select>

    <!-- 根据探水设计Id获取数据 -->
    <select id="getByTunnelIdDesc" resultMap="DrainAccountInstance">
        select
          uuid,
          org_code,
          org_name,
          create_time,
          del_flag,
          tunnel_id,
          check_plan_id,
          work_name,
          work_code,
          survey_water_mileage,
          survey_water_date,
          is_add_stop_notice,
          is_add_survey_notice,
          is_add_acceptance_check,
          is_add_transfer_detection,
          is_add_allowable_driving,
          is_add_summary_notice,
          status,
          is_viewed,
          upload_time,
          user_id,
          is_accomplish
          from tfs_drain_accounts
          where
          del_flag = 0
          and
          org_code=#{orgCode}
          and
          tunnel_id = #{tunnelId}
          order by
          survey_water_mileage
          desc
    </select>


    <!-- 根据探水设计Id组合查询数据 -->
    <select id="getByTunnelIds" resultMap="DrainAccountInstance">
        SELECT
            uuid,
            org_code,
            org_name,
            create_time,
            del_flag,
            tunnel_id,
            check_plan_id,
            work_name,
            work_code,
            survey_water_mileage,
            survey_water_date,
            is_add_stop_notice,
            is_add_survey_notice,
            is_add_acceptance_check,
            is_add_transfer_detection,
            is_add_allowable_driving,
            is_add_summary_notice,
            STATUS,
            is_viewed,
            upload_time,
            user_id,
            is_accomplish
        FROM
            tfs_drain_accounts
        WHERE
            org_code = #{orgCode}
          AND del_flag = 0
          AND is_add_transfer_detection = TRUE
          AND is_add_acceptance_check = TRUE
          AND is_add_allowable_driving = TRUE
          AND tunnel_id IN (<foreach collection="ids" separator="," item="id">#{id}</foreach> )
        ORDER BY
            survey_water_date DESC
    </select>

    <!-- 判断工作面名称是否重复 -->
    <select id="anyBySurveyWaterMileage" resultType="boolean">
        select (
          select count(*) from tfs_drain_accounts where tunnel_id = #{tunnelId} and survey_water_mileage =#{surveyWaterMileage} and org_code =#{orgCode} and del_flag = 0
        )>0;
    </select>

    <!-- 判断该工作面探水时间是否重复 -->
    <select id="anyBySurveyWaterDate" resultType="boolean">
        select (
          select count(*) from tfs_drain_accounts where tunnel_id = #{tunnelId} and survey_water_date =#{surveyWaterDate} and org_code =#{orgCode} and del_flag = 0
        )>0;
    </select>

    <!-- 获取一个月内煤矿的探水次数 -->
    <select id="getTimes" resultType="Integer">
        select count(*) from tfs_drain_accounts where org_code = #{orgCode} and year(survey_water_date) = #{year} and month(survey_water_date) = #{month} and del_flag = 0
    </select>

    <!-- 根据工作面编码和里程查询数据 -->
    <select id="getByCodeAndMileage" resultMap="DrainAccountInstance">
        select
          uuid,
          org_code,
          org_name,
          create_time,
          del_flag,
          tunnel_id,
          check_plan_id,
          work_name,
          work_code,
          survey_water_mileage,
          survey_water_date,
          is_add_stop_notice,
          is_add_survey_notice,
          is_add_acceptance_check,
          is_add_transfer_detection,
          is_add_allowable_driving,
          is_add_summary_notice,
          status,
          is_viewed,
          upload_time,
          user_id,
          is_accomplish
        from
        tfs_drain_accounts
        where
        tunnel_id=#{tunnelId}
        and
        survey_water_mileage=#{surveyWaterMileage}
        and
        org_code=#{orgCode}
        and
        del_flag = 0
    </select>

    <!--修改探水总结表状态-->
    <update id="updateSummaryState">
        UPDATE
        tfs_drain_accounts
        SET
        is_add_summary_notice=#{flag}
        WHERE
        uuid=#{id}
        and
        org_code=#{orgcode}
        and
        del_flag=0
    </update>

    <!--修改探水下发任务 userId-->
    <update id="updateUserId">
        UPDATE
        tfs_drain_accounts
        SET
        user_id=#{userId}
        WHERE
        uuid=#{id}
        and
        org_code=#{orgCode}
        and
        del_flag=0
    </update>
    <!--根据计划ID和探水里程查询-->
    <select id="getByTunnalIdAndSurveyWaterMileage" resultMap="DrainAccountInstance">
        select
        uuid,
        org_code,
        org_name,
        create_time,
        del_flag,
        tunnel_id,
        check_plan_id,
        work_name,
        work_code,
        survey_water_mileage,
        survey_water_date,
        is_add_stop_notice,
        is_add_survey_notice,
        is_add_acceptance_check,
        is_add_transfer_detection,
        is_add_allowable_driving,
        is_add_summary_notice,
        status,
        is_viewed,
        upload_time,
        user_id,
        is_accomplish
        from
        tfs_drain_accounts
        where
        survey_water_mileage=#{surveyWaterMileage}
        and
        tunnel_id=#{tunnelId}
        and
        org_code = #{orgCode}
        and
        del_flag = 0
        order by survey_water_date desc,tunnel_id
    </select>

    <!-- 获取最近一次探水的台账数据 -->
    <select id="getLatestWaterDateAccount" resultMap="DrainAccountInstance">
        select
        uuid,
        org_code,
        org_name,
        create_time,
        del_flag,
        tunnel_id,
        check_plan_id,
        work_name,
        work_code,
        survey_water_mileage,
        survey_water_date,
        is_add_stop_notice,
        is_add_survey_notice,
        is_add_acceptance_check,
        is_add_transfer_detection,
        is_add_allowable_driving,
        is_add_summary_notice,
        status,
        is_viewed,
        upload_time,
        user_id,
        is_accomplish
        from
        tfs_drain_accounts
        where
        org_code = #{orgCode}
        and EXISTS (select 1 from (select a.drain_id from tfs_hole_details a inner join tfs_history_videos b on a.uuid=b.hole_detail_id) t where t.drain_id=tfs_drain_accounts.uuid)
        order by survey_water_date desc
        limit 1
    </select>

    <!--修改煤层倾角-->
    <update id="updateCoalAngle">
        UPDATE
        tfs_drain_accounts
        SET
        coal_angle=#{coalAngle}
        WHERE
        uuid=#{id}
        and
        del_flag=0
    </update>

    <!-- 根据台账id修改允许掘进通知单 -->
    <update id = "updateAllowNoticeNoticeContent">
        UPDATE
        tfs_drain_accounts
        SET
        allowable_notice_content=#{allowableNoticeContent}
        WHERE
        uuid=#{id}
        and
        org_code = #{orgCode}
        and
        del_flag=0
    </update>

    <!-- 查询允许掘进通知单内容 -->
    <select id="getAllowNoticeNoticeContent" resultType="map">
        select
          allowable_notice_content content,
          org_code orgCode
          from tfs_drain_accounts
          where
          del_flag = 0
          and uuid=#{id}
    </select>

    <select id="getTunnelInfoByUser" resultMap="DrainAccountInstance">
        select uuid,
          org_code,
          org_name,
          create_time,
          del_flag,
          tunnel_id,
          check_plan_id,
          work_name,
          work_code,
          survey_water_mileage,
          survey_water_date,
          is_add_stop_notice,
          is_add_survey_notice,
          is_add_acceptance_check,
          is_add_transfer_detection,
          is_add_allowable_driving,
          is_add_summary_notice,
          status,
          is_viewed,
          upload_time,
          user_id,
          is_accomplish
          from tfs_drain_accounts
          where check_plan_id in
            ( select distinct  check_plan_id from tfs_check_plan_users where work_user_id = #{workUserId} and org_code = #{orgCode} and del_flag = '0' and
              date_format(create_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d')) and del_flag = '0'
    </select>

    <select id="getAnalysisNum" resultType="jylink.cpds.serviceModel.dto.OrgCameraTreeDto">
        SELECT
            drain.org_code AS orgCode,
            count(*) AS analysisNum
        FROM
            tfs_drain_accounts drain
        LEFT JOIN tfs_analysis_works ana ON ana.drain_id = drain.uuid
        WHERE
            drain.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
          AND drain.del_flag = 0
          AND ana.`status` = 3
        GROUP BY
            drain.org_code
    </select>

    <select id="getTunnelOrgCodes" resultType="string">
        SELECT
            org_code
        FROM
            tfs_tunnel_designs
        WHERE
            org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
          AND del_flag = 0
        GROUP BY
            org_code
    </select>

    <select id="getPlanDrillNum" resultType="jylink.cpds.serviceModel.dto.PlanDrillInfoDto">
        SELECT
            COUNT( DISTINCT drain.uuid ) planNum
        FROM
            tfs_drain_accounts drain
                LEFT JOIN tfs_analysis_works ana ON ana.drain_id = drain.uuid
        WHERE
            drain.del_flag = 0
        AND ana.uuid is null
        AND drain.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
        <if test="type != null and type == 0">
            AND YEARWEEK( date_format( drain.survey_water_date, '%Y-%m-%d' ), 1 ) = YEARWEEK( now(), 1 )
        </if>
        <if test="type != null and type == 1">
            AND YEAR ( drain.survey_water_date ) = YEAR (NOW())
            AND MONTH ( drain.survey_water_date ) = MONTH (NOW())
        </if>
        <if test="type != null and type == 2">
            AND YEAR ( drain.survey_water_date ) = YEAR (NOW())
        </if>
    </select>

    <select id="getAbnormalNumInfo" resultMap="BaseItemInstance">
        SELECT
            abnormal_name,
            abnormal_type,
            COUNT(*) AS total
        FROM
            (
                SELECT
                    dra.uuid,
                    re.abnormal_name,
                    re.abnormal_type
                FROM
                    tfs_drain_accounts dra
                        left join tfs_acceptance_checks acc on acc.drain_id = dra.uuid
                        LEFT JOIN tfs_hole_details hole ON hole.uuid = acc.hole_detail_id
                        INNER JOIN ( SELECT abnormal_type, abnormal_name, drain_id, hole_detail_id FROM tfs_hole_details_report WHERE del_flag = 0 AND abnormal_type >= 0 GROUP BY abnormal_type, hole_detail_id ) re ON re.hole_detail_id = hole.uuid
                WHERE
                    dra.del_flag = 0
                  AND hole.del_flag = 0
                  and acc.del_flag = 0
                AND dra.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
                <if test="type != null and type == 0">
                    AND YEARWEEK( date_format( hole.report_time, '%Y-%m-%d' ), 1 ) = YEARWEEK( now(), 1 )
                </if>
                <if test="type != null and type == 1">
                    AND YEAR ( hole.report_time ) = YEAR (NOW())
                    AND MONTH ( hole.report_time ) = MONTH (NOW())
                </if>
                <if test="type != null and type == 2">
                    AND YEAR ( hole.report_time ) = YEAR (NOW())
                </if>
                <if test="startDate != null and startDate!= ''">
                    and
                    DATE_FORMAT (hole.report_time,'%Y-%m-%d') &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate != ''">
                    and
                    DATE_FORMAT (hole.report_time,'%Y-%m-%d') &lt;= #{endDate}
                </if>
                <if test="orgName != null and orgName != '' ">
                    and INSTR(dra.org_name,#{orgName})
                </if>
                GROUP BY
                    hole.uuid,
                    re.abnormal_type
            ) t
        GROUP BY
            abnormal_type
    </select>

    <select id="getNoDrillPlanNum" resultType="integer">
        select count(*) from (
        SELECT
            drain.uuid
        FROM
            tfs_drain_accounts drain
        LEFT JOIN tfs_analysis_works ana ON ana.drain_id = drain.uuid
        WHERE
            DATE_FORMAT( drain.survey_water_date, '%Y-%m-%d' ) &lt; DATE_FORMAT( DATE_ADD( now(), INTERVAL #{day} DAY ), '%Y-%m-%d' )
            AND drain.del_flag = 0
            AND drain.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
            AND ana.uuid IS NULL
            AND DATE_FORMAT( drain.survey_water_date, '%Y-%m-%d' ) >= DATE_FORMAT( now(), '%Y-%m-%d' )
        GROUP BY
            drain.uuid ) t
    </select>

    <select id="getAnalysisDrainList" resultMap="DrainAccountInstance">
        SELECT
            drain.uuid,
            drain.org_code,
            drain.org_name,
            ana.create_time,
            drain.del_flag,
            drain.tunnel_id,
            drain.check_plan_id,
            drain.work_name,
            drain.work_code,
            drain.survey_water_mileage,
            drain.survey_water_date,
            drain.is_add_stop_notice,
            drain.is_add_survey_notice,
            drain.is_add_acceptance_check,
            drain.is_add_transfer_detection,
            drain.is_add_allowable_driving,
            drain.is_add_summary_notice,
            drain.STATUS,
            drain.is_viewed,
            drain.upload_time,
            drain.user_id,
            drain.camera_ip,
            drain.is_accomplish
        FROM
            tfs_drain_accounts drain
                LEFT JOIN tfs_analysis_works ana ON ana.drain_id = drain.uuid
        WHERE
            drain.del_flag = 0
          AND ana.`status` = 3
        AND drain.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
        <if test="orgName != null and orgName != '' ">
            and INSTR(CONCAT(drain.org_name,drain.work_name),#{orgName})
        </if>
        GROUP BY
            drain.uuid
    </select>

    <select id="getRealWaterDetection" resultType="jylink.cpds.serviceModel.dto.RealWaterDetectionDetailDto">
        SELECT
            *
        FROM
            (
            SELECT
                MIN( ana.create_time ) AS analysisStartTime,
                ana.`status`,
                dra.uuid AS drainId,
                dra.tunnel_id,
                dra.work_name,
                dra.org_code,
                dra.org_name,
                dra.survey_water_mileage,
                geo.result,
                acc.holeNum,
                acc.drillNum
            FROM
                tfs_drain_accounts dra
            LEFT JOIN tfs_analysis_works ana ON ana.del_flag = 0
                AND ana.drain_id = dra.uuid
            LEFT JOIN tfs_check_plans plan ON plan.uuid = dra.check_plan_id
            LEFT JOIN tfs_geophysical_report geo ON geo.uuid = plan.geophysical_id
                AND geo.`status` = 2
                AND geo.del_flag = 0
            LEFT JOIN (
                SELECT
                *
                FROM
                (
                SELECT
                count(*) AS holeNum,
                sum( CASE WHEN hole.STATUS = 2 THEN 1 ELSE 0 END ) AS drillNum,
                acc.drain_id
                FROM
                tfs_acceptance_checks acc
                LEFT JOIN tfs_hole_details hole ON hole.del_flag = 0
                AND hole.uuid = acc.hole_detail_id
                WHERE
                acc.del_flag = 0
                GROUP BY
                acc.drain_id
                ) t
                ) acc ON acc.drain_id = dra.uuid
            WHERE
                        ana.drain_id IN (
                        SELECT
                            dra.uuid
                        FROM
                            tfs_drain_accounts dra
                                LEFT JOIN tfs_analysis_works ana ON ana.del_flag = 0
                                AND ana.drain_id = dra.uuid
                        WHERE
                            ana.`status` = 3
                          AND dra.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
                          AND plan.del_flag = 0
                        <if test="orgName != null and orgName != '' ">
                            and INSTR(CONCAT(dra.org_name,dra.work_name),#{orgName})
                        </if>

                        AND dra.del_flag = 0
                        ORDER BY
                            ana.create_time DESC
                    )
                GROUP BY
                    dra.uuid
            ) t
        ORDER BY
            analysisStartTime DESC
    </select>

    <select id="getNoAnalysisList" resultType="jylink.cpds.serviceModel.dto.NoAnalysisPlanListDto">
        SELECT * FROM (
        SELECT
        plan.uuid AS planId,
        plan.org_code,
        plan.org_name,
        plan.work_name,
        plan.check_position,
        plan.prediction_date,
        plan.class_no_name,
        count( DISTINCT hole.uuid ) AS holeNum,
        plan.geophysical_id,
        geo.result,
        cla.class_start_time,
        cla.class_end_time
        FROM
        tfs_drain_accounts drain
        LEFT JOIN tfs_analysis_works ana ON ana.drain_id = drain.uuid
        LEFT JOIN tfs_check_plans plan ON plan.uuid = drain.check_plan_id
        LEFT JOIN tfs_check_plan_details hole ON hole.check_plan_id = plan.uuid
        AND hole.del_flag = 0
        LEFT JOIN tfs_geophysical_report geo ON geo.uuid = plan.geophysical_id
        AND geo.del_flag = 0
        AND geo.`status` = 2
        LEFT JOIN tfs_class_managers cla ON cla.uuid = plan.class_id
        AND cla.del_flag = 0
        WHERE
        drain.del_flag = 0
          <if test="day != null">
              AND DATE_FORMAT( drain.survey_water_date, '%Y-%m-%d' ) &lt; DATE_FORMAT( DATE_ADD( now(), INTERVAL #{day} DAY ),
              '%Y-%m-%d' )
          </if>
        AND drain.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
        <if test="orgName != null and orgName != '' ">
            and INSTR(CONCAT(plan.org_name,plan.work_name),#{orgName})
        </if>
        AND plan.del_flag = 0
        AND ana.uuid IS NULL
        AND DATE_FORMAT( drain.survey_water_date, '%Y-%m-%d' ) >= DATE_FORMAT( now(), '%Y-%m-%d' )
        GROUP BY
        drain.uuid
        ) t
        <if test="orderType != null and orderType == 0">
            order by prediction_date
        </if>
        <if test="orderType != null and orderType == 1">
            order by prediction_date desc
        </if>
    </select>

    <select id="getDrillPlanNum" resultType="jylink.cpds.serviceModel.dto.RealWaterDetectionDto">
        SELECT
            COUNT( DISTINCT drainId ) planNum,
            COUNT( DISTINCT org_code ) mineNum,
            SUM( CASE WHEN analysis_type = 0 THEN 1 ELSE 0 END ) noAnalysisNum,
            SUM( CASE WHEN analysis_type = 1 THEN 1 ELSE 0 END ) analysisNum,
            SUM( CASE WHEN analysis_type = 2 THEN 1 ELSE 0 END ) finishAnalysisNum
        FROM
            (
                SELECT
                    dra.uuid AS drainId,
                    dra.org_code,
                    CASE

                        WHEN maxAna.STATUS IS NULL THEN
                            0
                        WHEN maxAna.STATUS = 3 THEN
                            1 ELSE 2
                        END analysis_type,
        CONCAT(
        DATE_FORMAT( plan.prediction_date, '%Y-%m-%d' ),
                CASE

                WHEN cla.uuid IS NULL THEN
                '00:00:00'
                WHEN cla.class_start_time >= 10 THEN
                CONCAT( ' ', cla.class_start_time, ':00:00' ) ELSE CONCAT( ' 0', cla.class_start_time, ':00:00' )
                END
                ) AS orderTime
        FROM
                    tfs_drain_accounts dra
                        LEFT JOIN tfs_check_plans plan ON plan.uuid = dra.check_plan_id
                        LEFT JOIN tfs_class_managers cla ON cla.uuid = plan.class_id
                        LEFT JOIN tfs_analysis_works ana ON ana.drain_id = dra.uuid
                        LEFT JOIN ( SELECT * FROM ( SELECT STATUS, drain_id FROM tfs_analysis_works WHERE del_flag = 0 HAVING 1 ORDER BY create_time DESC ) t GROUP BY drain_id ) maxAna ON maxAna.drain_id = dra.uuid
                        LEFT JOIN (
                        SELECT
                            *
                        FROM
                            (
                                SELECT
                                    max( hole.end_drill_time ) end_drill_time,
                                    hole.drain_id
                                FROM
                                    tfs_hole_details hole
                                WHERE
                                    hole.del_flag = 0
                                  AND hole.`status` = 2
                                  AND NOT EXISTS ( SELECT 1 FROM tfs_history_analysis_works his WHERE his.hole_detail_id = hole.uuid AND his.del_flag = 0 )
                                GROUP BY
                                    hole.drain_id
                            ) t
                    ) hole ON hole.drain_id = dra.uuid
                WHERE
                    dra.del_flag = 0
                  AND plan.del_flag = 0
                  AND plan.`status` = 2
                <if test="orgName != null and orgName != '' ">
                    and INSTR(CONCAT(dra.org_name,dra.work_name),#{orgName})
                </if>
                <if test=" orgCode != null and orgCode != '' ">
                    and dra.org_code = #{orgCode}
                </if>
                AND dra.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
                GROUP BY
                    dra.uuid
            ) t
        <where>
            <if test="type != null and type == 0">
                AND YEARWEEK( date_format( orderTime, '%Y-%m-%d' ), 1 ) = YEARWEEK( now(), 1 )
            </if>
            <if test="type != null and type == 1">
                AND YEAR ( orderTime ) = YEAR (NOW())
                AND MONTH ( orderTime ) = MONTH (NOW())
            </if>
            <if test="type != null and type == 2">
                AND YEAR ( orderTime ) = YEAR (NOW())
            </if>
        </where>
    </select>

    <select id="getDrillPlanCount" resultType="long" >
        SELECT
            count(*)
        FROM
            (
                SELECT
                    CASE

                        WHEN
                            maxAna.STATUS IS NULL THEN
                            0
                        WHEN maxAna.STATUS = 3 THEN
                            1 ELSE 2
                        END analysis_type,
                    CONCAT(
                            DATE_FORMAT( plan.prediction_date, '%Y-%m-%d' ),
                            CASE

                                WHEN cla.uuid IS NULL THEN
                                    '00:00:00'
                                WHEN cla.class_start_time >= 10 THEN
                                    CONCAT( ' ', cla.class_start_time, ':00:00' ) ELSE CONCAT( ' 0', cla.class_start_time, ':00:00' )
                                END
                        ) AS orderTime
                FROM
                    tfs_drain_accounts dra
                        LEFT JOIN tfs_check_plans plan ON plan.uuid = dra.check_plan_id
                        LEFT JOIN tfs_class_managers cla ON cla.uuid = plan.class_id
                        LEFT JOIN (
                        SELECT
                            r1.drain_id,
                            r1.`status`,
                            r1.end_time
                        FROM
                            tfs_analysis_works r1
                        WHERE
                            NOT EXISTS (
                                    SELECT
                                        1
                                    FROM
                                        tfs_analysis_works r2
                                    WHERE
                                        r1.drain_id = r2.drain_id
                                      AND r2.create_time > r1.create_time
                                      AND r1.del_flag = 0
                                      AND r1.del_flag = 0
                                )
                        GROUP BY
                            drain_id
                    ) maxAna ON maxAna.drain_id = dra.uuid
                WHERE
                    dra.del_flag = 0
                  AND plan.del_flag = 0
                  AND plan.`status` = 2
                  <if test="result != null and result ">
                      and plan.geophysical_id is not null
                  </if>
                <if test="result != null and !result ">
                    and plan.geophysical_id is null
                </if>
                <if test="orgName != null and orgName != '' ">
                    and INSTR(CONCAT(dra.org_name,dra.work_name),#{orgName})
                </if>
                <if test=" orgCode != null and orgCode != '' ">
                    and dra.org_code = #{orgCode}
                </if>
                <if test="tunnelId != null and tunnelId != ''">
                    and dra.tunnel_id = #{tunnelId}
                </if>
                AND dra.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
                GROUP BY
                    dra.uuid
            ) t
        <where>
            <if test="analysisType != null ">
                AND analysis_type = #{analysisType}
            </if>
            <if test="type != null and type == 0">
                AND YEARWEEK( date_format( orderTime, '%Y-%m-%d' ), 1 ) = YEARWEEK( now(), 1 )
            </if>
            <if test="type != null and type == 1">
                AND YEAR ( orderTime ) = YEAR (NOW())
                AND MONTH ( orderTime ) = MONTH (NOW())
            </if>
            <if test="type != null and type == 2">
                AND YEAR ( orderTime ) = YEAR (NOW())
            </if>
        </where>
    </select>

    <select id="getDrillPlanList" resultType="jylink.cpds.serviceModel.dto.DrillPlanListDto">
        SELECT
            *
        FROM
            (
                SELECT
                    dra.uuid AS drainId,
                    dra.org_code,
                    dra.work_name,
                    dra.tunnel_id,
                    dra.org_name,
                    dra.survey_water_mileage,
                    plan.prediction_date,
                    plan.class_no_name,
                    geo.result,
                    plan.geophysical_id,
                    maxAna.STATUS,
                    CASE

                        WHEN maxAna.STATUS IS NULL THEN
                            0
                        WHEN maxAna.STATUS = 3 THEN
                            1 ELSE 2
                        END analysis_type,
                    IFNULL( maxAna.end_time, max( hole.end_drill_time ) ) end_time,
                    min( ana.create_time ) AS startTime,
                    CONCAT(
                            DATE_FORMAT( plan.prediction_date, '%Y-%m-%d' ),
                            CASE

                                WHEN cla.uuid IS NULL THEN
                                    '00:00:00'
                                WHEN cla.class_start_time >= 10 THEN
                                    CONCAT( ' ', cla.class_start_time, ':00:00' ) ELSE CONCAT( ' 0', cla.class_start_time, ':00:00' )
                                END
                        ) AS orderTime
                FROM
                    tfs_drain_accounts dra
                        LEFT JOIN tfs_check_plans plan ON plan.uuid = dra.check_plan_id
                        LEFT JOIN tfs_class_managers cla ON cla.uuid = plan.class_id
                        LEFT JOIN tfs_analysis_works ana ON ana.drain_id = dra.uuid
                        LEFT JOIN tfs_geophysical_report geo ON geo.uuid = plan.geophysical_id
                        AND geo.del_flag = 0
                        AND geo.`status` = 2
                        LEFT JOIN (
                        SELECT
                            r1.drain_id,
                            r1.`status`,
                            r1.end_time
                        FROM
                            tfs_analysis_works r1
                        WHERE
                            NOT EXISTS (
                                    SELECT
                                        1
                                    FROM
                                        tfs_analysis_works r2
                                    WHERE
                                        r1.drain_id = r2.drain_id
                                      AND r2.create_time > r1.create_time
                                      AND r1.del_flag = 0
                                      AND r1.del_flag = 0
                                )
                        GROUP BY
                            drain_id
                    ) maxAna ON maxAna.drain_id = dra.uuid
                        LEFT JOIN (
                        SELECT
                            MAX( hole.end_drill_time ) end_drill_time,
                            hole.drain_id
                        FROM
                            tfs_hole_details hole
                        WHERE
                            hole.del_flag = 0
                          AND hole.`status` = 2
                          AND NOT EXISTS ( SELECT 1 FROM tfs_history_analysis_works his WHERE his.del_flag = 0 AND hole.uuid = his.hole_detail_id )
                        GROUP BY
                            hole.drain_id
                    ) hole ON hole.drain_id = dra.uuid
                WHERE
                    dra.del_flag = 0
                  AND plan.del_flag = 0
                  AND plan.`status` = 2
                <if test="result != null and result ">
                    and plan.geophysical_id is not null
                </if>
                <if test="result != null and !result ">
                    and plan.geophysical_id is null
                </if>
                <if test="orgName != null and orgName != '' ">
                    and INSTR(CONCAT(dra.org_name,dra.work_name),#{orgName})
                </if>
                <if test=" orgCode != null and orgCode != '' ">
                    and dra.org_code = #{orgCode}
                </if>
                  <if test="tunnelId != null and tunnelId != ''">
                      and dra.tunnel_id = #{tunnelId}
                  </if>
                AND dra.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
            GROUP BY
                    dra.uuid
            ) t
        <where>
            <if test="analysisType != null ">
                AND analysis_type = #{analysisType}
            </if>
            <if test="type != null and type == 0">
                AND YEARWEEK( date_format( orderTime, '%Y-%m-%d' ), 1 ) = YEARWEEK( now(), 1 )
            </if>
            <if test="type != null and type == 1">
                AND YEAR ( orderTime ) = YEAR (NOW())
                AND MONTH ( orderTime ) = MONTH (NOW())
            </if>
            <if test="type != null and type == 2">
                AND YEAR ( orderTime ) = YEAR (NOW())
            </if>
        </where>
        ORDER BY
            orderTime DESC,
            survey_water_mileage DESC
    </select>

    <select id="getLastDrillInfo" resultType="jylink.cpds.serviceModel.dto.DrillPlanListDto">
        SELECT
            *
        FROM
            (
                SELECT
                    dra.uuid AS drainId,
                    dra.org_code,
                    dra.work_name,
                    dra.tunnel_id,
                    dra.org_name,
                    dra.survey_water_mileage,
                    maxAna.STATUS,
                    IFNULL( max( ana.end_time ), max( hole.end_drill_time ) ) end_time,
                    min( ana.create_time ) AS startTime,
                    CASE

                        WHEN maxAna.STATUS = 3 THEN
                            NOW() ELSE IFNULL( max( ana.end_time ), hole.end_drill_time )
                        END AS orderTime
                FROM
                    tfs_drain_accounts dra
                        LEFT JOIN tfs_analysis_works ana ON ana.drain_id = dra.uuid
                        LEFT JOIN (
                        SELECT
                            *
                        FROM
                            ( SELECT STATUS, drain_id, del_flag FROM tfs_analysis_works WHERE del_flag = 0 HAVING 1 ORDER BY create_time DESC ) t
                        GROUP BY
                            drain_id
                    ) maxAna ON maxAna.drain_id = dra.uuid
                        LEFT JOIN (
                        SELECT
                            MAX( hole.end_drill_time ) end_drill_time,
                            hole.drain_id
                        FROM
                            tfs_hole_details hole
                        WHERE
                            hole.del_flag = 0
                          AND hole.`status` = 2
                          AND NOT EXISTS ( SELECT 1 FROM tfs_history_analysis_works his WHERE his.del_flag = 0 AND hole.uuid = his.hole_detail_id )
                        GROUP BY
                            hole.drain_id
                    ) hole ON hole.drain_id = dra.uuid
                WHERE
                    dra.del_flag = 0
                  AND maxAna.del_flag = 0
                  AND dra.org_code = #{orgCode}
                GROUP BY
                    dra.uuid
            ) t
        ORDER BY
            orderTime DESC
            LIMIT 1
    </select>

    <select id="getPoleAbnormalNum" resultType="long">
        SELECT
            COUNT(*)
        FROM
            (
                SELECT
                    dra.uuid AS drainId
                FROM
                    tfs_drain_accounts dra
                        LEFT JOIN tfs_acceptance_checks acc ON acc.drain_id = dra.uuid
                        LEFT JOIN tfs_hole_details hole ON hole.uuid = acc.hole_detail_id
                        INNER JOIN ( SELECT abnormal_type, abnormal_name, drain_id, hole_detail_id FROM tfs_hole_details_report WHERE del_flag = 0 AND abnormal_type >= 0 GROUP BY abnormal_type, hole_detail_id ) re ON re.hole_detail_id = hole.uuid
                WHERE
                    dra.del_flag = 0
                    AND hole.del_flag = 0
                    AND acc.del_flag = 0
                <if test="abnormalType != null ">
                    and re.abnormal_type = #{abnormalType}
                </if>
                AND dra.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
                <if test="orgCode != null and orgCode != ''">
                    AND dra.org_code = #{orgCode}
                </if>
                <if test="tunnelId != null and tunnelId != ''">
                    and dra.tunnel_id = #{tunnelId}
                </if>
                <if test="type != null and type == 0">
                    AND YEARWEEK( date_format( hole.report_time, '%Y-%m-%d' ), 1 ) = YEARWEEK( now(), 1 )
                </if>
                <if test="type != null and type == 1">
                    AND YEAR ( hole.report_time ) = YEAR (NOW())
                    AND MONTH ( hole.report_time ) = MONTH (NOW())
                </if>
                <if test="type != null and type == 2">
                    AND YEAR ( hole.report_time ) = YEAR (NOW())
                </if>
                <if test="startDate != null and startDate!= ''">
                    and
                    DATE_FORMAT (hole.report_time,'%Y-%m-%d') &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate != ''">
                    and
                    DATE_FORMAT (hole.report_time,'%Y-%m-%d') &lt;= #{endDate}
                </if>
                <if test="orgName != null and orgName != '' ">
                    and INSTR(dra.org_name,#{orgName})
                </if>
                GROUP BY
                    dra.uuid
            ) t
    </select>

    <select id="getPoleAbnormalList" resultType="jylink.cpds.serviceModel.dto.PoleAbnormalInfoDto">
        SELECT
            t.drainId,
            t.tunnel_id,
            t.`status`,
            t.org_code,
            t.org_name,
            t.work_name,
            t.survey_water_mileage,
            t.hole_no,
            t.vice_hole_no,
            t.holeDetailId,
            t.start_time
        FROM
            (
                SELECT
                    dra.uuid AS drainId,
                    dra.tunnel_id,
                    hole.`status`,
                    MIN( ana.create_time ) start_time,
                    CASE

                        WHEN hole.`status` = 0 THEN
                            2
                        WHEN hole.`status` = 1 THEN
                            0 ELSE 1
                        END AS orderType,
                    dra.org_code,
                    dra.org_name,
                    dra.work_name,
                    dra.survey_water_mileage,
                    hole.hole_no,
                    hole.vice_hole_no,
                    hole.uuid as holeDetailId,
                    hole.report_time
                FROM
                    tfs_drain_accounts dra
                        LEFT JOIN tfs_acceptance_checks acc ON acc.drain_id = dra.uuid
                        LEFT JOIN tfs_hole_details hole ON hole.uuid = acc.hole_detail_id
                        LEFT JOIN tfs_analysis_works ana ON ana.drain_id = dra.uuid
                        INNER JOIN ( SELECT abnormal_type, abnormal_name, drain_id, hole_detail_id FROM tfs_hole_details_report WHERE del_flag = 0 AND abnormal_type >= 0 GROUP BY abnormal_type, hole_detail_id ) re ON re.hole_detail_id = hole.uuid
                WHERE
                    dra.del_flag = 0
                    AND hole.del_flag = 0
                    AND acc.del_flag = 0
                    <if test="abnormalType != null ">
                        and re.abnormal_type = #{abnormalType}
                    </if>
                    AND dra.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
                    <if test="type != null and type == 0">
                        AND YEARWEEK( date_format( hole.report_time, '%Y-%m-%d' ), 1 ) = YEARWEEK( now(), 1 )
                    </if>
                    <if test="orgCode != null and orgCode != ''">
                        AND dra.org_code = #{orgCode}
                    </if>
                    <if test="tunnelId != null and tunnelId != ''">
                        and dra.tunnel_id = #{tunnelId}
                    </if>
                    <if test="type != null and type == 1">
                        AND YEAR ( hole.report_time ) = YEAR (NOW())
                        AND MONTH ( hole.report_time ) = MONTH (NOW())
                    </if>
                    <if test="type != null and type == 2">
                        AND YEAR ( hole.report_time ) = YEAR (NOW())
                    </if>
                    <if test="startDate != null and startDate!= ''">
                        and
                        DATE_FORMAT (hole.report_time,'%Y-%m-%d') &gt;= #{startDate}
                    </if>
                    <if test="endDate != null and endDate != ''">
                        and
                        DATE_FORMAT (hole.report_time,'%Y-%m-%d') &lt;= #{endDate}
                    </if>
                    <if test="orgName != null and orgName != '' ">
                        and INSTR(dra.org_name,#{orgName})
                    </if>
                GROUP BY
                    dra.uuid
            ) t
        ORDER BY
            orderType,
            start_time DESC,
            survey_water_mileage
    </select>

    <select id="getAnalysisDrain" resultType="jylink.cpds.serviceModel.dto.LastDrillInfoDto">
        SELECT
            dra.uuid AS drain_id,
               dra.check_plan_id ,
               dra.tunnel_id,
            dra.work_name,
            dra.survey_water_date,
            dra.survey_water_mileage,
            dra.diy_column,
            0 AS STATUS
        FROM
            tfs_drain_accounts dra
                LEFT JOIN tfs_analysis_works ana ON ana.drain_id = dra.uuid
        WHERE
            dra.org_code = #{orgCode}
          AND dra.del_flag = 0
          AND ana.`status` = 3
        ORDER BY
            ana.create_time DESC
            LIMIT 1
    </select>

    <select id="getExpectPlan" resultType="jylink.cpds.serviceModel.dto.LastDrillInfoDto">
        SELECT
            dra.uuid AS drain_id,
            dra.work_name,
            dra.survey_water_date,
            dra.survey_water_mileage,
            plan.drilling_item,
            dra.check_plan_id,
               dra.tunnel_id,
            plan.work_org_name,
            plan.class_no_name,
            JSON_UNQUOTE(
            JSON_EXTRACT( plan.diy_column, '$.MileageDesc' )) AS mileageDesc,
            1 AS STATUS
        FROM
            tfs_drain_accounts dra
                LEFT JOIN tfs_check_plans plan ON plan.uuid = dra.check_plan_id
                LEFT JOIN tfs_analysis_works ana ON ana.drain_id = dra.uuid
        WHERE
            dra.org_code = #{orgCode}
          AND plan.del_flag = 0
          AND plan.`status` = 2
          AND dra.del_flag = 0
          AND ana.`status` IS NULL
          AND DATE_FORMAT( dra.survey_water_date, '%Y-%m-%d' ) >= DATE_FORMAT( NOW(), '%Y-%m-%d' )
        ORDER BY
            dra.survey_water_date,
            dra.survey_water_mileage DESC
            LIMIT 1
    </select>

    <select id="getFinishDrill" resultType="jylink.cpds.serviceModel.dto.LastDrillInfoDto">
        SELECT
            dra.uuid AS drain_id,
            dra.work_name,
            dra.survey_water_date,
            dra.survey_water_mileage,
            dra.check_plan_id,
               dra.tunnel_id,
            2 AS STATUS
        FROM
            tfs_drain_accounts dra
                LEFT JOIN tfs_acceptance_checks acc ON acc.drain_id = dra.uuid
                LEFT JOIN tfs_history_videos his ON his.hole_detail_id = acc.hole_detail_id
                LEFT JOIN tfs_full_video_list fu ON fu.full_video_id = his.uuid
        WHERE
            dra.org_code = #{orgCode}
          AND dra.del_flag = 0
          AND ( LENGTH( his.cs_drill_full_video ) > 1 OR LENGTH( fu.video_url ) > 1 )
        ORDER BY
            dra.survey_water_date DESC,
            dra.survey_water_mileage DESC
            LIMIT 1
    </select>

    <select id="getAPPDataOverview" resultType="jylink.cpds.serviceModel.dto.APPDataOverviewDto" >
        SELECT
            COUNT(*) AS jobNum,
            COUNT( DISTINCT tunnel_id ) AS workFaceNum,
            SUM( holeNum ) AS holeNum,
            ROUND( sum(hole_distance), 2 ) AS drillHoleDistance,
            sum(
                TIMESTAMPDIFF( SECOND, CASE WHEN startTime &lt; firstDay THEN firstDay ELSE startTime END, CASE WHEN endTime > lastDay THEN lastDay ELSE endTime END )) AS workTime
        FROM
            (
                SELECT
                    dra.uuid,
                    dra.tunnel_id,
                    dra.work_name,
                    dra.survey_water_mileage,
                    MIN( ana.create_time ) startTime,
                    CONCAT( LAST_DAY( CONCAT( #{year}, '-', LPAD( #{month}, 2, '0' ), '-01' )), ' 23:59:59' ) AS lastDay,
                    CONCAT( #{year}, '-', LPAD( #{month}, 2, '0' ), '-01 00:00:00' ) AS firstDay,
                    maxAna.`status`,
                    COUNT( DISTINCT hole.uuid ) AS holeNum,
                    acc.hole_distance AS hole_distance,
                    CASE

                        WHEN maxAna.STATUS = 3 THEN
                            NOW() ELSE IFNULL(
                            IFNULL(
                                    maxAna.end_time,
                                    MAX( hole.end_drill_time )),
                            DATE_ADD( MIN( ana.create_time ), INTERVAL 15 DAY ))
                        END AS endTime
                FROM
                    tfs_drain_accounts dra
                        LEFT JOIN tfs_analysis_works ana ON ana.del_flag = 0
                        AND ana.drain_id = dra.uuid
                        LEFT JOIN (
                        SELECT
                            r1.drain_id,
                            r1.`status`,
                            r1.end_time
                        FROM
                            tfs_analysis_works r1
                        WHERE
                            NOT EXISTS (
                                    SELECT
                                        1
                                    FROM
                                        tfs_analysis_works r2
                                    WHERE
                                        r1.drain_id = r2.drain_id
                                      AND r2.create_time > r1.create_time
                                      AND r1.del_flag = 0
                                      AND r1.del_flag = 0
                                )
                        GROUP BY
                            drain_id
                    ) maxAna ON maxAna.drain_id = dra.uuid
                        LEFT JOIN tfs_hole_details hole ON hole.del_flag = 0
                        AND hole.drain_id = dra.uuid
                        LEFT JOIN ( SELECT sum( hole_distance ) AS hole_distance, drain_id FROM tfs_acceptance_checks WHERE del_flag = 0 AND org_code = #{orgCode} GROUP BY drain_id ) acc ON acc.drain_id = dra.uuid
                WHERE
                    dra.org_code = #{orgCode}
                  AND dra.del_flag = 0
                  AND ana.`status` IS NOT NULL
                  AND NOT EXISTS ( SELECT 1 FROM tfs_history_analysis_works his WHERE his.org_code = #{orgCode} AND his.del_flag = 0 AND his.hole_detail_id = hole.uuid )
                GROUP BY
                    dra.uuid
            ) t
        WHERE
            ( YEAR ( startTime ) = #{year} AND MONTH ( startTime ) = #{month} )
           OR (
            YEAR ( endTime ) = #{year}
            AND MONTH ( endTime ) = #{month})
    </select>

    <select id="getDrillOverviewDetail" resultType="jylink.cpds.serviceModel.dto.DrillOverviewDetailDto">
        SELECT
            *
        FROM
            (
                SELECT
                    dra.uuid AS drainId,
                    dra.tunnel_id,
                    dra.work_name,
                    dra.survey_water_mileage,
                    MIN( ana.create_time ) startTime,
                    maxAna.`status`,
                    CASE

                        WHEN maxAna.STATUS = 3 THEN
                            NOW() ELSE IFNULL(
                            IFNULL(
                                    maxAna.end_time,
                                    MAX( hole.end_drill_time )),
                            DATE_ADD( MIN( ana.create_time ), INTERVAL 15 DAY ))
                        END AS endTime
                FROM
                    tfs_drain_accounts dra
                        LEFT JOIN tfs_analysis_works ana ON ana.del_flag = 0
                        AND ana.drain_id = dra.uuid
                        LEFT JOIN (
                        SELECT
                            r1.drain_id,
                            r1.`status`,
                            r1.end_time
                        FROM
                            tfs_analysis_works r1
                        WHERE
                            NOT EXISTS (
                                    SELECT
                                        1
                                    FROM
                                        tfs_analysis_works r2
                                    WHERE
                                        r1.drain_id = r2.drain_id
                                      AND r2.create_time > r1.create_time
                                      AND r1.del_flag = 0
                                      AND r1.del_flag = 0
                                )
                        GROUP BY
                            drain_id
                    ) maxAna ON maxAna.drain_id = dra.uuid
                        LEFT JOIN tfs_hole_details hole ON hole.del_flag = 0
                        AND hole.drain_id = dra.uuid
                WHERE
                    dra.org_code = #{orgCode}
                  AND dra.del_flag = 0
                  AND ana.`status` IS NOT NULL
                  AND NOT EXISTS ( SELECT 1 FROM tfs_history_analysis_works his WHERE his.org_code = #{orgCode} AND his.del_flag = 0 AND his.hole_detail_id = hole.uuid )
                GROUP BY
                    dra.uuid
            ) t
        WHERE
            ( YEAR ( startTime ) = #{year} AND MONTH ( startTime ) = #{month} )
           OR (
            YEAR ( endTime ) = #{year}
            AND MONTH ( endTime ) = #{month})
        ORDER BY
            endTime DESC
    </select>

</mapper>
