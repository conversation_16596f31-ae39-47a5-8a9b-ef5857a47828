<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IAnalysisHistoryWorkDao">

    <resultMap type="jylink.cpds.domain.AnalysisHistoryWork" id="AnalysisHistoryWorkInstance">
        <id property="id" column="uuid"/>
        <result property="holeDetailId" column="hole_detail_id"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="hkOrgCode" column="hk_org_code"/>
        <result property="indexCode" column="index_code"/>
        <result property="channel" column="channel"/>
        <result property="cameraName" column="camera_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="userId" column="user_id"/>
        <result property="status" column="status"/>
    </resultMap>

    <!-- 添加数据 -->
    <insert id="add">
        insert
        into tfs_history_analysis_works
        (uuid,
         hole_detail_id,
         org_code,
         org_name,
         hk_org_code,
         index_code,
         channel,
         camera_name,
         start_time,
         end_time,
         create_time,
         del_flag,
         user_id,
         status)
        values (#{item.id},
                #{item.holeDetailId},
                #{item.orgCode},
                #{item.orgName},
                #{item.hkOrgCode},
                #{item.indexCode},
                #{item.channel},
                #{item.cameraName},
                #{item.startTime},
                #{item.endTime},
                #{item.createTime},
                #{item.delFlag},
                #{item.userId},
                #{item.status})
    </insert>

    <!-- 根据验收表Id查询数据 -->
    <select id="getByHoleDetailId" resultMap="AnalysisHistoryWorkInstance">
        select 
        uuid,
        hole_detail_id,
        org_code,
        org_name,
        hk_org_code,
        index_code,
        channel,
        camera_name,
        start_time,
        end_time,
        create_time,
        del_flag,
        user_id,
        diy_column,
        status
        from tfs_history_analysis_works
        where hole_detail_id = #{holeDetailId}
        and del_flag = 0
        order by create_time desc
    </select>

    <!-- 根据验收表Id集合查询数据 -->
    <select id="getByHoleDetailIds" resultMap="AnalysisHistoryWorkInstance">
        select uuid,
        hole_detail_id,
        org_code,
        org_name,
        hk_org_code,
        index_code,
        channel,
        camera_name,
        start_time,
        end_time,
        create_time,
        del_flag,
        user_id,
        status
        from tfs_history_analysis_works
        where hole_detail_id in (<foreach collection="holeDetailIds" item="holeDetailId" separator=",">#{holeDetailId}</foreach> )
        and del_flag = 0
        order by create_time desc
    </select>

    <!-- 根据Id查询数据 -->
    <select id="getById" resultMap="AnalysisHistoryWorkInstance">
        select uuid,
               hole_detail_id,
               org_code,
               org_name,
               hk_org_code,
               index_code,
               channel,
               camera_name,
               start_time,
               end_time,
               create_time,
               del_flag,
               user_id,
               status
        from tfs_history_analysis_works
        where uuid = #{id}
          and del_flag = 0
    </select>

    <!-- 删除数据 -->
    <update id="deleteById">
        update
        tfs_history_analysis_works
        set del_flag = 1
        where uuid=#{id} and org_code=#{orgCode} and del_flag = 0
    </update>

    <!-- 根据Id查询数据是否存在 -->
    <select id="anyById" resultType="boolean">
        select (
        select count(*) from tfs_history_analysis_works where uuid = #{id} and del_flag = 0
        ) > 0;
    </select>

    <!-- 根据Id查询数据是否存在 -->
    <select id="anyByHoleDetailId" resultType="boolean">
        select (
        select count(*) from tfs_history_analysis_works where hole_detail_id = #{holeDetailId} and del_flag = 0
        ) > 0;
    </select>

    <!-- 更新数据 -->
    <update id="updateStatus">
        update tfs_history_analysis_works
        set
        status = #{status}
        where
        uuid = #{id} and del_flag = 0
    </update>

    <!-- 更新详情id -->
    <update id="updateHoleDetailId">
        update tfs_history_analysis_works
        set
        hole_detail_id = #{holeDetailId}
        where
        uuid = #{id} and del_flag = 0
    </update>

    <!-- 查询是否有正常结束的 -->
    <select id="anyNormalFinish" resultType="boolean">
        select (
        select count(*) from tfs_history_analysis_works where hole_detail_id = #{holeDetailId} and del_flag = 0 and status = #{status}
        ) > 0;
    </select>

    <!-- 根据孔表id集合删除数据 -->
    <delete id="deleteByHoleDetailIds">
        update tfs_history_analysis_works set del_flag = 1 where hole_detail_id in (<foreach collection="holeDetailIds" item="holeDetailId" separator=",">#{holeDetailId}</foreach>)
    </delete>
</mapper>