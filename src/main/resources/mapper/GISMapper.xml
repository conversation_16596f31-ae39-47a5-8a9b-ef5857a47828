<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IGISDao">
    <!--结果映射 -->
    <resultMap type="jylink.cpds.domain.GisWorkInfo" id="GisWorkInstance">
        <id property="id" column="uuid"/>
        <result property="workName" column="work_name"/>
        <result property="workCode" column="work_code"/>
        <result property="tunnelDistance" column="tunnel_distance"/>
        <result property="surveyWaterMileage" column="survey_water_mileage"/>
        <result property="drivingDistance" column="driving_distance"/>
        <result property="overDistance" column="over_distance"/>
        <result property="endDrillTime" column="end_drill_time"/>
        <result property="coalLayerKey" column="coal_layer_key"/>
    </resultMap>

    <!-- 实时任务结果映射 -->
    <resultMap type="jylink.cpds.domain.AnalysisWork" id="AnalysisWorkInstance">
        <id property="id" column="uuid"/>
        <result property="drainId" column="drain_id"/>
        <result property="ledId" column="led_id"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="hkOrgCode" column="hk_org_code"/>
        <result property="indexCode" column="index_code"/>
        <result property="channel" column="channel"/>
        <result property="cameraName" column="camera_name"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="userId" column="user_id"/>
        <result property="status" column="status"/>
    </resultMap>

    <!-- 台账结果映射 -->
    <resultMap type="jylink.cpds.domain.DrainAccount" id="DrainAccountInstance">
        <id property="id" column="uuid"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tunnelId" column="tunnel_id"/>
        <result property="checkPlanId" column="check_plan_id"/>
        <result property="workName" column="work_name"/>
        <result property="workCode" column="work_code"/>
        <result property="surveyWaterMileage" column="survey_water_mileage"/>
        <result property="surveyWaterDate" column="survey_water_date"/>
        <result property="isAddAcceptanceCheck" column="is_add_acceptance_check"/>
        <result property="isAddTransferDetection" column="is_add_transfer_detection"/>
        <result property="isAddAllowableDriving" column="is_add_allowable_driving"/>
        <result property="isAddSummaryNotice" column="is_add_summary_notice"/>
        <result property="isViewed" column="is_viewed"/>
        <result property="status" column="status"/>
        <result property="uploadTime" column="upload_time"/>
        <result property="userId" column="user_id"/>
        <result property="isAccomplish" column="is_accomplish"/>
    </resultMap>

    <resultMap type="jylink.cpds.serviceModel.gis.RegionDto" id="RegionDto">
        <id property="regionCode" column="regionCode"/>
        <result property="regionName" column="regionName"/>
        <result property="totalMine" column="totalMine"/>
        <result property="joinMine" column="joinMine"/>
        <association property="waterType" javaType="jylink.cpds.serviceModel.gis.WaterTypeDto">
            <result property="simple" column="simple"/>
            <result property="medium" column="medium"/>
            <result property="complex" column="complex"/>
            <result property="excomplex" column="excomplex"/>
            <result property="unknown" column="unknown"/>
        </association>
    </resultMap>

    <!--param-->
    <resultMap type="jylink.cpds.serviceModel.gis.HoleParameter" id="parameters">
        <result property="drainId" column="drainId"/>
        <result property="workFaceId" column="workFaceId"/>
        <result property="workName" column="workName"/>
        <result property="surveyWaterMileage" column="surveyWaterMileage"/>
        <result property="status" column="status"/>
        <result property="endTime" column="endTime"/>
        <result property="startTime" column="startTime"/>
        <result property="abNormals" column="abNormals"/>
        <result property="workNo" column="workNo"/>
        <result property="gisTunnelId" column="gisTunnelId"/>
        <result property="mileageDesc" column="mileageDesc" />
        <collection property="children" javaType="java.util.ArrayList" column="drainId" ofType="allParametersChildren"
                    select="getAllParametersChildren"/>
    </resultMap>

    <!--param-->
    <resultMap type="jylink.cpds.serviceModel.gis.HoleParameter" id="allParameters">
        <result property="drainId" column="drainId"/>
        <result property="tunnelId" column="tunnelId"/>
        <result property="workName" column="workName"/>
        <result property="surveyWaterMileage" column="surveyWaterMileage"/>
        <result property="status" column="status"/>
        <result property="endTime" column="endTime"/>
        <result property="startTime" column="startTime"/>
        <result property="abNormals" column="abNormals"/>
        <result property="workNo" column="workNo"/>

        <collection property="children" javaType="java.util.ArrayList" column="drainId" ofType="allParametersChildren"
                    select="getAllParametersChildren"/>
    </resultMap>
    <resultMap type="jylink.cpds.serviceModel.gis.HoleParameter$Children" id="allParametersChildren">
        <result property="id" column="id"/>
        <result property="holeAzimuth" column="holeAzimuth"/>
        <result property="conclusionHoleDistance" column="conclusionHoleDistance"/>
        <result property="holeNumber" column="holeNumber"/>
        <result property="holeName" column="holeName"/>
        <result property="holeObliquity" column="holeObliquity"/>
        <result property="videoId" column="videoId"/>
        <result property="number" column="number"/>
        <result property="holeDistance" column="holeDistance"/>
        <result property="analysisHoleDistance" column="analysisHoleDistance"/>
        <result property="reportHoleDistance" column="reportHoleDistance"/>
        <result property="approvalId" column="approvalId"/>
        <result property="analyzing" column="analyzing"/>
    </resultMap>

    <!-- 岩性分类实体 -->
    <resultMap type="jylink.cpds.serviceModel.dto.RockCharacterDto" id="RockCharacterDto">
        <result property="from" column="from"/>
        <result property="to" column="to"/>
        <result property="name" column="name"/>
        <result property="abnormalType" column="abnormal_type"/>
        <result property="poleNo" column="pole_no"/>
        <result property="lastPoleNo" column="lastPoleNo"/>
        <result property="reportUserName" column="report_user_name"/>
        <result property="reportTime" column="report_time"/>
    </resultMap>

    <!-- 岩性分段查询 -->
    <resultMap type="jylink.cpds.serviceModel.dto.CoalRockSegmentationDto" id="CoalRockSegmentationInstance">
        <id property="id" column="uuid"/>
        <result property="azimuth" column="hole_azimuth"/>
        <result property="holeNo" column="hole_no"/>
        <result property="holeDetailId" column="hole_detail_id"/>
        <result property="obliquity" column="hole_obliquity"/>
        <result property="holeDistance" column="hole_distance"/>
        <result property="poleLength" column="pole_length"/>
        <result property="poleNumber" column="pole_number"/>
        <result property="abnormalLocation" column="abnormal_location"/>
        <result property="abnormalType" column="abnormal_type"/>
    </resultMap>

    <!-- 根据探水设计Id组合查询数据 -->
    <select id="getByTunnelIds" resultMap="DrainAccountInstance">
        SELECT
        uuid,
        org_code,
        org_name,
        create_time,
        del_flag,
        tunnel_id,
        check_plan_id,
        work_name,
        work_code,
        survey_water_mileage,
        survey_water_date,
        is_add_acceptance_check,
        is_add_transfer_detection,
        is_add_allowable_driving,
        is_add_summary_notice,
        STATUS,
        is_viewed,
        upload_time,
        user_id,
        is_accomplish
        FROM
        tfs_drain_accounts
        WHERE
        del_flag = 0
        AND tunnel_id IN (<foreach collection="ids" separator="," item="id">#{id}</foreach> )
        ORDER BY
        survey_water_date DESC
    </select>

    <!--根据orgcode查询数据-->
    <select id="getByOrgCode" resultMap="GisWorkInstance">
        SELECT
        *
        FROM
        (
        SELECT
        a.uuid,
        a.coal_layer_key,
        a.work_name,
        b.detection_status
        FROM
        tfs_work_face a
        LEFT OUTER JOIN tfs_tunnel_designs b ON a.uuid = b.work_face_id
        WHERE
        a.org_code = #{orgCode}
        and a.del_flag = 0
        <if test="coalLayerKey != null and !coalLayerKey.isEmpty()" >
            and a.coal_layer_key = #{coalLayerKey}
        </if>
        ) t
        WHERE
        ifnull( t.detection_status, 0 )=0
        ORDER BY
        t.work_name
    </select>

    <!--获取工作面距离-->
    <select id="getWorkDistanceByOrgCode" resultMap="GisWorkInstance">
        SELECT
        *
        FROM
        (
        SELECT distinct
        d.uuid,
        d.work_name,
        b.work_code,
        d.tunnel_distance,
        CASE
        WHEN a.survey_water_mileage IS NULL THEN
        0 ELSE a.survey_water_mileage
        END survey_water_mileage,
        c.driving_distance,
        c.over_distance
        FROM
        tfs_tunnel_designs b
        inner join tfs_work_face d on d.uuid=b.work_face_id
        LEFT OUTER JOIN tfs_drain_accounts a ON a.tunnel_id = b.uuid
        LEFT OUTER JOIN tfs_transfer_detections c ON a.uuid = c.drain_id
        WHERE
        b.del_flag = 0
        <if test="layer != null and !layer.isEmpty()" >
            and d.coal_layer_key=#{layer}
        </if>
        AND b.detection_status = 0
        AND b.org_code = #{orgCode}
        ORDER BY
        work_name,
        survey_water_mileage DESC

        ) t
        GROUP BY
        uuid
        ORDER BY
        work_name
    </select>
    <!--获取工作面里程-->
    <select id="getWorkMileageByOrgCode" resultMap="GisWorkInstance">
        select d.uuid,d.work_name
        from tfs_drain_accounts a
        inner join tfs_tunnel_designs b on a.tunnel_id=b.uuid and b.detection_status=0
        inner join tfs_work_face d on d.uuid=b.work_face_id
        where survey_water_mileage = (select max(survey_water_mileage) from tfs_drain_accounts where tunnel_id = a.tunnel_id and tfs_drain_accounts.org_code=#{orgCode} and tfs_drain_accounts.del_flag=0)
        <if test="layer != null and !layer.isEmpty()" >
            and d.coal_layer_key=#{layer}
        </if>
        and a.del_flag=0
        and d.del_flag=0
        order by d.work_name
    </select>
    <!--根据设计ids获取里列表-->
    <select id="getMileageByWorkInfoIds" resultMap="GisWorkInstance">
        select e.uuid,e.work_name,a.survey_water_mileage,d.end_drill_time
        from tfs_drain_accounts a
        inner join tfs_tunnel_designs b on a.tunnel_id=b.uuid and b.detection_status=0
        inner join (select drain_id,max(end_drill_time) end_drill_time from tfs_hole_details group by drain_id) d on
        a.uuid=d.drain_id
        inner join tfs_work_face e on e.uuid=b.work_face_id
        where a.org_code=#{orgCode} and a.del_flag=0
        and e.uuid in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
        order by e.work_name,a.survey_water_mileage
    </select>


    <select id="getAnalysisWorksByDrainIds" resultMap="AnalysisWorkInstance">
        select * from tfs_analysis_works where drain_id in (<foreach collection="drainIds" separator="," item="drainId">
        #{drainId}</foreach>) and del_flag = 0
    </select>

    <select id="getWaterTypeDistribution" resultMap="RegionDto">
        <choose>
            <when test="type=='province'">
                select tmp.*,(tmp.simple+tmp.medium+tmp.complex+tmp.excomplex+tmp.unknown) as totalMine
                from (
                select
                (select count(1)
                from tfs_mine_info
                where del_flag = 0 and CONVERT(org_code USING utf8) COLLATE utf8_general_ci in
                (select cmpi_dmpro from dcm_mine_info_mj t where t.Mine_CityZone_CODE = dcm_mine_info_mj.Mine_CityZone_CODE  and t.cmpi_dmpro in (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> ))) as joinMine,
                    Mine_CityZone_CODE as regionCode,Mine_CityZone_Name as regionName,
                    sum(case HYDROGEOLOGICAL_TYPE_CODE when '10' then 1 else 0 end) simple,
                    sum(case HYDROGEOLOGICAL_TYPE_CODE when '20' then 1 else 0 end) medium,
                    sum(case HYDROGEOLOGICAL_TYPE_CODE when '30' then 1 else 0 end) complex,
                    sum(case HYDROGEOLOGICAL_TYPE_CODE when '40' then 1 else 0 end) excomplex,
                    sum(case HYDROGEOLOGICAL_TYPE_CODE when '100' then 1 else 0 end) unknown
                from dcm_mine_info_mj
                where Mine_ProvZone_CODE=#{code}
                and cmpi_dmpro in (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
                group by Mine_CityZone_CODE,Mine_CityZone_Name) tmp
            </when>
            <otherwise>
                select tmp.*,(tmp.simple+tmp.medium+tmp.complex+tmp.excomplex+tmp.unknown) as totalMine
                from (
                select
                    (select count(1)
                    from tfs_mine_info
                    where del_flag = 0 and CONVERT(org_code USING utf8) COLLATE utf8_general_ci in
                    (select cmpi_dmpro from dcm_mine_info_mj t where t.Mine_CityZone_CODE = dcm_mine_info_mj.Mine_CityZone_CODE  and t.cmpi_dmpro in (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> ))) as joinMine,
                    ZONE_COUNTY_ID_CODE as regionCode,ZONE_COUNTY_ID_NAME as regionName,
                    sum(case HYDROGEOLOGICAL_TYPE_CODE when '10' then 1 else 0 end) simple,
                    sum(case HYDROGEOLOGICAL_TYPE_CODE when '20' then 1 else 0 end) medium,
                    sum(case HYDROGEOLOGICAL_TYPE_CODE when '30' then 1 else 0 end) complex,
                    sum(case HYDROGEOLOGICAL_TYPE_CODE when '40' then 1 else 0 end) excomplex,
                    sum(case HYDROGEOLOGICAL_TYPE_CODE when '100' then 1 else 0 end) unknown
                from dcm_mine_info_mj
                where Mine_CityZone_CODE=#{code}
                and cmpi_dmpro in (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
                group by ZONE_COUNTY_ID_CODE,ZONE_COUNTY_ID_NAME ) tmp
            </otherwise>
        </choose>

    </select>
    <select id="getParameters" resultMap="parameters">
        SELECT * FROM (
        SELECT
        face.uuid workFaceId,
        face.gis_tunnel_id gisTunnelId,
        account.uuid drainId,
        account.work_name workName,
        account.survey_water_mileage surveyWaterMileage,
        CASE
        WHEN JSON_VALID(plan.diy_column ) = 1 THEN
        JSON_UNQUOTE(JSON_EXTRACT(plan.diy_column, '$.MileageDesc'))
        ELSE
        ''
        END
        as mileageDesc,
        CASE
        WHEN ((
        SELECT
        count( 1 )
        FROM
        tfs_analysis_works works
        WHERE
        works.drain_id = account.uuid
        AND works.del_flag = 0
        AND works.STATUS = 3
        ) >= 1
        ) THEN
        '1' ELSE '2'
        END STATUS
        FROM
        tfs_drain_accounts account
        INNER JOIN tfs_tunnel_designs designs ON account.tunnel_id = designs.uuid
        inner join tfs_work_face face on face.uuid = designs.work_face_id
        INNER JOIN tfs_analysis_works taw ON account.uuid = taw.drain_id
        left join tfs_check_plans plan on plan.uuid = account.check_plan_id and plan.del_flag = 0
        WHERE
        account.del_flag = 0
        AND account.org_code = #{orgCode}
        AND designs.del_flag = 0
        AND taw.del_flag = 0
        <if test="coalLayerKey != null and !coalLayerKey.isEmpty()" >
            and face.coal_layer_key = #{coalLayerKey}
        </if>
        <if test="workName != null and !workName.isEmpty()" >
            and face.work_name like concat('%',#{workName},'%')
        </if>
        <choose>
            <when test="endFlag">
                and designs.detection_status = 1
                and  (taw.status='1' or taw.status='5')
            </when>
            <otherwise>
                and designs.detection_status = 0
                and case
                when (select 1
                from tfs_analysis_works
                where status = '3'
                and drain_id = account.uuid AND del_flag = 0 LIMIT 1) is null then (taw.status = '1' or taw.status='5')
                else taw.status = '3' end
            </otherwise>
        </choose>
        ORDER BY
        account.survey_water_mileage DESC
        ) t
        GROUP BY
        workFaceId
    </select>

    <select id="getAllParameters" resultMap="allParameters">
        SELECT
        account.uuid drainId,
        face.uuid tunnelId,
        account.work_name workName,
        account.survey_water_mileage surveyWaterMileage,
        face.coal_layer_key coalLayerKey,
        plan.exploration_water_number workNo,
        CASE

        WHEN b.drilling_verdict > 0 THEN
        1 ELSE 0
        END abNormals,
        CASE

        WHEN ((
        SELECT
        count( 1 )
        FROM
        tfs_analysis_works works
        WHERE
        works.drain_id = account.uuid
        AND works.del_flag = 0
        AND works.STATUS = 3
        ) = 1
        ) THEN
        '1' ELSE '2'
        END STATUS,
        (
        SELECT
        end_drill_time
        FROM
        tfs_hole_details details
        WHERE
        details.drain_id = account.uuid
        AND details.end_drill_time IS NOT NULL
        AND details.del_flag = 0
        ORDER BY
        end_drill_time DESC
        LIMIT 1
        ) endTime,
        IFNULL((
        SELECT
        create_time
        FROM
        tfs_analysis_works ana
        WHERE
        ana.drain_id = account.uuid
        AND ana.del_flag = 0
        ORDER BY
        create_time ASC
        LIMIT 1
        ) ,(
        SELECT
        min(
        IFNULL( his.start_time, details.start_drill_time )) start_drill_time
        FROM
        tfs_hole_details details
        LEFT JOIN tfs_history_analysis_works his ON his.del_flag = 0
        AND his.hole_detail_id = details.uuid
        WHERE
        details.drain_id = account.uuid
        AND details.del_flag = 0
        ) ) startTime
        FROM
        tfs_drain_accounts account
        left join tfs_check_plans plan on plan.uuid = account.check_plan_id and plan.del_flag = 0
        INNER JOIN tfs_tunnel_designs designs ON account.tunnel_id = designs.uuid
        INNER JOIN tfs_work_face face ON face.uuid = designs.work_face_id
        LEFT OUTER JOIN (
        SELECT
        drain_id,
        sum( CASE WHEN drill_condition != 0 THEN 1 ELSE 0 END ) drilling_verdict
        FROM
        tfs_acceptance_checks
        WHERE
        org_code = #{orgCode}
        AND del_flag = 0
        GROUP BY
        drain_id
        ) b ON account.uuid = b.drain_id
        WHERE
        account.del_flag = 0
        AND designs.del_flag = 0
        AND account.org_code = #{orgCode}
        <if test="detectionStatus != null and !detectionStatus.isEmpty()">
            and designs.detection_status = #{detectionStatus}
        </if>
        <if test="coalLayerKey != null and !coalLayerKey.isEmpty()" >
            and face.coal_layer_key = #{coalLayerKey}
        </if>
        ORDER BY
        face.create_time DESC
    </select>

    <select id="getAllParametersChildren" resultMap="allParametersChildren">
      select uuid id,
       hole_azimuth holeAzimuth,
       approve_id approvalId,
       hole_distance                                                   conclusionHoleDistance,
       hole_no                                                         holeNumber,
       concat('钻孔 ', hole_no)                                          holeName,
       hole_obliquity holeObliquity,
       hole_detail_id                                                  videoId,
       (analysis_hole_distance / ((select details.hole_distance
                                   from tfs_check_plan_details details
                                   where details.uuid = checks.plan_hole_no_id
                                     and details.del_flag = 0) * 100)) number,
       case
         when (select details.hole_distance
               from tfs_hole_details details
               where details.uuid = checks.hole_detail_id
                 and details.del_flag = 0) is null then ''
         else (select details.hole_distance
               from tfs_hole_details details
               where details.uuid = checks.hole_detail_id
                 and details.del_flag = 0) end                         holeDistance,
       case
         when (select details.analysis_hole_distance
               from tfs_hole_details details
               where details.uuid = checks.hole_detail_id
                 and details.del_flag = 0) is null then ''
         else (select details.analysis_hole_distance
               from tfs_hole_details details
               where details.uuid = checks.hole_detail_id
                 and details.del_flag = 0) end                         analysisHoleDistance,
       case
         when (select details.report_hole_distance
               from tfs_hole_details details
               where details.uuid = checks.hole_detail_id
                 and details.del_flag = 0) is null then ''
         else (select details.report_hole_distance
               from tfs_hole_details details
               where details.uuid = checks.hole_detail_id
                 and details.del_flag = 0) end                         reportHoleDistance,
         (select details.status
               from tfs_hole_details details
               where details.uuid = checks.hole_detail_id
                 and details.del_flag = 0) analyzing
from tfs_acceptance_checks checks
where checks.del_flag = 0
      and checks.drain_id = #{drainId}
      order by hole_no asc
    </select>

    <select id="getAbNormal" resultMap="CoalRockSegmentationInstance">
        SELECT
            a.uuid,
            a.hole_azimuth,
            a.hole_no,
            a.hole_obliquity,
            a.hole_detail_id,
            b.hole_distance,
            d.pole_length,
            CEIL( b.hole_distance / d.pole_length ) AS pole_number,
            b.abnormal_location,
            b.abnormal_type
        FROM
            tfs_acceptance_checks a
            LEFT JOIN tfs_hole_details b ON a.hole_detail_id = b.uuid
            AND b.del_flag = 0
            LEFT JOIN tfs_drain_accounts c ON a.drain_id = c.uuid
            AND c.del_flag = 0
            LEFT JOIN tfs_tunnel_designs d ON d.uuid = c.tunnel_id
            AND d.del_flag = 0
        WHERE
            a.drain_id = #{drainId}
        order by a.hole_no
    </select>

    <select id="getPoleAbnormal" resultMap="RockCharacterDto">
        CREATE TEMPORARY TABLE `linshi_tables` (
            `id` INT ( 10 ) UNSIGNED NOT NULL AUTO_INCREMENT,
            hole_detail_id VARCHAR ( 36 ) NOT NULL,
            `pos` DECIMAL ( 5, 1 ) NOT NULL,
            `abnormal_type` INT ( 2 ) DEFAULT NULL,
            `name` VARCHAR ( 50 ) DEFAULT NULL,
            `poleNo` INT ( 10 ) DEFAULT NULL,
            `reportName` VARCHAR ( 50 ) DEFAULT NULL,
            `reportTime` datetime ( 0 ) DEFAULT NULL,
            PRIMARY KEY ( `id` )
        ) ENGINE = MEMORY;
        CREATE TEMPORARY TABLE `linshi_tables2` (
            `id` INT ( 10 ) UNSIGNED NOT NULL AUTO_INCREMENT,
            hole_detail_id VARCHAR ( 36 ) NOT NULL,
            `pos` DECIMAL ( 5, 1 ) NOT NULL,
            `abnormal_type` INT ( 2 ) DEFAULT NULL,
            `name` VARCHAR ( 50 ) DEFAULT NULL,
            `poleNo` INT ( 10 ) DEFAULT NULL,
            `reportName` VARCHAR ( 50 ) DEFAULT NULL,
            `reportTime` datetime ( 0 ) DEFAULT NULL,
            PRIMARY KEY ( `id` )
        ) ENGINE = MEMORY;
        INSERT INTO linshi_tables ( hole_detail_id, pos, abnormal_type, NAME, poleNo, reportName, reportTime ) SELECT
        t1.hole_detail_id,
        t1.pole_no * pole_length AS pos,
        abnormal_type,
        IFNULL( abnormal_name, '煤' ) AS NAME,
        t1.pole_no,
        t1.report_user_name,
        t1.report_time
        FROM
            tfs_hole_details_report t1
            INNER JOIN (
            SELECT
                max( pole_no ) AS pole_no,
                hole_detail_id
            FROM
                (
                SELECT
                    (
                        @i :=
                    CASE

                            WHEN @pre_hole_detail_id != hole_detail_id THEN
                            @i + 1
                            WHEN @pre_abnormal_type != IFNULL( abnormal_type, '' ) THEN
                            @i + 1 ELSE @i
                        END
                        ) rownum,
                        pole_no,
                        pole_length,
                        abnormal_type,
                        abnormal_name,
                        hole_detail_id,
                        (
                        @pre_abnormal_type := IFNULL( abnormal_type, '' )),
                        ( @pre_hole_detail_id := hole_detail_id ),
                        report_user_name,
                        report_time
                    FROM
                        tfs_hole_details_report,
                        ( SELECT @i := 0, @pre_abnormal_type := '-1', @pre_hole_detail_id := '-1' ) AS a
                    WHERE
                        drain_id = #{drainId}
                    ORDER BY
                        hole_detail_id,
                        pole_no
                    ) t2
                GROUP BY
                    hole_detail_id,
                    rownum
                ) t2 ON t1.pole_no = t2.pole_no
                AND t1.hole_detail_id = t2.hole_detail_id
            WHERE
                drain_id = #{drainId}
            ORDER BY
                t1.hole_detail_id,
                t1.pole_no;
            INSERT INTO linshi_tables2 ( hole_detail_id, pos, abnormal_type, NAME, poleNo, reportName, reportTime ) SELECT
            hole_detail_id,
            pos,
            abnormal_type,
            NAME,
            poleNo,
            reportName,
            reportTime
            FROM
                linshi_tables;
            SELECT
                a.hole_detail_id,
                a.abnormal_type,
                IFNULL( b.pos, 0 ) `from`,
                a.pos AS `to`,
                a.NAME,
                IFNULL( b.poleNo + 1, 1 ) pole_no,
                a.poleNo lastPoleNo,
                IFNULL(
                    a.reportName,(
                    SELECT
                        report_user_name
                    FROM
                        tfs_hole_details_report
                    WHERE
                        hole_detail_id = a.hole_detail_id
                        AND pole_no = 1
                        AND del_flag = 0
                    )) report_user_name,
                IFNULL(
                    DATE_FORMAT( a.reportTime, '%H时%i分%s秒' ),
                    (
                    SELECT
                        DATE_FORMAT( report_time, '%H时%i分%s秒' )
                    FROM
                        tfs_hole_details_report
                    WHERE
                        hole_detail_id = a.hole_detail_id
                        AND pole_no = 1
                        AND del_flag = 0
                    )) report_time
                FROM
                linshi_tables a
                LEFT OUTER JOIN linshi_tables2 b ON a.hole_detail_id = b.hole_detail_id
                AND a.id - 1 = b.id;
            DROP TEMPORARY TABLE
            IF
                EXISTS linshi_tables;
            DROP TEMPORARY TABLE
        IF
            EXISTS linshi_tables2;
    </select>

</mapper>
