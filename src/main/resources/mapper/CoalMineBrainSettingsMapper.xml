<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.ICoalMineBrainSettingDao">

    <resultMap type="jylink.cpds.domain.CoalMineBrainSetting" id="CoalMineBrainSettingMap">
        <result property="uuid" column="id" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
        <result property="port" column="port" jdbcType="INTEGER"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
        <result property="diyColumn" column="diy_column" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="CoalMineBrainSettingMap">
        select
          uuid, org_code, org_name, ip_address, port, user_name, password, create_time, del_flag, diy_column
        from tfs_coal_mine_brain_settings
        where uuid = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="CoalMineBrainSettingMap">
        select
          uuid, org_code, org_name, ip_address, port, user_name, password, create_time, del_flag, diy_column
        from tfs_coal_mine_brain_settings
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="CoalMineBrainSettingMap">
        select
          uuid, org_code, org_name, ip_address, port, user_name, password, create_time, del_flag, diy_column
        from tfs_coal_mine_brain_settings
        <where>
            del_flag = 0
            <if test="id != null and id != ''">
                and uuid = #{id}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                and ip_address = #{ipAddress}
            </if>
            <if test="port != null">
                and port = #{port}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="password != null and password != ''">
                and password = #{password}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" >
        insert into tfs_coal_mine_brain_settings(uuid,org_code, org_name, ip_address, port, user_name, password, create_time, del_flag)
        values (#{id},#{orgCode}, #{orgName}, #{ipAddress}, #{port}, #{userName}, #{password}, #{createTime}, #{delFlag})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tfs_coal_mine_brain_settings
        <set>
            <if test="orgCode != null and orgCode != ''">
                org_code = #{orgCode},
            </if>
            <if test="orgName != null and orgName != ''">
                org_name = #{orgName},
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                ip_address = #{ipAddress},
            </if>
            <if test="port != null">
                port = #{port},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
        </set>
        where uuid = #{id}
    </update>

    <!-- 逻辑删除 -->
    <update id="delete" >
        update tfs_coal_mine_brain_settings set del_flag = 1 where org_code = #{orgCode} and del_flag = 0
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from tfs_coal_mine_brain_settings where uuid = #{id}
    </delete>

    <!-- 查询数据是否存在 -->
    <select id="anyById" resultType="boolean">
        select (
                   select count(*) from tfs_coal_mine_brain_settings where org_code = #{orgCode} and del_flag = 0 and uuid = #{id}
               ) > 0;
    </select>

    <!-- 查询数据是否存在 -->
    <select id="anyByOrgCode" resultType="boolean">
        select (
                   select count(*) from tfs_coal_mine_brain_settings where org_code = #{orgCode} and del_flag = 0
               ) > 0;
    </select>

</mapper>