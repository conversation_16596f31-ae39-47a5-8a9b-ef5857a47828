<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IOperationMonitorDao">
    <delete id="delTunnelDesign">
        DELETE FROM tfs_warn_message WHERE tunnel_id = #{tunnelId} and org_code = #{orgCode};
        DELETE FROM tfs_history_warn_message WHERE tunnel_id = #{tunnelId} and org_code = #{orgCode};
        DELETE FROM tfs_bloc_abnm_upload WHERE abnm_id in (SELECT uuid FROM tfs_bloc_abnm_alarm WHERE data_id in (SELECT uuid FROM tfs_drain_accounts WHERE tunnel_id = #{tunnelId} and org_code = #{orgCode} UNION ALL SELECT uuid FROM tfs_check_plans WHERE tunnel_id = #{tunnelId} and org_code = #{orgCode}) union ALL SELECT uuid FROM tfs_bloc_abnm_alarm_his WHERE data_id in (SELECT uuid FROM tfs_drain_accounts WHERE tunnel_id = #{tunnelId} and org_code = #{orgCode} UNION ALL SELECT uuid FROM tfs_check_plans WHERE tunnel_id = #{tunnelId} and org_code = #{orgCode}));
        DELETE FROM tfs_bloc_abnm_alarm WHERE data_id in (SELECT uuid FROM tfs_drain_accounts WHERE tunnel_id = #{tunnelId} and org_code = #{orgCode} UNION ALL SELECT uuid FROM tfs_check_plans WHERE tunnel_id = #{tunnelId} and org_code = #{orgCode});
        DELETE FROM tfs_bloc_abnm_alarm_his WHERE data_id in (SELECT uuid FROM tfs_drain_accounts WHERE tunnel_id = #{tunnelId} and org_code = #{orgCode} UNION ALL SELECT uuid FROM tfs_check_plans WHERE tunnel_id = #{tunnelId} and org_code = #{orgCode});
        delete FROM tfs_full_video_list WHERE full_video_id in ( select uuid FROM tfs_history_videos where hole_detail_id in (select uuid from tfs_hole_details where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode}))) and org_code = #{orgCode}) and org_code = #{orgCode};
        delete from tfs_history_videos where hole_detail_id in (select uuid from tfs_hole_details where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode}))) and org_code = #{orgCode};
        delete from tfs_video_list where hole_detail_id in (select uuid from tfs_hole_details     where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode}))) and org_code = #{orgCode};
        delete from tfs_history_analysis_works where hole_detail_id in (select uuid from tfs_hole_details     where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode}))) and org_code = #{orgCode};
        delete from tfs_acceptance_checks where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode})) and org_code = #{orgCode};
        delete from tfs_acceptance_approves where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode})) and org_code = #{orgCode};
        delete from tfs_drain_account_operator where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode})) and org_code = #{orgCode};
        delete from tfs_person_workload where hole_detail_id in (select uuid from tfs_hole_details     where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode}))) and org_code = #{orgCode};
        delete from tfs_change_class where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode})) and org_code = #{orgCode};
        delete from tfs_allowable_notices where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode})) and org_code = #{orgCode};
        delete from tfs_stop_notices where plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode}) and org_code = #{orgCode};
        delete from tfs_survey_notices where plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode}) and org_code = #{orgCode};
        delete from tfs_transfer_detections where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode})) and org_code = #{orgCode};
        delete from tfs_analysis_works where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode})) and org_code = #{orgCode};
        delete from tfs_hole_details where drain_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode})) and org_code = #{orgCode};
        delete from tfs_sign_record where data_id in (select uuid from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode})) and org_code = #{orgCode};
        delete from tfs_drain_accounts where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode}) and org_code = #{orgCode};
        delete from tfs_sign_record where data_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode}) and org_code = #{orgCode};
        delete from tfs_check_plan_details where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode}) and org_code = #{orgCode};
        delete from tfs_check_plan_users where check_plan_id in  (select uuid from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode}) and org_code = #{orgCode};
        delete from tfs_log_record_detail where log_id in (SELECT uuid from tfs_log_record    where EXISTS(select 1 from tfs_check_plans where tunnel_id = #{tunnelId} and tunnel_id = tfs_log_record.tunnel_id and check_position = tfs_log_record.survey_water_mileage)) and org_code = #{orgCode};
        delete from tfs_log_record where EXISTS(select 1 from tfs_check_plans where tunnel_id = #{tunnelId} and tunnel_id = tfs_log_record.tunnel_id and check_position = tfs_log_record.survey_water_mileage) and org_code = #{orgCode};
        delete from tfs_check_plans where tunnel_id = #{tunnelId} and org_code = #{orgCode};
        delete from tfs_intelligent_hole where intelligent_design_id in (select uuid from tfs_intelligent_design where uuid = (select intelligent_design_id from tfs_tunnel_designs where uuid = #{tunnelId} and org_code = #{orgCode}) and org_code = #{orgCode});
        delete from tfs_intelligent_design where uuid = (select intelligent_design_id from tfs_tunnel_designs where uuid = #{tunnelId} and org_code = #{orgCode}) and org_code = #{orgCode};
        delete from tfs_hole_design_alters where tunnel_alter_id in (select uuid from tfs_design_alters where tunnel_id = #{tunnelId} and org_code = #{orgCode} )and org_code = #{orgCode};
        delete from tfs_design_alters where tunnel_id = #{tunnelId} and org_code = #{orgCode};
        delete from tfs_hole_designs where tunnel_id= #{tunnelId} and org_code = #{orgCode};
        delete from tfs_tunnel_designs where uuid = #{tunnelId} and org_code = #{orgCode};
    </delete>
    <delete id="delCheckPlan">
        DELETE FROM tfs_warn_message WHERE message_type in (1,5) and (tunnel_id,survey_water_mileage) = (SELECT tunnel_id,check_position FROM tfs_check_plans WHERE uuid = #{checkPlanId} and org_code = #{orgCode});
        DELETE FROM tfs_history_warn_message WHERE message_type in (1,5) and (tunnel_id,survey_water_mileage) = (SELECT tunnel_id,check_position FROM tfs_check_plans WHERE uuid = #{checkPlanId} and org_code = #{orgCode});
        DELETE FROM tfs_bloc_abnm_upload WHERE abnm_id in (SELECT uuid FROM tfs_bloc_abnm_alarm WHERE data_id = #{checkPlanId} and org_code = #{orgCode} union ALL SELECT uuid FROM tfs_bloc_abnm_alarm_his WHERE data_id = #{checkPlanId} and org_code = #{orgCode});
        DELETE FROM tfs_bloc_abnm_upload WHERE abnm_id in (SELECT uuid FROM tfs_bloc_abnm_alarm WHERE data_id = (SELECT uuid FROM tfs_drain_accounts WHERE check_plan_id = #{checkPlanId} and org_code = #{orgCode} ) union ALL SELECT uuid FROM tfs_bloc_abnm_alarm_his WHERE data_id = (SELECT uuid FROM tfs_drain_accounts WHERE check_plan_id = #{checkPlanId} and org_code = #{orgCode} ));
        DELETE FROM tfs_bloc_abnm_alarm WHERE data_id = #{checkPlanId} and org_code = #{orgCode};
        DELETE FROM tfs_bloc_abnm_alarm_his WHERE data_id = #{checkPlanId} and org_code = #{orgCode};
        DELETE FROM tfs_bloc_abnm_alarm WHERE data_id = (SELECT uuid FROM tfs_drain_accounts WHERE check_plan_id = #{checkPlanId} and org_code = #{orgCode});
        DELETE FROM tfs_bloc_abnm_alarm_his WHERE data_id = (SELECT uuid FROM tfs_drain_accounts WHERE check_plan_id = #{checkPlanId} and org_code = #{orgCode});
        delete FROM tfs_full_video_list WHERE full_video_id in (SELECT uuid FROM tfs_history_videos where hole_detail_id in (select uuid from tfs_hole_details where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId})) and org_code = #{orgCode} ) and org_code = #{orgCode};
        delete from tfs_history_videos where hole_detail_id in (select uuid from tfs_hole_details where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId})) and org_code = #{orgCode};
        delete from tfs_video_list where hole_detail_id in (select uuid from tfs_hole_details where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId})) and org_code = #{orgCode};
        delete from tfs_history_analysis_works where hole_detail_id in (select uuid from tfs_hole_details where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId})) and org_code = #{orgCode};
        delete from tfs_acceptance_checks where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId}) and org_code = #{orgCode};
        delete from tfs_acceptance_approves where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId}) and org_code = #{orgCode};
        delete from tfs_drain_account_operator where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId}) and org_code = #{orgCode};
        delete from tfs_person_workload where hole_detail_id in (select uuid from tfs_hole_details where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId})) and org_code = #{orgCode};
        delete from tfs_change_class where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId}) and org_code = #{orgCode};
        delete from tfs_allowable_notices where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId}) and org_code = #{orgCode};
        delete from tfs_stop_notices where plan_id = #{checkPlanId} and org_code = #{orgCode};
        delete from tfs_survey_notices where plan_id = #{checkPlanId} and org_code = #{orgCode};
        delete from tfs_transfer_detections where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId}) and org_code = #{orgCode};
        delete from tfs_analysis_works where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId}) and org_code = #{orgCode};
        delete from tfs_hole_details where drain_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId}) and org_code = #{orgCode};
        delete from tfs_sign_record where data_id in (select uuid from tfs_drain_accounts where check_plan_id = #{checkPlanId}) and org_code = #{orgCode};
        delete from tfs_sign_record where data_id = #{checkPlanId} and org_code = #{orgCode};
        delete from tfs_drain_accounts where check_plan_id = #{checkPlanId} and org_code = #{orgCode};
        delete from tfs_check_plan_details where check_plan_id = #{checkPlanId} and org_code = #{orgCode};
        delete from tfs_check_plan_users where check_plan_id = #{checkPlanId} and org_code = #{orgCode};
        delete from tfs_log_record_detail where log_id in (SELECT uuid from tfs_log_record WHERE EXISTS(select 1 from tfs_check_plans where uuid = #{checkPlanId} and tunnel_id = tfs_log_record.tunnel_id and check_position = tfs_log_record.survey_water_mileage)) and org_code = #{orgCode};
        delete from tfs_log_record WHERE EXISTS(select 1 from tfs_check_plans where uuid = #{checkPlanId} and tunnel_id = tfs_log_record.tunnel_id and check_position = tfs_log_record.survey_water_mileage) and org_code = #{orgCode};
        delete from tfs_check_plans where uuid = #{checkPlanId} and org_code = #{orgCode};
    </delete>

    <update id="deleteWorkFace">
        delete from tfs_geophysical_report where tunnel_id = #{workFaceId} and org_code = #{orgCode};
        delete from tfs_evacuate_warn      where tunnel_id = #{workFaceId} and org_code = #{orgCode};
        delete from tfs_evacuate_config    where tunnel_id = #{workFaceId} and org_code = #{orgCode};
        delete from tfs_tunnel_led_camera  where tunnel_id = #{workFaceId} and org_code = #{orgCode};
        delete from tfs_work_face          where uuid      = #{workFaceId} and org_code = #{orgCode};
    </update>
    <update id="setDefaultHole">
        update tfs_acceptance_checks set hole_detail_id = #{holeDetailId} where org_code = #{orgCode} and uuid = #{acceptanceCheckId}

    </update>
    <select id="getDefaultHole" resultType="String">
        select
                hole_detail_id
        from
                tfs_acceptance_checks where org_code = #{orgCode} and uuid = #{acceptanceCheckId}
    </select>

    <delete id="delHole">
        delete from tfs_full_video_list where full_video_id in (select uuid from tfs_history_videos where hole_detail_id = #{holeDetailId} and org_code = #{orgCode}) and org_code = #{orgCode};
        delete from tfs_history_analysis_works where hole_detail_id = #{holeDetailId} and org_code = #{orgCode};
        delete from tfs_video_list where hole_detail_id = #{holeDetailId} and org_code = #{orgCode};
        delete from tfs_history_videos where hole_detail_id = #{holeDetailId} and org_code = #{orgCode};
        delete from tfs_hole_details where uuid = #{holeDetailId} and org_code = #{orgCode};
    </delete>


</mapper>