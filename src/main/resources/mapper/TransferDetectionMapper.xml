<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.ITransferDetectionDao">
    <!--结果映射 -->
    <resultMap id="TransferDetectionInstance" type="jylink.cpds.domain.TransferDetection">
        <id property="id" column="uuid"/>
        <result property="drainId" column="drain_id"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="transferDate" column="transfer_date"/>
        <result property="workName" column="work_name"/>
        <result property="galleryHoleAzimuth" column="gallery_hole_azimuth"/>
        <result property="prospectingDate" column="prospecting_date"/>
        <result property="classNum" column="class_num"/>
        <result property="checkPosition" column="check_position"/>
        <result property="nextCheckPosition" column="next_check_position"/>
        <result property="overDistance" column="over_distance"/>
        <result property="drivingDistance" column="driving_distance"/>
        <result property="problems" column="problems"/>
        <result property="tfsMonitorName" column="tfs_monitor_name"/>
        <result property="safeName" column="safe_name"/>
        <result property="cjMonitorName" column="cj_monitor_name"/>
        <result property="checkName" column="check_name"/>
        <result property="drivedDistance" column="drived_distance"/>
        <result property="drivedClassNum" column="drived_class_num"/>
        <result property="drivedClassNumKey" column="drived_class_num_key"/>
        <result property="transferMonitorName" column="transfer_monitor_name"/>
        <result property="transferSafeName" column="transfer_safe_name"/>
        <result property="surplusDistance" column="surplus_distance"/>
        <result property="surplusClassNum" column="surplus_class_num"/>
        <result property="surplusClassNumKey" column="surplus_class_num_key"/>
        <result property="successionMonitorName" column="succession_monitor_name"/>
        <result property="successionSafeName" column="succession_safe_name"/>
        <result property="isViewed" column="is_viewed"/>
        <result property="status" column="status"/>
        <result property="uploadTime" column="upload_time"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="surveyWaterDate" column="survey_water_date"/>
        <result property="number" column="number"/>
        <result property="acceptanceDate" column="acceptance_date"/>
        <result property="leadershipInstructions" column="leadership_instructions"/>
        <result property="geodesySectionChiefName" column="geodesy_section_chief_name"/>
        <result property="chiefEngineerName" column="chief_engineer_name"/>
        <result property="prospectingClassStart" column="prospecting_class_start"/>
        <result property="prospectingDateStart" column="prospecting_date_start"/>
        <result property="prospectingClassEnd" column="prospecting_class_end"/>
        <result property="prospectingDateEnd" column="prospecting_date_end"/>
    </resultMap>
    <!--根据id进行查询-->
    <select id="getById" resultMap="TransferDetectionInstance">
          select
          uuid,
          drain_id,
          org_code,
          org_name,
          transfer_date,
          work_name,
          gallery_hole_azimuth,
          prospecting_date,
          class_num,
          check_position,
          next_check_position,
          over_distance,
          driving_distance,
          problems,
          tfs_monitor_name,
          safe_name,
          cj_monitor_name,
          check_name,
          drived_distance,
          drived_class_num,
          drived_class_num_key,
          transfer_monitor_name,
          transfer_safe_name,
          surplus_distance,
          surplus_class_num,
          surplus_class_num_key,
          succession_monitor_name,
          succession_safe_name,
          is_viewed,
          status,
          upload_time,
          create_time,
          del_flag,
          number,
          acceptance_date,
          leadership_instructions,
          geodesy_section_chief_name,
          chief_engineer_name,
          prospecting_class_start,
          prospecting_date_start,
          prospecting_class_end,
          prospecting_date_end
          from tfs_transfer_detections
          where uuid = #{id}  and del_flag = 0 and org_code=#{orgCode}
    </select>

    <!--根据台账id进行查询-->
    <select id="getByDrainId" resultMap="TransferDetectionInstance">
          select
          uuid,
          drain_id,
          org_code,
          org_name,
          transfer_date,
          work_name,
          gallery_hole_azimuth,
          prospecting_date,
          class_num,
          check_position,
          next_check_position,
          over_distance,
          driving_distance,
          problems,
          tfs_monitor_name,
          safe_name,
          cj_monitor_name,
          check_name,
          drived_distance,
          drived_class_num,
          drived_class_num_key,
          transfer_monitor_name,
          transfer_safe_name,
          surplus_distance,
          surplus_class_num,
          surplus_class_num_key,
          succession_monitor_name,
          succession_safe_name,
          is_viewed,
          status,
          upload_time,
          create_time,
          del_flag,
          number,
          DATE_FORMAT(acceptance_date,'%Y-%m-%d') acceptance_date,
          leadership_instructions,
          geodesy_section_chief_name,
          chief_engineer_name,
          prospecting_class_start,
          prospecting_date_start,
          prospecting_class_end,
          prospecting_date_end
          from tfs_transfer_detections
          where drain_id = #{drainId}  and del_flag = 0 and org_code=#{orgCode}
    </select>

    <!--根据orgCode进行查询-->
    <select id="getByOrgCode" resultMap="TransferDetectionInstance">
          select
          uuid,
          drain_id,
          org_code,
          org_name,
          transfer_date,
          work_name,
          gallery_hole_azimuth,
          prospecting_date,
          class_num,
          check_position,
          next_check_position,
          over_distance,
          driving_distance,
          problems,
          tfs_monitor_name,
          safe_name,
          cj_monitor_name,
          check_name,
          drived_distance,
          drived_class_num,
          drived_class_num_key,
          transfer_monitor_name,
          transfer_safe_name,
          surplus_distance,
          surplus_class_num,
          surplus_class_num_key,
          succession_monitor_name,
          succession_safe_name,
          is_viewed,
          status,
          upload_time,
          create_time,
          del_flag,
          number,
          acceptance_date,
          leadership_instructions,
          geodesy_section_chief_name,
          chief_engineer_name,
          prospecting_class_start,
          prospecting_date_start,
          prospecting_class_end,
          prospecting_date_end
          from tfs_transfer_detections as a
          where org_code = #{orgCode}  and del_flag = 0
          order by create_time desc
    </select>

    <!--根据停止工作面名称和探水时间查询-->
    <select id="getByWorkAndDate" resultMap="TransferDetectionInstance">
        select
        a.uuid,
        a.drain_id,
        a.org_code,
        a.org_name,
        a.transfer_date,
        a.work_name,
        a.gallery_hole_azimuth,
        a.prospecting_date,
        a.class_num,
        a.check_position,
        a.next_check_position,
        a.over_distance,
        a.driving_distance,
        a.problems,
        a.tfs_monitor_name,
        a.safe_name,
        a.cj_monitor_name,
        a.check_name,
        a.drived_distance,
        a.drived_class_num,
        a.drived_class_num_key,
        a.transfer_monitor_name,
        a.transfer_safe_name,
        a.surplus_distance,
        a.surplus_class_num,
        a.surplus_class_num_key,
        a.succession_monitor_name,
        a.succession_safe_name,
        a.is_viewed,
        a.status,
        a.upload_time,
        a.create_time,
        a.del_flag,
        a.number,
        a.acceptance_date,
        a.leadership_instructions,
        a.geodesy_section_chief_name,
        a.chief_engineer_name,
        a.prospecting_class_start,
        a.prospecting_date_start,
        a.prospecting_class_end,
        a.prospecting_date_end
        from tfs_transfer_detections as a,tfs_drain_accounts as b
        where a.org_code = #{orgCode}
        and a.del_flag = 0
        and a.drainId = b.uuid
        <if test="surveyWaterDate!=null and workName!=null">
            and DATE_FORMAT(b.survey_water_date,'%Y') =  #{surveyWaterDate}
            and a.work_name = #{workName}
        </if>
        <if test="surveyWaterDate==null and workName!=null">
            and a.work_name = #{workOrgName}
        </if>
        <if test="surveyWaterDate!=null and workName==null">
            and DATE_FORMAT(b.survey_water_date,'%Y') =  #{surveyWaterDate}
        </if>
        order by a.create_time desc
    </select>

    <!--添加应用实例-->
    <insert id="add">
        insert into tfs_transfer_detections (
          uuid,
          drain_id,
          org_code,
          org_name,
          prospecting_date,
          problems,
          prospecting_date_start,
          prospecting_class_start,
          prospecting_date_end,
          prospecting_class_end,
          acceptance_date,
          check_position,
          over_distance,
          <!--transfer_date,
          gallery_hole_azimuth,

          class_num,

          next_check_position,


          drived_distance,
          drived_class_num,
          drived_class_num_key,
          transfer_monitor_name,
          transfer_safe_name,
          surplus_distance,
          surplus_class_num,
          surplus_class_num_key,
          succession_monitor_name,
          succession_safe_name,

          -->
          work_name,
          driving_distance,
          tfs_monitor_name,
          safe_name,
          cj_monitor_name,
          check_name,
          number,
          leadership_instructions,
          geodesy_section_chief_name,
          chief_engineer_name,
          is_viewed,
          status,
          create_time,
          del_flag,
          geodesy_section_chief_id,
          check_id,
          chief_engineer_id,
          cj_monitor_id,
          safe_id,
          tfs_monitor_id
        )
        values (
        #{instance.id},
        #{instance.drainId},
        #{instance.orgCode},
        #{instance.orgName},
        #{instance.prospectingDate},
        #{instance.problems},
        #{instance.prospectingDateStart},
        #{instance.prospectingClassStart},
        #{instance.prospectingDateEnd},
        #{instance.prospectingClassEnd},
        #{instance.acceptanceDate},
        #{instance.checkPosition},
        #{instance.overDistance},
        <!--#{instance.transferDate},
        #{instance.galleryHoleAzimuth},
        #{instance.classNum},

        #{instance.nextCheckPosition},


        #{instance.drivedDistance},
        #{instance.drivedClassNum},
        #{instance.drivedClassNumKey},
        #{instance.transferMonitorName},
        #{instance.transferSafeName},
        #{instance.surplusDistance},
        #{instance.surplusClassNum},
        #{instance.surplusClassNumKey},
        #{instance.successionMonitorName},
        #{instance.successionSafeName},

        -->
        #{instance.workName},
        #{instance.drivingDistance},
        #{instance.tfsMonitorName},
        #{instance.safeName},
        #{instance.cjMonitorName},
        #{instance.checkName},
        #{instance.number},

        #{instance.leadershipInstructions},
        #{instance.geodesySectionChiefName},
        #{instance.chiefEngineerName},
        0,
        #{instance.status},
        #{instance.createTime},
        0,
        #{instance.geodesySectionChiefId},
        #{instance.checkId},
        #{instance.chiefEngineerId},
        #{instance.cjMonitorId},
        #{instance.safeId},
        #{instance.tfsMonitorId}
        )
    </insert>

    <!--添加信息实例-->
    <insert id="addMessage">
        insert into tfs_transfer_detections (
          uuid,
          drain_id,
          org_code,
          org_name,
          transfer_date,
          work_name,
          gallery_hole_azimuth,
          prospecting_date,
          class_num,
          check_position,
          next_check_position,
          over_distance,
          drived_distance,
          driving_distance,
          problems,
          surplus_distance,
          is_viewed,
          status,
          create_time,
          del_flag
        )
        values (
        #{instance.id},
        #{instance.drainId},
        #{instance.orgCode},
        #{instance.orgName},
        #{instance.transferDate},
        #{instance.workName},
        #{instance.galleryHoleAzimuth},
        #{instance.prospectingDate},
        #{instance.classNum},
        #{instance.checkPosition},
        #{instance.nextCheckPosition},
        #{instance.overDistance},
        #{instance.drivedDistance},
        #{instance.drivingDistance},
        #{instance.problems},
        #{instance.surplusDistance},
        0,
        0,
        #{instance.createTime},
        0
        )
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update tfs_transfer_detections
        set
        <!--transfer_date = #{instance.transferDate},
        gallery_hole_azimuth = #{instance.galleryHoleAzimuth},

        class_num = #{instance.classNum},
        check_position = #{instance.checkPosition},
        next_check_position = #{instance.nextCheckPosition},

         problems = #{instance.problems},
         drived_distance = #{instance.drivedDistance},
        drived_class_num = #{instance.drivedClassNum},
        drived_class_num_key = #{instance.drivedClassNumKey},
        transfer_monitor_name = #{instance.transferMonitorName},
        transfer_safe_name = #{instance.transferSafeName},
        surplus_distance = #{instance.surplusDistance},
        surplus_class_num = #{instance.surplusClassNum},
        surplus_class_num_key = #{instance.surplusClassNumKey},
        succession_monitor_name = #{instance.successionMonitorName},
        succession_safe_name= #{instance.successionSafeName}
        work_name = #{instance.workName},

        -->
        over_distance =  #{instance.overDistance},
        prospecting_date = #{instance.prospectingDate},
        prospecting_date_start = #{instance.prospectingDateStart},
        prospecting_class_start = #{instance.prospectingClassStart},
        prospecting_date_end = #{instance.prospectingDateEnd},
        prospecting_class_end = #{instance.prospectingClassEnd},
        driving_distance = #{instance.drivingDistance},
        tfs_monitor_name =#{instance.tfsMonitorName},
        safe_name = #{instance.safeName},
        cj_monitor_name = #{instance.cjMonitorName},
        check_name = #{instance.checkName},
        number = #{instance.number},
        status=#{instance.status},
        acceptance_date = #{instance.acceptanceDate},
        leadership_instructions = #{instance.leadershipInstructions},
        geodesy_section_chief_name = #{instance.geodesySectionChiefName},
        chief_engineer_name = #{instance.chiefEngineerName},
        geodesy_section_chief_id = #{instance.geodesySectionChiefId},
        check_id = #{instance.checkId},
        chief_engineer_id = #{instance.chiefEngineerId},
        cj_monitor_id = #{instance.cjMonitorId},
        safe_id = #{instance.safeId},
        tfs_monitor_id = #{instance.tfsMonitorId}
        where
        uuid = #{instance.id}
        and
        org_code = #{instance.orgCode}
        and
        del_flag = 0
    </update>

    <!-- 更新验收数据 -->
    <update id="updateMessage">
        update tfs_transfer_detections
        set
        next_check_position = #{instance.nextCheckPosition},
        driving_distance = #{instance.drivingDistance},
        problems = #{instance.problems}
        where
        drain_id = #{instance.drainId}
        and
        del_flag = 0
        and
        status = 5
    </update>

    <!-- 根据台账Id更新数据 -->
    <update id="updateDrainId">
        update tfs_transfer_detections
        set
        <!--transfer_date = #{instance.transferDate},
        gallery_hole_azimuth = #{instance.galleryHoleAzimuth},
        class_num = #{instance.classNum},
        check_position = #{instance.checkPosition},
        next_check_position = #{instance.nextCheckPosition},

        problems = #{instance.problems},
        drived_distance = #{instance.drivedDistance},
        drived_class_num = #{instance.drivedClassNum},
        drived_class_num_key = #{instance.drivedClassNumKey},
        transfer_monitor_name = #{instance.transferMonitorName},
        transfer_safe_name = #{instance.transferSafeName},
        surplus_distance = #{instance.surplusDistance},
        surplus_class_num = #{instance.surplusClassNum},
        surplus_class_num_key = #{instance.surplusClassNumKey},
        succession_monitor_name = #{instance.successionMonitorName},
        succession_safe_name= #{instance.successionSafeName},

        work_name = #{instance.workName},

        -->
        over_distance =  #{instance.overDistance},
        prospecting_date = #{instance.prospectingDate},
        prospecting_date_start = #{instance.prospectingDateStart},
        prospecting_class_start = #{instance.prospectingClassStart},
        prospecting_date_end = #{instance.prospectingDateEnd},
        prospecting_class_end = #{instance.prospectingClassEnd},
        driving_distance = #{instance.drivingDistance},
        tfs_monitor_name =#{instance.tfsMonitorName},
        safe_name = #{instance.safeName},
        cj_monitor_name = #{instance.cjMonitorName},
        check_name = #{instance.checkName},
        number = #{instance.number},
        acceptance_date = #{instance.acceptanceDate},
        leadership_instructions = #{instance.leadershipInstructions},
        geodesy_section_chief_name = #{instance.geodesySectionChiefName},
        chief_engineer_name = #{instance.chiefEngineerName},
        geodesy_section_chief_id = #{instance.geodesySectionChiefId},
        check_id = #{instance.checkId},
        chief_engineer_id = #{instance.chiefEngineerId},
        cj_monitor_id = #{instance.cjMonitorId},
        safe_id = #{instance.safeId},
        tfs_monitor_id = #{instance.tfsMonitorId}
        where
        drain_id = #{instance.drainId}
        and
        org_code = #{instance.orgCode}
        and
        del_flag = 0
    </update>

    <!-- 删除数据 -->
    <update id="delete">
        update tfs_transfer_detections set del_flag = 1
        where uuid = #{id}   and org_code = #{orgCode} and del_flag = 0;
    </update>

    <!-- 修改上报状态 -->
    <update id="upload">
        update tfs_transfer_detections
        set status = 4 , upload_time = #{instance.uploadTime}
        where uuid = #{instance.id} and org_code = #{instance.orgCode} and del_flag = 0
    </update>

    <!-- 根据台账id数据上报 -->
    <update id="drainUpload">
        update tfs_transfer_detections set status = 4,upload_time = #{uploadTime} where drain_id = #{drainId} and org_code = #{orgCode} and del_flag = 0;
    </update>

    <!-- 修改状态为上级单位已经查看 -->
    <update id="check">
        update tfs_transfer_detections
        set is_viewed = 1
        where uuid = #{id} and org_code = #{orgCode} and del_flag = 0 and is_viewed = 0;
    </update>

    <!-- 数据是否存在 -->
    <select id="anyById" resultType="boolean">
        select (
          select count(*) from tfs_transfer_detections where uuid = #{id} and org_code =#{orgCode} and del_flag = 0
        )>0;
    </select>

    <!-- 根据台账删除数据 -->
    <update id="deleteDrainId">
        update tfs_transfer_detections set del_flag = 1
        where drain_id = #{drainId}   and org_code = #{orgCode} and del_flag = 0;
    </update>

    <!-- 台账数据machineId是否存在 -->
    <select id="anyByDrainId" resultType="boolean">
        select (
          select count(*) from tfs_transfer_detections where drain_id = #{drainId} and org_code =#{orgCode} and del_flag = 0
        )>0;
    </select>

    <!--  -->
    <select id="getByDrainIds" resultMap="TransferDetectionInstance">
        select
          uuid,
          drain_id,
          org_code,
          org_name,
          transfer_date,
          work_name,
          gallery_hole_azimuth,
          prospecting_date,
          class_num,
          check_position,
          next_check_position,
          over_distance,
          driving_distance,
          problems,
          tfs_monitor_name,
          safe_name,
          cj_monitor_name,
          check_name,
          drived_distance,
          drived_class_num,
          drived_class_num_key,
          transfer_monitor_name,
          transfer_safe_name,
          surplus_distance,
          surplus_class_num,
          surplus_class_num_key,
          succession_monitor_name,
          succession_safe_name,
          is_viewed,
          status,
          upload_time,
          create_time,
          del_flag,
          number,
          acceptance_date,
          leadership_instructions,
          geodesy_section_chief_name,
          chief_engineer_name,
          prospecting_class_start,
          prospecting_date_start,
          prospecting_class_end,
          prospecting_date_end
          from
          tfs_transfer_detections
          where
          drain_id in (<foreach collection="drainIds" item="drainId" separator=",">#{drainId}</foreach> )
          and
          del_flag = 0;
    </select>

    <!-- 根据设计Id获取数据 -->
    <select id="getByTunnelId" resultMap="TransferDetectionInstance">
        SELECT
            transfer.uuid,
            transfer.drain_id,
            transfer.org_code,
            transfer.org_name,
            transfer.transfer_date,
            transfer.work_name,
            transfer.gallery_hole_azimuth,
            transfer.prospecting_date,
            transfer.class_num,
            transfer.check_position,
            transfer.next_check_position,
            transfer.over_distance,
            transfer.driving_distance,
            transfer.problems,
            transfer.tfs_monitor_name,
            transfer.safe_name,
            transfer.cj_monitor_name,
            transfer.check_name,
            transfer.drived_distance,
            transfer.drived_class_num,
            transfer.drived_class_num_key,
            transfer.transfer_monitor_name,
            transfer.transfer_safe_name,
            transfer.surplus_distance,
            transfer.surplus_class_num,
            transfer.surplus_class_num_key,
            transfer.succession_monitor_name,
            transfer.succession_safe_name,
            transfer.is_viewed,
            transfer.status,
            transfer.upload_time,
            transfer.create_time,
            transfer.del_flag,
            transfer.number,
            transfer.acceptance_date,
            transfer.leadership_instructions,
            transfer.geodesy_section_chief_name,
            transfer.chief_engineer_name,
            transfer.prospecting_class_start,
            transfer.prospecting_date_start,
            transfer.prospecting_class_end,
            transfer.prospecting_date_end
        FROM
            tfs_transfer_detections AS transfer
            LEFT JOIN tfs_drain_accounts AS account ON transfer.drain_id = account.uuid
            LEFT JOIN tfs_tunnel_designs AS design ON account.tunnel_id = design.uuid
        WHERE
            transfer.del_flag = 0
            AND transfer.org_code = #{orgCode}
            AND design.uuid = #{tunnelId}
        ORDER BY
            transfer.create_time
    </select>


    <!-- 修改审批状态 -->
    <update id="updateStatus">
        update tfs_transfer_detections
        set status = #{status}
        where uuid = #{id} and org_code = #{orgCode} and del_flag = 0
    </update>

    <!-- 获取综合展现大屏数据 -->
    <select id="getDisplayNum" resultMap="TransferDetectionInstance">
        SELECT
            a.check_position,
            a.over_distance,
            a.driving_distance,
            a.work_name
        FROM
            ( SELECT max( check_position ) AS check_position,
            work_name
            FROM tfs_transfer_detections
            WHERE
            del_flag = 0
            AND org_code = #{orgCode}
            AND `status` = 2
            GROUP BY work_name ) t,
            tfs_transfer_detections a
        WHERE
            a.check_position = t.check_position
            AND a.work_name = t.work_name
    </select>

</mapper>