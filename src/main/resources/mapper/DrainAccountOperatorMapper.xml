<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IDrainAccountOperatorDao">

    <resultMap type="jylink.cpds.domain.DrainAccountOperator" id="drainAccountOperator">
        <id property="id" column="uuid"></id>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="drainId" column="drain_id"></result>
        <result property="constructionTeamId" column="construction_team_id"></result>
        <result property="constructionTeam" column="construction_team"></result>
        <result property="constructionLeadingId" column="construction_leading_id"></result>
        <result property="constructionLeadingName" column="construction_leading_name"></result>
        <result property="constructionOrder" column="construction_order"></result>
        <result property="isOperator" column="is_operator"></result>
        <result property="changeClassId" column="change_class_id"/>
        <result property="delFlag" column="del_flag"></result>
        <result property="createTime" column="create_time"/>
        <result property="personType" column="person_type"/>
    </resultMap>

    <!--添加应用实例-->
    <insert id="add">
        insert into tfs_drain_account_operator(
        uuid,
        org_code,
        org_name,
        drain_id,
        construction_team_id,
        construction_team,
        construction_leading_id,
        construction_leading_name,
        construction_order,
        is_operator,
        change_class_id,
        del_flag,
        create_time
       )
        values
        <foreach collection="list" item="instance" separator=",">
        (   #{instance.id},
            #{instance.orgCode},
            #{instance.orgName},
            #{instance.drainId},
            #{instance.constructionTeamId},
            #{instance.constructionTeam},
            #{instance.constructionLeadingId},
            #{instance.constructionLeadingName},
            #{instance.constructionOrder},
            #{instance.isOperator},
            #{instance.changeClassId},
            0,
            #{instance.createTime}
        )
        </foreach>
    </insert>


    <!--根据台账ID查询数据-->
    <select id="getByDrainId" resultMap="drainAccountOperator">
        select
        uuid,
        org_code,
        org_name,
        drain_id,
        construction_team_id,
        construction_team,
        construction_leading_id,
        construction_leading_name,
        construction_order,
        is_operator,
        change_class_id,
        del_flag,
        person_type,
        create_time
        from
        tfs_drain_account_operator
        where
        drain_id=#{drainId}
        and
        org_code = #{orgCode}
        and del_flag = 0
        order by construction_order desc
    </select>

    <!--根据交接班ID查询数据-->
    <select id="getByChangeClassId" resultMap="drainAccountOperator">
        select
        uuid,
        org_code,
        org_name,
        drain_id,
        construction_team_id,
        construction_team,
        construction_leading_id,
        construction_leading_name,
        construction_order,
        is_operator,
        change_class_id,
        del_flag,
        person_type,
        create_time
        from
        tfs_drain_account_operator
        where
        change_class_id=#{changeClassId}
        and
        org_code = #{orgCode}
        and del_flag = 0
    </select>

    <!--根据交接班IDs查询数据-->
    <select id="getByChangeClassIds" resultMap="drainAccountOperator">
        select
        uuid,
        org_code,
        org_name,
        drain_id,
        construction_team_id,
        construction_team,
        construction_leading_id,
        construction_leading_name,
        construction_order,
        is_operator,
        change_class_id,
        del_flag,
        person_type,
        create_time
        from
        tfs_drain_account_operator
        where
        change_class_id in (<foreach collection="changeClassIds" separator="," item="changeClassId" >#{changeClassId}</foreach>)
        and del_flag = 0
    </select>

    <select id="getByDrainAndUserId" resultMap="drainAccountOperator">
        select
        uuid,
        org_code,
        org_name,
        drain_id,
        construction_team_id,
        construction_team,
        construction_leading_id,
        construction_leading_name,
        construction_order,
        is_operator,
        change_class_id,
        del_flag,
        person_type,
        create_time
        from
        tfs_drain_account_operator
        where
        drain_id=#{drainId}
        and
        construction_leading_id=#{userId}
        and
        org_code = #{orgCode}
        and del_flag = 0
        and is_operator=1
        order by construction_order desc
    </select>

    <update id="updateIsOperator">
        update tfs_drain_account_operator set is_operator=1
        where uuid=#{id}
        and del_flag = 0
    </update>

    <select id="getPersonInfo" resultType="jylink.cpds.serviceModel.dto.PersonTrackTimeDto">
        SELECT
            opera.construction_leading_id AS userId,
            opera.construction_leading_name AS perName,
            opera.change_class_id AS changeClassId,
            per.per_card_no AS perCardNo,
            opera.person_type
        FROM
            tfs_drain_account_operator opera
                LEFT JOIN tfs_special_pers per ON per.uuid = opera.construction_leading_id
        WHERE
            opera.drain_id = #{drainId}
        GROUP BY
            opera.change_class_id,
            opera.construction_leading_id
    </select>

    <select id="getDrainPersonInfo" resultType="jylink.cpds.serviceModel.dto.PersonTrackTimeDto">
        SELECT
            opera.construction_leading_id AS userId,
            opera.construction_leading_name AS perName,
            opera.change_class_id AS changeClassId,
            per.per_card_no AS perCardNo,
            opera.person_type
        FROM
            tfs_drain_account_operator opera
                LEFT JOIN tfs_special_pers per ON per.uuid = opera.construction_leading_id
        WHERE
            opera.drain_id = #{drainId}
        GROUP BY
            opera.construction_leading_id
    </select>

</mapper>
