<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IDrawingExchangesConfigDao">
    <resultMap type="jylink.cpds.domain.DrawingExchangesConfig" id="DrawingExchangesConfigMap">
        <result property="id" column="uuid" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="extendedFields" column="extended_fields" jdbcType="VARCHAR"/>
        <result property="diyColumn" column="diy_column" jdbcType="VARCHAR"/>
        <result property="exchangeFrequency" column="exchange_frequency" jdbcType="INTEGER"/>
        <result property="startDate" column="start_date" jdbcType="OTHER"/>
        <result property="endDate" column="end_date" jdbcType="TIMESTAMP"/>
        <result property="configOrgCode" column="config_org_code" jdbcType="VARCHAR"/>
        <result property="configOrgName" column="config_org_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个,主键查询-->
    <select id="queryById" resultMap="DrawingExchangesConfigMap">
        select uuid,
               org_code,
               org_name,
               del_flag,
               create_time,
               update_time,
               extended_fields,
               diy_column,
               exchange_frequency,
               start_date,
               end_date,
               config_org_code,
               config_org_name
        from tfs_drawing_exchanges_config
        where uuid = #{id}
          and del_flag = 0
          and org_code = #{orgCode}
    </select>

    <!--根据机构编码查询数据-->
    <select id="getByOrgCode" resultMap="DrawingExchangesConfigMap">
        select uuid,
               org_code,
               org_name,
               del_flag,
               create_time,
               update_time,
               extended_fields,
               diy_column,
               exchange_frequency,
               start_date,
               end_date,
               config_org_code,
               config_org_name
        from tfs_drawing_exchanges_config
        where del_flag = 0
          and org_code = #{orgCode}
        limit 1
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="DrawingExchangesConfigMap">
        select
        uuid, org_code, org_name, del_flag, create_time, update_time, extended_fields, diy_column, exchange_frequency,
        start_date, end_date, config_org_code, config_org_name from tfs_drawing_exchanges_config
        <where>
            <if test="id != null and id != ''">
                and uuid = #{id}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="delFlag != null">
                and del_flag = #{delFlag}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="extendedFields != null and extendedFields != ''">
                and extended_fields = #{extendedFields}
            </if>
            <if test="diyColumn != null and diyColumn != ''">
                and diy_column = #{diyColumn}
            </if>
            <if test="exchangeFrequency != null">
                and exchange_frequency = #{exchangeFrequency}
            </if>
            <if test="startDate != null">
                and start_date = #{startDate}
            </if>
            <if test="endDate != null">
                and end_date = #{endDate}
            </if>
            <if test="configOrgCode != null and configOrgCode != ''">
                and config_org_code = #{configOrgCode}
            </if>
            <if test="configOrgName != null and configOrgName != ''">
                and config_org_name = #{configOrgName}
            </if>
        </where>
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAllCount" resultType="long">
        select
        count(1)
        from tfs_drawing_exchanges_config
        <where>
            <if test="id != null and id != ''">
                and uuid = #{id}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="delFlag != null">
                and del_flag = #{delFlag}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="extendedFields != null and extendedFields != ''">
                and extended_fields = #{extendedFields}
            </if>
            <if test="diyColumn != null and diyColumn != ''">
                and diy_column = #{diyColumn}
            </if>
            <if test="exchangeFrequency != null">
                and exchange_frequency = #{exchangeFrequency}
            </if>
            <if test="startDate != null">
                and start_date = #{startDate}
            </if>
            <if test="endDate != null">
                and end_date = #{endDate}
            </if>
            <if test="configOrgCode != null and configOrgCode != ''">
                and config_org_code = #{configOrgCode}
            </if>
            <if test="configOrgName != null and configOrgName != ''">
                and config_org_name = #{configOrgName}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into tfs_drawing_exchanges_config(uuid, org_code, org_name, del_flag, create_time, update_time,
                                                 extended_fields, diy_column, exchange_frequency, start_date, end_date,
                                                 config_org_code, config_org_name)
        values (#{id}, #{orgCode}, #{orgName}, #{delFlag}, #{createTime}, #{updateTime}, #{extendedFields},
                #{diyColumn}, #{exchangeFrequency}, #{startDate}, #{endDate}, #{configOrgCode}, #{configOrgName})
    </insert>

    <!--批量添加-->
    <insert id="addAll">
        insert into tfs_drawing_exchanges_config
            (
             uuid,
             org_code,
             org_name,
             del_flag,
             create_time,
             extended_fields,
             diy_column,
             exchange_frequency,
             start_date,
             end_date,
             config_org_code,
             config_org_name)
        values
        <foreach collection="list" separator="," item="instance">
               (
                #{instance.id},
                #{instance.orgCode},
                #{instance.orgName},
                0,
                #{instance.createTime},
                #{instance.extendedFields},
                #{instance.diyColumn},
                #{instance.exchangeFrequency},
                #{instance.startDate},
                #{instance.endDate},
                #{instance.configOrgCode},
                #{instance.configOrgName}
                )
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tfs_drawing_exchanges_config
        <set>
            <if test="orgCode != null and orgCode != ''">
                org_code = #{orgCode},
            </if>
            <if test="orgName != null and orgName != ''">
                org_name = #{orgName},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="extendedFields != null and extendedFields != ''">
                extended_fields = #{extendedFields},
            </if>
            <if test="diyColumn != null and diyColumn != ''">
                diy_column = #{diyColumn},
            </if>
            <if test="exchangeFrequency != null">
                exchange_frequency = #{exchangeFrequency},
            </if>
            <if test="startDate != null">
                start_date = #{startDate},
            </if>
            <if test="endDate != null">
                end_date = #{endDate},
            </if>
            <if test="configOrgCode != null and configOrgCode != ''">
                config_org_code = #{configOrgCode},
            </if>
            <if test="configOrgName != null and configOrgName != ''">
                config_org_name = #{configOrgName},
            </if>
        </set>
        where uuid = #{id} and del_flag = 0
    </update>

    <!--通过主键逻辑删除-->
    <update id="deleteById">
        update tfs_drawing_exchanges_config
        set del_flag = 1
        where uuid = #{id}
          and del_flag = 0
    </update>

    <delete id="deleteByConfigOrgCode">
        delete from tfs_drawing_exchanges_config where config_org_code = #{orgCode}
    </delete>

</mapper>
