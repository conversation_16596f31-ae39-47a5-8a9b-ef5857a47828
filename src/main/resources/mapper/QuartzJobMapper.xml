<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IQuartzJobInfoDao">

    <!--结果映射 -->
    <resultMap type="jylink.cpds.domain.QuartzJobInfo" id="QuartzJobInfoInstance">
        <id property="id" column="uuid" />
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="jobName" column="job_name"/>
        <result property="jobGroup" column="job_group"/>
        <result property="jobClassName" column="job_class_name"/>
        <result property="tiggerName" column="trigger_Name"/>
        <result property="tiggerGroupName" column="trigger_group_name"/>
        <result property="checkPlanId" column="check_plan_id"/>
        <result property="isGround" column="is_ground"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!--添加应用实例-->
    <insert id="add">
        insert into tfs_quartz_job (
        uuid,
        org_code,
        org_name,
        job_name,
        job_group,
        job_class_name,
        trigger_Name,
        trigger_group_name,
        check_plan_id,
        is_ground,
        create_time,
        del_flag
        )
        values (
        #{instance.id},
        #{instance.orgCode},
        #{instance.orgName},
        #{instance.jobName},
        #{instance.jobGroup},
        #{instance.jobClassName},
        #{instance.tiggerName},
        #{instance.tiggerGroupName},
        #{instance.checkPlanId},
        #{instance.isGround},
        #{instance.createTime},
        #{instance.delFlag}
        )
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update tfs_quartz_job
        set
        is_ground = #{isGround}
        where
        check_plan_id = #{checkPlanId} and del_flag = 0 and  org_code = #{orgCode}
    </update>

</mapper>