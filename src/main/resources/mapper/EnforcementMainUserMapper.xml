<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IEnforcementMainUserDao">

    <resultMap type="jylink.cpds.domain.EnforcementMainUser" id="EnforcementMainUserMap">
        <result property="id" column="uuid" jdbcType="VARCHAR"/>
        <result property="enforcementMainId" column="enforcement_main_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="enforcementType" column="enforcement_type" jdbcType="VARCHAR"/>
        <result property="enforcementContent" column="enforcement_content" jdbcType="VARCHAR"/>
        <result property="enforcementRequirements" column="enforcement_requirements" jdbcType="VARCHAR"/>
        <result property="enforcementBasis" column="enforcement_basis" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="userIdentity" column="user_identity" jdbcType="INTEGER"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询单个,主键查询-->
    <select id="queryById" resultMap="EnforcementMainUserMap">
        select
            uuid,
            enforcement_main_id,
            user_id,
            user_name,
            enforcement_type,
            enforcement_content,
            enforcement_requirements,
            enforcement_basis,
            status,
            user_identity,
            org_code,
            org_name,
            create_time
        from tfs_enforcement_main_user
        where uuid = #{id}
    </select>

    <!--查询单个,主键查询-->
    <select id="getByMainId" resultMap="EnforcementMainUserMap">
        select
            uuid,
            enforcement_main_id,
            user_id,
            user_name,
            enforcement_type,
            enforcement_content,
            enforcement_requirements,
            enforcement_basis,
            status,
            user_identity,
            org_code,
            org_name,
            create_time
        from tfs_enforcement_main_user
        where enforcement_main_id = #{enforcementMainId}
        order by user_identity,user_name asc
    </select>

    <!--查询单个,主键查询-->
    <select id="getByMainIds" resultMap="EnforcementMainUserMap">
        select
            uuid,
            enforcement_main_id,
            user_id,
            user_name,
            enforcement_type,
            enforcement_content,
            enforcement_requirements,
            enforcement_basis,
            status,
            user_identity,
            org_code,
            org_name,
            create_time
        from tfs_enforcement_main_user
        where enforcement_main_id in ( <foreach collection="enforcementMainIds" separator="," item="enforcementMainId"> #{enforcementMainId}</foreach> )
    </select>

    <!-- 根据主键id和用户id查询数据信息 -->
    <select id="getByMainIdAndUserId" resultMap="EnforcementMainUserMap">
        select
            uuid,
            enforcement_main_id,
            user_id,
            user_name,
            enforcement_type,
            enforcement_content,
            enforcement_requirements,
            enforcement_basis,
            status,
            user_identity,
            org_code,
            org_name,
            create_time
        from tfs_enforcement_main_user
        where enforcement_main_id = #{enforcementMainId}
        and user_id = #{userId}
        order by user_identity asc
        limit 1
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="EnforcementMainUserMap">
        select
            uuid,
            enforcement_main_id,
            user_id,
            user_name,
            enforcement_type,
            enforcement_content,
            enforcement_requirements,
            enforcement_basis,
            status,
            user_identity,
            org_code,
            org_name,
            create_time
        from tfs_enforcement_main_user
        <where>
            <if test="id != null and id != ''">
                and uuid = #{id}
            </if>
            <if test="enforcementMainId != null and enforcementMainId != ''">
                and enforcement_main_id = #{enforcementMainId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="enforcementType != null and enforcementType != ''">
                and enforcement_type = #{enforcementType}
            </if>
            <if test="enforcementContent != null and enforcementContent != ''">
                and enforcement_content = #{enforcementContent}
            </if>
            <if test="enforcementRequirements != null and enforcementRequirements != ''">
                and enforcement_requirements = #{enforcementRequirements}
            </if>
            <if test="enforcementBasis != null and enforcementBasis != ''">
                and enforcement_basis = #{enforcementBasis}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="userIdentity != null">
                and user_identity = #{userIdentity}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
    
    <!--通过实体作为筛选条件查询-->
    <select id="queryAllCount" resultType="long">
        select
         count(1)

        from tfs_enforcement_main_user
        <where>
            <if test="id != null and id != ''">
                and uuid = #{id}
            </if>
            <if test="enforcementMainId != null and enforcementMainId != ''">
                and enforcement_main_id = #{enforcementMainId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="enforcementType != null and enforcementType != ''">
                and enforcement_type = #{enforcementType}
            </if>
            <if test="enforcementContent != null and enforcementContent != ''">
                and enforcement_content = #{enforcementContent}
            </if>
            <if test="enforcementRequirements != null and enforcementRequirements != ''">
                and enforcement_requirements = #{enforcementRequirements}
            </if>
            <if test="enforcementBasis != null and enforcementBasis != ''">
                and enforcement_basis = #{enforcementBasis}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="userIdentity != null">
                and user_identity = #{userIdentity}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" >
        insert into tfs_enforcement_main_user(
        uuid,
        enforcement_main_id,
        user_id,
        user_name,
        enforcement_type,
        enforcement_content,
        enforcement_requirements,
        enforcement_basis,
        status,
        user_identity,
        org_code,
        org_name,
        create_time
        )
        values (
        #{id} ,
        #{enforcementMainId} ,
        #{userId} ,
        #{userName} ,
        #{enforcementType} ,
        #{enforcementContent} ,
        #{enforcementRequirements} ,
        #{enforcementBasis} ,
        #{status} ,
        #{userIdentity} ,
        #{orgCode} ,
        #{orgName} ,
        #{createTime} ,
        #{updateTime} )
    </insert>

    <!--批量添加-->
    <insert id="addAll" >
        insert into tfs_enforcement_main_user(
        uuid,
        enforcement_main_id,
        user_id,
        user_name,
        enforcement_type,
        enforcement_content,
        enforcement_requirements,
        enforcement_basis,
        status,
        user_identity,
        org_code,
        org_name,
        create_time
        )
        values <foreach collection="list" item="instance" separator=","> (
        #{instance.id} ,
        #{instance.enforcementMainId} ,
        #{instance.userId} ,
        #{instance.userName} ,
        #{instance.enforcementType} ,
        #{instance.enforcementContent} ,
        #{instance.enforcementRequirements} ,
        #{instance.enforcementBasis} ,
        #{instance.status} ,
        #{instance.userIdentity} ,
        #{instance.orgCode} ,
        #{instance.orgName} ,
        #{instance.createTime} )
    </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tfs_enforcement_main_user
        <set>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="enforcementContent != null and enforcementContent != ''">
                enforcement_content = #{enforcementContent},
            </if>
            <if test="enforcementRequirements != null and enforcementRequirements != ''">
                enforcement_requirements = #{enforcementRequirements},
            </if>
            <if test="enforcementBasis != null and enforcementBasis != ''">
                enforcement_basis = #{enforcementBasis},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="userIdentity != null">
                user_identity = #{userIdentity},
            </if>
            <if test="orgCode != null and orgCode != ''">
                org_code = #{orgCode},
            </if>
            <if test="orgName != null and orgName != ''">
                org_name = #{orgName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where uuid = #{id}
    </update>

    <!--通过主键逻辑删除-->
    <update id="deleteById">
        update tfs_enforcement_main_user set del_flag = 1 where uuid = #{id} and del_flag = 0
    </update>

    <!-- 根据执法主表id物理删除 -->
    <delete id="deleteByMainId">
        delete from tfs_enforcement_main_user where enforcement_main_id = #{enforcementMainId}
    </delete>

</mapper>