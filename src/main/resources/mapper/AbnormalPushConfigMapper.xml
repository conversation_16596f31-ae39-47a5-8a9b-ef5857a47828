<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IAbnormalPushConfigDao">
    <resultMap type="jylink.cpds.domain.AbnormalPushConfig" id="AbnormalPushConfigMap">
        <result property="id" column="uuid" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="extendedFields" column="extended_fields" jdbcType="VARCHAR"/>
        <result property="diyColumn" column="diy_column" jdbcType="VARCHAR"/>
        <result property="abnormalType" column="abnormal_type" jdbcType="INTEGER"/>
        <result property="abnormalTypeName" column="abnormal_type_name" jdbcType="VARCHAR"/>
        <result property="openFlag" column="open_flag" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个,主键查询-->
    <select id="queryById" resultMap="AbnormalPushConfigMap">
        select uuid,
               org_code,
               org_name,
               del_flag,
               create_time,
               update_time,
               extended_fields,
               diy_column,
               abnormal_type,
               abnormal_type_name,
               open_flag,
               user_id,
               user_name,
               sort_order
        from tfs_abnormal_push_config
        where uuid = #{id}
          and del_flag = 0
          and org_code = #{orgCode}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="AbnormalPushConfigMap">
        select
        uuid, org_code, org_name, del_flag, create_time, update_time, extended_fields, diy_column, abnormal_type,
        abnormal_type_name, open_flag, user_id, user_name, sort_order from tfs_abnormal_push_config
        <where>
            <if test="id != null and id != ''">
                and uuid = #{id}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="delFlag != null">
                and del_flag = #{delFlag}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="extendedFields != null and extendedFields != ''">
                and extended_fields = #{extendedFields}
            </if>
            <if test="diyColumn != null and diyColumn != ''">
                and diy_column = #{diyColumn}
            </if>
            <if test="abnormalType != null">
                and abnormal_type = #{abnormalType}
            </if>
            <if test="abnormalTypeName != null and abnormalTypeName != ''">
                and abnormal_type_name = #{abnormalTypeName}
            </if>
            <if test="openFlag != null">
                and open_flag = #{openFlag}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="sortOrder != null">
                and sort_order = #{sortOrder}
            </if>
        </where>
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAllCount" resultType="long">
        select
        count(1)
        from tfs_abnormal_push_config
        <where>
            <if test="id != null and id != ''">
                and uuid = #{id}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="delFlag != null">
                and del_flag = #{delFlag}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="extendedFields != null and extendedFields != ''">
                and extended_fields = #{extendedFields}
            </if>
            <if test="diyColumn != null and diyColumn != ''">
                and diy_column = #{diyColumn}
            </if>
            <if test="abnormalType != null">
                and abnormal_type = #{abnormalType}
            </if>
            <if test="abnormalTypeName != null and abnormalTypeName != ''">
                and abnormal_type_name = #{abnormalTypeName}
            </if>
            <if test="openFlag != null">
                and open_flag = #{openFlag}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="sortOrder != null">
                and sort_order = #{sortOrder}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into tfs_abnormal_push_config(uuid, org_code, org_name, del_flag, create_time, update_time,
                                             extended_fields,
                                             diy_column, abnormal_type, abnormal_type_name, open_flag, user_id,
                                             user_name,
                                             sort_order)
        values (#{id}, #{orgCode}, #{orgName}, #{delFlag}, #{createTime}, #{updateTime}, #{extendedFields},
                #{diyColumn},
                #{abnormalType}, #{abnormalTypeName}, #{openFlag}, #{userId}, #{userName}, #{sortOrder})
    </insert>

    <!--批量新增-->
    <insert id="addAll">
        insert into tfs_abnormal_push_config(uuid, org_code, org_name, del_flag, create_time, update_time,
                                             extended_fields,
                                             diy_column, abnormal_type, abnormal_type_name, open_flag, user_id,
                                             user_name,
                                             sort_order)
        values
               <foreach collection="list" separator="," item="instance">
               (#{instance.id},
                #{instance.orgCode},
                #{instance.orgName},
                0,
                #{instance.createTime},
                #{instance.updateTime},
                #{instance.extendedFields},
                #{instance.diyColumn},
                #{instance.abnormalType},
                #{instance.abnormalTypeName},
                #{instance.openFlag},
                #{instance.userId},
                #{instance.userName},
                #{instance.sortOrder})
               </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tfs_abnormal_push_config
        <set>
            <if test="orgCode != null and orgCode != ''">
                org_code = #{orgCode},
            </if>
            <if test="orgName != null and orgName != ''">
                org_name = #{orgName},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="extendedFields != null and extendedFields != ''">
                extended_fields = #{extendedFields},
            </if>
            <if test="diyColumn != null and diyColumn != ''">
                diy_column = #{diyColumn},
            </if>
            <if test="abnormalType != null">
                abnormal_type = #{abnormalType},
            </if>
            <if test="abnormalTypeName != null and abnormalTypeName != ''">
                abnormal_type_name = #{abnormalTypeName},
            </if>
            <if test="openFlag != null">
                open_flag = #{openFlag},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="sortOrder != null">
                sort_order = #{sortOrder},
            </if>
        </set>
        where uuid = #{id} and del_flag = 0
    </update>

    <!--通过主键逻辑删除-->
    <update id="deleteById">
        update tfs_abnormal_push_config
        set del_flag = 1
        where uuid = #{id}
          and del_flag = 0
    </update>

    <!--通过主键逻辑删除-->
    <delete id="deleteByUserId">
        delete from tfs_abnormal_push_config where user_id = #{userId}
                                               and del_flag = 0
                                               and org_code = #{orgCode}
    </delete>

    <!--查询单个,主键查询-->
    <select id="getByUserId" resultMap="AbnormalPushConfigMap">
        select uuid,
               org_code,
               org_name,
               del_flag,
               create_time,
               update_time,
               extended_fields,
               diy_column,
               abnormal_type,
               abnormal_type_name,
               open_flag,
               user_id,
               user_name,
               sort_order
        from tfs_abnormal_push_config
        where user_id = #{userId}
          and del_flag = 0
          and org_code = #{orgCode}
        <if test="openFlag != null">
            and open_flag = #{openFlag}
        </if>
        order by sort_order
    </select>

</mapper>
