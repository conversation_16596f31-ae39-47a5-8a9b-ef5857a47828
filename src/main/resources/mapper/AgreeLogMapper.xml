<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IAgreeLogDao">

    <resultMap type="jylink.cpds.domain.AgreeLog" id="AgreeLogMap">
        <result property="id" column="uuid" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="OTHER"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
        <result property="drainId" column="drain_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="aggreeTime" column="aggree_time" jdbcType="OTHER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="AgreeLogMap">
        select
          uuid, org_code, org_name, create_time, del_flag, drain_id, user_id, user_name, aggree_time
        from tfs_agree_log
        where uuid = #{id}
    </select>
    <select id="queryByDrainId" resultMap="AgreeLogMap">
        select
          uuid, org_code, org_name, create_time, del_flag, drain_id, user_id, user_name, aggree_time
        from tfs_agree_log
        where drain_id = #{drainId} and del_flag = '0'
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="AgreeLogMap">
        select
          uuid, org_code, org_name, create_time, del_flag, drain_id, user_id, user_name, aggree_time
        from tfs_agree_log
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="AgreeLogMap">
        select
          uuid, org_code, org_name, create_time, del_flag, drain_id, user_id, user_name, aggree_time
        from tfs_agree_log
        <where>
            <if test="id != null and id != ''">
                and uuid = #{id}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="delFlag != null and delFlag != ''">
                and del_flag = #{delFlag}
            </if>
            <if test="drainId != null and drainId != ''">
                and drain_id = #{drainId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                and user_name = #{userName}
            </if>
            <if test="aggreeTime != null">
                and aggree_time = #{aggreeTime}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="uuid" useGeneratedKeys="true">
        insert into tfs_agree_log(uuid,org_code, org_name, create_time, del_flag, drain_id, user_id, user_name, aggree_time)
        values (#{id},#{orgCode}, #{orgName}, #{createTime}, '0', #{drainId}, #{userId}, #{userName}, #{aggreeTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tfs_agree_log
        <set>
            <if test="orgCode != null and orgCode != ''">
                org_code = #{orgCode},
            </if>
            <if test="orgName != null and orgName != ''">
                org_name = #{orgName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
            <if test="drainId != null and drainId != ''">
                drain_id = #{drainId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="aggreeTime != null">
                aggree_time = #{aggreeTime},
            </if>
        </set>
        where uuid = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from tfs_agree_log where uuid = #{id}
    </delete>

</mapper>