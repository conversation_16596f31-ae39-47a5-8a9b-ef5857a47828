<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IFormTemplateDao">
    <!--结果映射 -->
    <resultMap type="jylink.cpds.domain.FormTemplate" id="formTemplate">
        <id property="id" column="uuid"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="formCode" column="form_code"/>
        <result property="formName" column="form_name"/>
        <result property="modelContent" column="model_content"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- 根据Id查询数据 -->
    <select id="getById" resultMap="formTemplate">
        select uuid,
               org_code,
               org_name,
               form_code,
               form_name,
               model_content,
               create_time,
               del_flag
        from tfs_form_template
        where uuid = #{id}
        and org_code = #{orgCode}
          and del_flag = 0
    </select>

    <!-- 根据Id查询数据 -->
    <select id="getByFormCode" resultMap="formTemplate">
        select uuid,
               org_code,
               org_name,
               form_code,
               form_name,
               model_content,
               create_time,
               del_flag
        from tfs_form_template
        where form_code = #{formCode}
        and org_code = #{orgCode}
        and del_flag = 0
    </select>

    <!-- 添加数据 -->
    <insert id="add">
        insert
        into tfs_form_template
        (uuid,
         org_code,
         org_name,
         form_code,
         form_name,
         model_content,
         create_time,
         del_flag)
        values (#{instance.id},
                #{instance.orgCode},
                #{instance.orgName},
                #{instance.formCode},
                #{instance.formName},
                #{instance.modelContent},
                #{instance.createTime},
                #{instance.delFlag})
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update tfs_form_template
        set form_code=#{instance.formCode},
        form_name=#{instance.formName},
        model_content=#{instance.modelContent}
        where form_code=#{instance.formCode}
        and del_flag = 0;
    </update>

    <!-- 查询数据是否存在 -->
    <select id="anyById" resultType="boolean">
        select (
                   select count(*) from tfs_form_template where org_code = #{orgCode} and del_flag = 0 and uuid = #{id}
               ) > 0;
    </select>

    <!-- 查询数据是否存在 -->
    <select id="anyByFormCode" resultType="boolean">
        select (
                   select count(*) from tfs_form_template where del_flag = 0 and form_code = #{formCode}
               ) > 0;
    </select>
</mapper>