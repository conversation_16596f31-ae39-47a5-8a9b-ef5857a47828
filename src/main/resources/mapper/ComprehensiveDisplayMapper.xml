<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IComprehensiveDisplayDao">
    <!--结果映射 -->
    <resultMap type="jylink.cpds.domain.ComprehensiveDisplayYear" id="YearInstance">
        <result property="dutyFootage" column="duty_footage"/>
        <result property="holeNo" column="hole_no"/>
        <result property="tunnelNum" column="tunnelNum" />
        <result property="holeDistance" column="hole_distance"/>
        <result property="workNo" column="work_no"/>
    </resultMap>

    <!--结果映射 -->
    <resultMap type="jylink.cpds.domain.ComprehensiveDisplayMonth" id="MonthInstance">
        <result property="month" column="MONTH"/>
        <result property="normals" column="normals"/>
        <result property="abnormals" column="abnormals"/>
        <result property="total" column="total"/>
        <result property="holeNo" column="holeno"/>
        <result property="holeDistance" column="holedistance"/>
        <result property="perNo" column="perno"/>
        <result property="dutyFootage" column="duty_footage"/>

    </resultMap>

    <!--结果映射 -->
    <resultMap type="jylink.cpds.serviceModel.ListItem" id="ListItemInstance">
        <result property="key" column="abnormal_type"/>
        <result property="value" column="abnormals"/>
    </resultMap>

    <!--结果映射 -->
    <resultMap type="jylink.cpds.serviceModel.dto.ComprehensiveDisplayTodayDto" id="TodayInstance">
        <result property="planHoleNum" column="jhHoleNo"/>
        <result property="planHoleDistance" column="jhHoleDistance"/>
        <result property="realHoleNum" column="sjHoleNo"/>
        <result property="realHoleDistance" column="sjHoleDistance"/>
    </resultMap>

    <!-- 机构二级界面主体 -->
    <resultMap type="jylink.cpds.serviceModel.dto.OrgMessageDto" id="OrgMessageInstance">
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="cityName" column="Mine_CityZone_NAME"/>
        <result property="countryName" column="ZONE_COUNTY_ID_NAME"/>
        <result property="personNumber" column="PerNo"/>
    </resultMap>

    <!--结果映射 -->
    <resultMap type="jylink.cpds.serviceModel.dto.SecondAbnormalDto" id="SecondAbnormalInstance">
        <result property="drainId" column="drain_id"/>
        <result property="abnormalType" column="abnormal_type"/>
        <result property="workName" column="work_name"/>
        <result property="surveyWaterMileage" column="survey_water_mileage"/>
        <result property="surveyWaterDate" column="survey_water_date"/>
        <result property="tunnelId" column="tunnel_id"/>
        <collection property="holes" column="{drainId=drain_id,abnormalType=abnormal_type}" ofType="jylink.cpds.serviceModel.ListItem" select="getHoleByAbnormalType">
            <result column="uuid" property="key"></result>
            <result column="hole_no" property="value"></result>
        </collection>
    </resultMap>

    <select id="getHoleByAbnormalType" resultType="map">
        select b.uuid ,b.hole_no from tfs_hole_details a INNER JOIN tfs_acceptance_checks b on a.uuid=b.hole_detail_id
          where a.drain_id=#{drainId} and abnormal_type=#{abnormalType}
    </select>
    <!-- 查询累计掘进进尺 -->
    <select id="getDutyFootage" resultType="double">
        select sum(duty_footage) dutyFootage
        from tfs_excavation_report
        where org_code=#{orgCode} and del_flag=0 and status = 2
        and date_format(work_date,'%Y')=date_format(NOW(),'%Y')
    </select>
    <select id="getDutyFootageByOrgCodes" resultType="double">
        select sum(duty_footage) dutyFootage
        from tfs_excavation_report
        where org_code in (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach>) and del_flag=0 and status = 2
        and date_format(work_date,'%Y')=date_format(NOW(),'%Y')
    </select>

    <!--年度探水次数、孔数、深度-->
    <select id="getNumber" resultMap="YearInstance">
        select sum(WorkNo) as work_no,sum(HoleNo) as hole_no,sum(HoleDistance) as hole_distance from (
        select count(DISTINCT b.drain_id) as WorkNo,count(c.hole_no) as HoleNo,sum(hole_distance) as HoleDistance
          from tfs_acceptance_approves b
          INNER JOIN tfs_acceptance_checks c on b.uuid=c.approve_id
          where b.org_code=#{orgCode} and b.del_flag=0 and c.del_flag=0 and b.status=2
          group by b.drain_id
        ) t
    </select>
    <select id="getNumberByOrgCodes" resultMap="YearInstance">
        select sum(WorkNo) as work_no,sum(HoleNo) as hole_no,round(sum(HoleDistance),1) as hole_distance ,count(DISTINCT tunnel_id) as tunnelNum from (
        select count(DISTINCT a.uuid) as WorkNo,count(c.hole_no) as HoleNo,sum(hole_distance) as HoleDistance,a.tunnel_id
          from tfs_drain_accounts a
            INNER JOIN tfs_acceptance_checks c on a.uuid=c.drain_id

            LEFT JOIN dcm_mine_info_mj dcm ON dcm.cmpi_dmpro = a.org_code
        where a.org_code in (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach>) and a.del_flag=0 and c.del_flag=0
            <if test="startDate != null and startDate != ''">
                AND date_format( a.survey_water_date, '%Y-%m-%d' ) >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND date_format( a.survey_water_date, '%Y-%m-%d' ) &lt;= #{endDate}
            </if><if test="mineCityZoneCode != null and mineCityZoneCode != ''">
                and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
            </if>
            <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
                and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
            </if>
            <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
                and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
            </if>
            <if test="orgName != null and !orgName.isEmpty()">
                and
                a.org_name like concat ('%',#{orgName},'%')
            </if>
        group by a.uuid
        ) t
    </select>

    <select id="getTunnelNum" resultType="integer">
        SELECT
            COUNT( DISTINCT de.uuid )
        FROM
            tfs_tunnel_designs de
                LEFT JOIN dcm_mine_info_mj dcm ON dcm.cmpi_dmpro = de.org_code
        WHERE
            de.del_flag = 0
          AND de.`status` = 2
          AND de.org_code IN ( <foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
          <if test="startDate != null and startDate != ''">
              AND DATE_FORMAT( de.create_time, '%Y-%m-%d' ) >= #{startDate}
          </if>
          <if test="endDate != null and endDate != ''">
              AND DATE_FORMAT( de.create_time, '%Y-%m-%d' ) &lt;= #{endDate}
          </if>
            <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
                and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
            </if>
            <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
                and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
            </if>
            <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
                and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
            </if>
            <if test="orgName != null and !orgName.isEmpty()">
                and
                de.org_name like concat ('%',#{orgName},'%')
            </if>
    </select>

    <!--年度累计人次-->
    <select id="getPerson" resultType="int">
        select count(1) as PerNo
        from tfs_drain_accounts b inner join
        (select drain_id,status from tfs_acceptance_approves where org_code=#{orgCode} and del_flag=0 and status=2 AND date_format( survey_water_date, '%Y-%m-%d' ) >= DATE_SUB( DATE_FORMAT(NOW(),'%Y-%m-%d'), INTERVAL 1 YEAR )
          INNER JOIN tfs_drain_accounts b on a.drain_id=b.uuid
          INNER JOIN tfs_drain_account_operator c on b.uuid=c.drain_id
          where b.del_flag=0 and c.del_flag=0
    </select>
    <select id="getPersonByOrgCodes" resultType="int">
        SELECT
            count( 1 ) AS PerNo
        FROM
            tfs_drain_accounts b
            LEFT JOIN dcm_mine_info_mj dcm ON dcm.cmpi_dmpro = b.org_code
            INNER JOIN ( SELECT drain_id, construction_leading_id FROM tfs_drain_account_operator WHERE del_flag = 0 GROUP BY drain_id, construction_leading_id ) c ON b.uuid = c.drain_id
        WHERE
            b.del_flag = 0
            <if test="startDate != null and startDate != ''">
                AND DATE_FORMAT( b.survey_water_date, '%Y-%m-%d' ) >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE_FORMAT( b.survey_water_date, '%Y-%m-%d' ) &lt; #{endDate}
            </if>
            <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
                and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
            </if>
            <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
                and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
            </if>
            <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
                and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
            </if>
            <if test="orgName != null and !orgName.isEmpty()">
                and
                b.org_name like concat ('%',#{orgName},'%')
            </if>
            AND b.org_code IN (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach>)
    </select>

    <select id="getPersonInfo" resultMap="OrgMessageInstance">
        SELECT
            d.cmpi_dmpro,
            d.CORP_NAME org_name,
            d.Mine_CityZone_NAME,
            d.ZONE_COUNTY_ID_NAME,
            count( 1 ) AS PerNo
        FROM
            dcm_mine_info_mj d
            LEFT JOIN tfs_drain_accounts b ON d.cmpi_dmpro = CONVERT ( b.org_code USING utf8 ) COLLATE utf8_general_ci
            AND b.del_flag = 0
--             AND date_format( survey_water_date, '%Y-%m-%d' ) >= DATE_SUB( DATE_FORMAT(NOW(),'%Y-%m-%d'), INTERVAL 1 YEAR )
            INNER JOIN ( SELECT drain_id, construction_leading_id FROM tfs_drain_account_operator WHERE del_flag = 0 GROUP BY drain_id, construction_leading_id ) c ON b.uuid = c.drain_id
        WHERE
            b.del_flag = 0
            AND d.cmpi_dmpro IN (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
        <if test="orgName != null and orgName != ''" >
            and d.CORP_NAME like concat ('%',#{orgName},'%')
        </if>
        GROUP BY
            d.cmpi_dmpro
    </select>

    <select id="getPersonCount" resultType="long">
        SELECT
            COUNT( DISTINCT d.cmpi_dmpro )
        FROM
            dcm_mine_info_mj d
            INNER JOIN tfs_drain_accounts b ON d.cmpi_dmpro = CONVERT ( b.org_code USING utf8 ) COLLATE utf8_general_ci
            INNER JOIN tfs_drain_account_operator c ON b.uuid = c.drain_id
        WHERE
            b.del_flag = 0
            and d.cmpi_dmpro in (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
            <if test="orgName != null and orgName != ''" >
                and d.CORP_NAME like concat ('%',#{orgName},'%')
            </if>
            AND c.del_flag = 0
    </select>

    <!--年度累计异常-->
    <select id="getAbnormal" resultMap="ListItemInstance">
        select abnormal_type,count(1) as abnormals from (
          select b.drain_id,d.abnormal_type
            from tfs_acceptance_approves b
            INNER JOIN tfs_acceptance_checks c on b.uuid=c.approve_id
            INNER JOIN tfs_hole_details d on c.hole_detail_id=d.uuid
            where b.org_code=#{orgCode} and b.del_flag=0 and c.del_flag=0 and d.del_flag=0 and b.`status`=2 and d.hole_status=1
            group by b.drain_id,d.abnormal_type
        ) t  group by abnormal_type
    </select>

    <select id="getAbnormalByOrgcodes" resultMap="ListItemInstance">
        SELECT
            tmp.abnormal_type,
            count( t.uuid ) AS abnormals
        FROM
            (
                SELECT
                    0 abnormal_type
                FROM
                    DUAL UNION ALL
                SELECT
                    1 abnormal_type
                FROM
                    DUAL UNION ALL
                SELECT
                    2 abnormal_type
                FROM
                    DUAL UNION ALL
                SELECT
                    4 abnormal_type
                FROM
                    DUAL UNION ALL
                SELECT
                    5 abnormal_type
                FROM
                    DUAL UNION ALL
                SELECT
                    6 abnormal_type
                FROM
                    DUAL UNION ALL
                SELECT
                    7 abnormal_type
                FROM
                    DUAL
            ) tmp
                LEFT JOIN (
                SELECT
                    b.uuid,
                    d.abnormal_type
                FROM
                    tfs_drain_accounts b
                        INNER JOIN tfs_hole_details c ON b.uuid = c.drain_id
                        INNER JOIN ( SELECT abnormal_type, abnormal_name, drain_id, hole_detail_id FROM tfs_hole_details_report WHERE del_flag = 0 AND abnormal_type >= 0 GROUP BY abnormal_type, hole_detail_id ) d ON c.uuid = d.hole_detail_id
                        LEFT JOIN dcm_mine_info_mj dcm ON dcm.cmpi_dmpro = b.org_code
                WHERE
                    b.del_flag = 0
                  AND c.del_flag = 0
                  AND b.org_code in (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
                    <if test="startDate != null and startDate != ''">
                        AND DATE_FORMAT( b.survey_water_date, '%Y-%m-%d' ) >= #{startDate}
                    </if>
                    <if test="endDate != null and endDate != ''">
                        AND DATE_FORMAT( b.survey_water_date, '%Y-%m-%d' ) &lt; #{endDate}
                    </if>
                    <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
                        and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
                    </if>
                    <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
                        and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
                    </if>
                    <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
                        and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
                    </if>
                    <if test="orgName != null and !orgName.isEmpty()">
                        and
                        b.org_name like concat ('%',#{orgName},'%')
                    </if>
            ) t ON tmp.abnormal_type = t.abnormal_type
        GROUP BY
            abnormal_type
    </select>
    <!--月度消息-->
    <select id="getMonth" resultMap="MonthInstance">
    select
        tt.MONTH,
        tt2.normals,
        tt2.abnormals,
        tt2.total,
        tt2.holeno,
        tt2.holedistance,
        tt2.perno,
        tt3.duty_footage
        FROM
        (
        SELECT
        '01' AS MONTH UNION
        SELECT
        '02' AS MONTH UNION
        SELECT
        '03' AS MONTH UNION
        SELECT
        '04' AS MONTH UNION
        SELECT
        '05' AS MONTH UNION
        SELECT
        '06' AS MONTH UNION
        SELECT
        '07' AS MONTH UNION
        SELECT
        '08' AS MONTH UNION
        SELECT
        '09' AS MONTH UNION
        SELECT
        '10' AS MONTH UNION
        SELECT
        '11' AS MONTH UNION
        SELECT
        '12' AS MONTH
        ) tt
        LEFT OUTER JOIN (
        SELECT
        months,
        sum( normals ) normals,
        sum( abnormals ) abnormals,
        sum( total ) total,
        sum( holeno ) holeno,
        sum( holedistance ) holedistance,
        sum( perno ) perno
        FROM
        (
        SELECT
        t2.drain_id,
        months,
        normals,
        abnormals,
        normals + abnormals AS total,
        holeno,
        holedistance,
        count( d.uuid ) perno
        FROM
        (
        SELECT
        drain_id,
        months,
        sum( CASE WHEN drilling_verdict = '正常' THEN 1 ELSE 0 END ) normals,
        sum( CASE WHEN drilling_verdict &lt;&gt; '正常' THEN 1 ELSE 0 END ) abnormals,
        sum( holeno ) holeno,
        sum( holedistance ) holedistance
        FROM
        (
        SELECT
        b.drain_id,
        b.drilling_verdict,
        count( 1 ) AS holeno,
        sum( c.hole_distance ) holedistance,
        date_format( b.survey_water_date, '%m' ) months
        FROM
        tfs_acceptance_approves b
        INNER JOIN tfs_acceptance_checks c ON b.uuid = c.approve_id
        WHERE
        b.org_code = #{orgCode}
        AND b.del_flag = 0
        AND c.del_flag = 0
        AND b.status = 2
        AND date_format( b.survey_water_date, '%Y' )= date_format( NOW(), '%Y' )
        GROUP BY
        b.drain_id,
        b.drilling_verdict,
        date_format( b.survey_water_date, '%m' )
        ) t
        GROUP BY
        drain_id,
        months
        ) t2
        LEFT OUTER JOIN tfs_drain_account_operator d ON t2.drain_id = d.drain_id
        GROUP BY
        t2.drain_id,
        months,
        normals,
        abnormals,
        holeno,
        holedistance
        ) t3
        GROUP BY
        months
        ) tt2 ON tt.MONTH = tt2.months
        LEFT OUTER JOIN (
        SELECT
        date_format( work_date, '%m' ) MONTH,
        sum( duty_footage ) duty_footage
        FROM
        tfs_excavation_report
        WHERE
        org_code = #{orgCode}
        AND del_flag = 0
        AND status = 2
        AND date_format( work_date, '%Y' )= date_format( NOW(), '%Y' )
        GROUP BY
        date_format( work_date, '%m' )
        ) tt3 ON tt.MONTH = tt3.MONTH
    </select>


    <!--获取当天计划，实际情况-->
    <select id="getToday" resultMap="TodayInstance">
        SELECT
            sum( jhHoleNo ) jhHoleNo,
            sum( jhHoleDistance ) jhHoleDistance,
            sum( sjHoleNo ) sjHoleNo,
            sum( sjHoleDistance ) sjHoleDistance
        FROM
            (
            SELECT
                b.uuid AS check_plan_id,
                count( hole_no ) jhHoleNo,
                sum( hole_distance ) jhHoleDistance
            FROM
                tfs_check_plans b
                INNER JOIN tfs_check_plan_details c ON b.uuid = c.check_plan_id
            WHERE
                b.org_code = #{orgCode}
                AND b.del_flag = 0
                AND b.`status` = 2
                AND DATE_FORMAT( b.prediction_date, '%Y-%m-%d' )= date_format( NOW(), '%Y-%m-%d' )
            GROUP BY
                b.uuid
            ) t
            INNER JOIN tfs_drain_accounts t2 ON t.check_plan_id = t2.check_plan_id
            LEFT OUTER JOIN (
            SELECT
                b.drain_id,
                count( c.hole_no ) AS sjHoleNo,
                sum( hole_distance ) AS sjHoleDistance
            FROM
                tfs_acceptance_approves b
                INNER JOIN tfs_acceptance_checks c ON b.uuid = c.approve_id
            WHERE
                b.org_code = #{orgCode}
                AND b.del_flag = 0
                AND c.del_flag = 0
                AND b.`status` = 2
            GROUP BY
            b.drain_id
            ) t3 ON t2.uuid = t3.drain_id
    </select>

    <!--二级界面异常-->
    <select id="getSecondAbnormal" resultMap="SecondAbnormalInstance">
        SELECT
        b.drain_id,
        d.abnormal_type,
        c.work_name,
        c.survey_water_mileage,
        b.survey_water_date,
        e.tunnel_id
        FROM
        tfs_acceptance_approves b
        INNER JOIN tfs_acceptance_checks c ON b.uuid = c.approve_id
        INNER JOIN tfs_hole_details d ON c.hole_detail_id = d.uuid
        INNER JOIN tfs_drain_accounts e ON b.drain_id = e.uuid
        WHERE
        b.org_code = #{orgCode}
        AND b.del_flag = 0
        AND c.del_flag = 0
        AND d.del_flag = 0
        AND b.`status` = 2
        AND d.hole_status = 1
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()" >
            and
            DATE_FORMAT (b.survey_water_date,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (b.survey_water_date,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (b.survey_water_date,'%Y-%m-%d') >= #{startDate}
        </if>
        <if test="abnormalType != null" >
            and
            d.abnormal_type=#{abnormalType}
        </if>
        <if test=" tunnelId !=null and !tunnelId.isEmpty()" >
            and
            e.tunnel_id= #{tunnelId}
        </if>
        GROUP BY
        b.drain_id,
        d.abnormal_type,
        c.work_name,
        c.survey_water_mileage,
        b.survey_water_date,
        e.tunnel_id
        order by b.survey_water_date desc
    </select>

    <!--二级界面异常个数-->
    <select id="getCount" resultType="long">
        SELECT
        count(*)
        FROM
        tfs_acceptance_approves b
        INNER JOIN tfs_acceptance_checks c ON b.uuid = c.approve_id
        INNER JOIN tfs_hole_details d ON c.hole_detail_id = d.uuid
        INNER JOIN tfs_drain_accounts e ON b.drain_id = e.uuid
        WHERE
        b.org_code = #{orgCode}
        AND b.del_flag = 0
        AND c.del_flag = 0
        AND d.del_flag = 0
        AND b.`status` = 2
        AND d.hole_status = 1
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()" >
            and
            DATE_FORMAT (b.survey_water_date,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (b.survey_water_date,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (b.survey_water_date,'%Y-%m-%d') >= #{startDate}
        </if>
        <if test="abnormalType != null" >
            and
            d.abnormal_type=#{abnormalType}
        </if>
        <if test=" tunnelId !=null and !tunnelId.isEmpty()" >
            and
            e.tunnel_id= #{tunnelId}
        </if>
    </select>
</mapper>
