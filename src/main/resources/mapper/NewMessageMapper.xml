<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.INewMessageDao">
    <resultMap type="jylink.cpds.domain.NewMessage" id="NewMessageMap">
        <result property="id" column="uuid" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="messageType" column="message_type" jdbcType="INTEGER"/>
        <result property="messageContent" column="message_content" jdbcType="VARCHAR"/>
        <result property="messageTitle" column="message_title" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap type="jylink.cpds.serviceModel.dto.NewMessageDto" id="NewMessageDtoMap">
        <result property="id" column="uuid" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="messageType" column="message_type" jdbcType="INTEGER"/>
        <result property="messageContent" column="message_content" jdbcType="VARCHAR"/>
        <result property="messageTitle" column="message_title" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个,主键查询-->
    <select id="queryById" resultMap="NewMessageMap">
        select
          uuid, org_code, org_name, message_type, message_content, message_title, url, create_time, update_time, del_flag
        from tfs_new_message
        where uuid = #{id} and del_flag = 0 and org_code = #{orgCode}
    </select> 

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="NewMessageMap">
        select
          uuid, org_code, org_name, message_type, message_content, message_title, url, create_time, update_time, del_flag
        from tfs_new_message
        <where>
            <if test="uuid != null and uuid != ''">
                and uuid = #{uuid}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="messageType != null">
                and message_type = #{messageType}
            </if>
            <if test="messageContent != null and messageContent != ''">
                and message_content = #{messageContent}
            </if>
            <if test="messageTitle != null and messageTitle != ''">
                and message_title = #{messageTitle}
            </if>
            <if test="url != null and url != ''">
                and url = #{url}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="delFlag != null">
                and del_flag = #{delFlag}
            </if>
        </where>
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="getLimit" resultMap="NewMessageDtoMap">
        select
        uuid,
        org_code,
        org_name,
        message_type,
        message_content,
        message_title,
        url,
        DATE_FORMAT(create_time,'%m-%d %H:%i') create_time,
        create_time as times
        from tfs_new_message
        where org_code in (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach>)
        order by times desc
        limit 20
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAllCount" resultType="long">
        select
         count(1)

        from tfs_new_message
        <where>
            <if test="uuid != null and uuid != ''">
                and uuid = #{uuid}
            </if>
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="messageType != null">
                and message_type = #{messageType}
            </if>
            <if test="messageContent != null and messageContent != ''">
                and message_content = #{messageContent}
            </if>
            <if test="messageTitle != null and messageTitle != ''">
                and message_title = #{messageTitle}
            </if>
            <if test="url != null and url != ''">
                and url = #{url}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="delFlag != null">
                and del_flag = #{delFlag}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" >
        insert into tfs_new_message(uuid, org_code, org_name, message_type, message_content, message_title, url, create_time, update_time, del_flag)
        values (        #{id} ,         #{orgCode} ,         #{orgName} ,         #{messageType} ,         #{messageContent} ,         #{messageTitle} ,         #{url} ,         #{createTime} ,         #{updateTime} ,         #{delFlag} )
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tfs_new_message
        <set>
            <if test="orgCode != null and orgCode != ''">
                org_code = #{orgCode},
            </if>
            <if test="orgName != null and orgName != ''">
                org_name = #{orgName},
            </if>
            <if test="messageType != null">
                message_type = #{messageType},
            </if>
            <if test="messageContent != null and messageContent != ''">
                message_content = #{messageContent},
            </if>
            <if test="messageTitle != null and messageTitle != ''">
                message_title = #{messageTitle},
            </if>
            <if test="url != null and url != ''">
                url = #{url},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
        </set>
        where uuid = #{id} and del_flag = 0
    </update>

    <!--通过主键逻辑删除-->
    <update id="deleteById">
        update tfs_new_message set del_flag = 1 where uuid = #{id} and del_flag = 0
    </update>

</mapper>