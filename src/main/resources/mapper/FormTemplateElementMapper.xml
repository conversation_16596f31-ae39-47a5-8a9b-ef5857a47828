<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IFormTemplateElementDao">
    <!--结果映射 -->
    <resultMap type="jylink.cpds.domain.FormTemplateElement" id="formTemplateElement">
        <id property="id" column="uuid"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="formKey" column="form_key"/>
        <result property="parameterName" column="parameter_name"/>
        <result property="parameterKey" column="parameter_key"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- 根据Id查询数据 -->
    <select id="getByFormKey" resultMap="formTemplateElement">
        select uuid,
               org_code,
               org_name,
               form_key,
               parameter_name,
               parameter_key,
               sort_order,
               create_time,
               del_flag
        from tfs_form_template_element
        where form_key = #{formKey}
          and del_flag = 0
          order by sort_order
    </select>

</mapper>