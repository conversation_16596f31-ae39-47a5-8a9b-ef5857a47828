<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IEnforcementClassifyOneDao">

    <resultMap type="jylink.cpds.domain.EnforcementClassifyOne" id="EnforcementClassifyOneMap">
        <result property="id" column="uuid" jdbcType="VARCHAR"/>
        <result property="oneCode" column="one_code" jdbcType="VARCHAR"/>
        <result property="oneDesc" column="one_desc" jdbcType="VARCHAR"/>
        <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个,主键查询-->
    <select id="queryById" resultMap="EnforcementClassifyOneMap">
        select
          uuid, one_code, one_desc, sort_order
        from tfs_enforcement_classify_one
        where uuid = #{id} and del_flag = 0 and org_code = #{orgCode}
    </select> 

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="EnforcementClassifyOneMap">
        select
          uuid, one_code, one_desc, sort_order
        from tfs_enforcement_classify_one
        <where>
            <if test="id != null and id != ''">
                and uuid = #{id}
            </if>
            <if test="oneCode != null and oneCode != ''">
                and one_code = #{oneCode}
            </if>
            <if test="oneDesc != null and oneDesc != ''">
                and one_desc = #{oneDesc}
            </if>
            <if test="sortOrder != null">
                and sort_order = #{sortOrder}
            </if>
        </where>
        order by sort_order
    </select>
    
    <!--通过实体作为筛选条件查询-->
    <select id="queryAllCount" resultType="long">
        select
         count(1)
        from tfs_enforcement_classify_one
        <where>
            <if test="id != null and id != ''">
                and uuid = #{id}
            </if>
            <if test="oneCode != null and oneCode != ''">
                and one_code = #{oneCode}
            </if>
            <if test="oneDesc != null and oneDesc != ''">
                and one_desc = #{oneDesc}
            </if>
            <if test="sortOrder != null">
                and sort_order = #{sortOrder}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" >
        insert into tfs_enforcement_classify_one(uuid, one_code, one_desc, sort_order)
        values (        #{id} ,         #{oneCode} ,         #{oneDesc} ,         #{sortOrder} )
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tfs_enforcement_classify_one
        <set>
            <if test="oneCode != null and oneCode != ''">
                one_code = #{oneCode},
            </if>
            <if test="oneDesc != null and oneDesc != ''">
                one_desc = #{oneDesc},
            </if>
            <if test="sortOrder != null">
                sort_order = #{sortOrder},
            </if>
        </set>
        where uuid = #{id} and del_flag = 0
    </update>

    <!--通过主键逻辑删除-->
    <update id="deleteById">
        update tfs_enforcement_classify_one set del_flag = 1 where uuid = #{id} and del_flag = 0
    </update>

</mapper>