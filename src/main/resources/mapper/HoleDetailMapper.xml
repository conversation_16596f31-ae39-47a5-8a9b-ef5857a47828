<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IHoleDetailDao">
    <!-- 结果映射 -->
    <resultMap type="jylink.cpds.domain.HoleDetail" id="HoleDetailInstance">
        <id property="id" column="uuid"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="drainId" column="drain_id"/>
        <result property="workName" column="work_name"/>
        <result property="workCode" column="work_code"/>
        <result property="surveyWaterMileage" column="survey_water_mileage"/>
        <result property="holeNo" column="hole_no"/>
        <result property="viceHoleNo" column="vice_hole_no"/>
        <result property="holeAzimuth" column="hole_azimuth"/>
        <result property="holeObliquity" column="hole_obliquity"/>
        <result property="holeDistance" column="hole_distance"/>
        <result property="analysisHoleDistance" column="analysis_hole_distance"/>
        <result property="status" column="status"/>
        <result property="reportHoleDistance" column="report_hole_distance"/>
        <result property="conclusionHoleDistance" column="conclusion_hole_distance"/>
        <result property="holeCondition" column="hole_condition"/>
        <result property="startDrillTime" column="start_drill_time"/>
        <result property="endDrillTime" column="end_drill_time"/>
        <result property="completionRate" column="completion_rate"/>
        <result property="recognitionRate" column="recognition_rate"/>
        <result property="holeAzimuthUrl" column="hole_azimuth_url"/>
        <result property="holeObliquityUrl" column="hole_obliquity_url"/>
        <result property="reportClassNumKey" column="report_class_num_key"/>
        <result property="reportClassNumValue" column="report_class_num_value"/>
        <result property="workOrgId" column="work_org_id"/>
        <result property="workOrgName" column="work_org_name"/>
        <result property="reportUserId" column="report_user_id"/>
        <result property="reportUserName" column="report_user_name"/>
        <result property="reportTime" column="report_time"/>
        <result property="height" column="height"/>
        <result property="reportHoleAzimuth" column="report_hole_azimuth"/>
        <result property="reportHoleObliquity" column="report_hole_obliquity"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="holeDiam" column="hole_diam"/>
        <result property="casingLength" column="casing_length"/>
        <result property="casingDiam" column="casing_diam"/>
        <result property="changeClassId" column="change_class_id"/>
        <result property="poleNum" column="pole_num" />
    </resultMap>
    <!-- 结果映射 -->
    <resultMap type="jylink.cpds.domain.HoleDetail" id="AnalysisResultList">
        <id property="id" column="uuid"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="drainId" column="drain_id"/>
        <result property="workName" column="work_name"/>
        <result property="workCode" column="work_code"/>
        <result property="surveyWaterMileage" column="survey_water_mileage"/>
        <result property="holeNo" column="hole_no"/>
        <result property="viceHoleNo" column="vice_hole_no"/>
        <result property="holeAzimuth" column="hole_azimuth"/>
        <result property="holeObliquity" column="hole_obliquity"/>
        <result property="holeDistance" column="hole_distance"/>
        <result property="analysisHoleDistance" column="analysis_hole_distance"/>
        <result property="status" column="status"/>
        <result property="reportHoleDistance" column="report_hole_distance"/>
        <result property="conclusionHoleDistance" column="conclusion_hole_distance"/>
        <result property="holeCondition" column="hole_condition"/>
        <result property="startDrillTime" column="start_drill_time"/>
        <result property="endDrillTime" column="end_drill_time"/>
        <result property="completionRate" column="completion_rate"/>
        <result property="recognitionRate" column="recognition_rate"/>
        <result property="holeAzimuthUrl" column="hole_azimuth_url"/>
        <result property="holeObliquityUrl" column="hole_obliquity_url"/>
        <result property="holeStatus" column="hole_status"/>
        <result property="workStatus" column="work_status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="reportClassNumKey" column="report_class_num_key"/>
        <result property="reportClassNumValue" column="report_class_num_value"/>
        <result property="height" column="height"/>
        <result property="reportHoleAzimuth" column="report_hole_azimuth"/>
        <result property="reportHoleObliquity" column="report_hole_obliquity"/>
        <result property="workOrgId" column="work_org_id"/>
        <result property="workOrgName" column="work_org_name"/>
        <result property="reportTime" column="report_time"/>
        <result property="reportUserId" column="report_user_id"/>
        <result property="reportUserName" column="report_user_name"/>
        <result property="holeDiam" column="hole_diam"/>
        <result property="casingLength" column="casing_length"/>
        <result property="casingDiam" column="casing_diam"/>
        <result property="changeClassId" column="change_class_id"/>
        <result property="defaultFlag" column="default_flag"/>
        <association property="acceptanceCheck" javaType="jylink.cpds.domain.AcceptanceCheck">
            <id column="ac_uuid" property="id"/>
            <result column="ac_hole_no" property="holeNo"/>
        </association>
    </resultMap>

    <!-- 岩性分段查询 -->
    <resultMap type="jylink.cpds.serviceModel.dto.CoalRockSegmentationDto" id="CoalRockSegmentationInstance">
        <id property="id" column="uuid"/>
        <result property="azimuth" column="hole_azimuth"/>
        <result property="holeDetailId" column="hole_detail_id"/>
        <result property="holeNo" column="hole_no"/>
        <result property="obliquity" column="hole_obliquity"/>
        <result property="holeDistance" column="hole_distance"/>
        <result property="poleLength" column="pole_length"/>
        <result property="poleNumber" column="pole_number"/>
        <result property="abnormalLocation" column="abnormal_location"/>
        <result property="abnormalType" column="abnormal_type"/>
        <result property="abnormalType" column="abnormal_type"/>
    </resultMap>

    <!-- 岩性分段查询 -->
    <resultMap type="jylink.cpds.serviceModel.dto.RecognitionRateDto" id="RecognitionRateMap">
        <result property="drainId" column="drain_id"/>
        <result property="reportHoleDistance" column="report_hole_distance"/>
        <result property="analysisHoleDistance" column="analysis_hole_distance"/>
        <result property="recognitionRate" column="recognitionRate"/>
    </resultMap>

    <!--添加应用实例-->
    <insert id="add">
        insert into tfs_hole_details (
        uuid,
        org_code,
        org_name,
        drain_id,
        work_name,
        work_code,
        survey_water_mileage,
        hole_no,
        vice_hole_no,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        status,
        create_time,
        abnormal_location,
        abnormal_type,
        hole_diam,
        casing_length,
        casing_diam,
        change_class_id,
        del_flag
        )
        values (
        #{instance.id},
        #{instance.orgCode},
        #{instance.orgName},
        #{instance.drainId},
        #{instance.workName},
        #{instance.workCode},
        #{instance.surveyWaterMileage},
        #{instance.holeNo},
        #{instance.viceHoleNo},
        #{instance.holeAzimuth},
        #{instance.holeObliquity},
        #{instance.holeDistance},
        #{instance.status},
        #{instance.createTime},
        #{instance.abnormalLocation},
        #{instance.abnormalType},
        #{instance.holeDiam},
        #{instance.casingLength},
        #{instance.casingDiam},
        #{instance.changeClassId},
        0
        )
    </insert>

    <!--添加应用实例-->
    <insert id="addAll">
        insert into tfs_hole_details (
        uuid,
        org_code,
        org_name,
        drain_id,
        work_name,
        work_code,
        survey_water_mileage,
        hole_no,
        vice_hole_no,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        status,
        report_hole_distance,
        conclusion_hole_distance,
        hole_condition,
        start_drill_time,
        end_drill_time,
        hole_azimuth_url,
        hole_obliquity_url,
        report_class_num_key,
        report_class_num_value,
        work_org_id,
        work_org_name,
        report_user_id,
        report_user_name,
        report_time,
        height,
        report_hole_obliquity,
        report_hole_azimuth,
        hole_status,
        work_status,
        create_time,
        abnormal_location,
        abnormal_type,
        hole_diam,
        casing_length,
        casing_diam,
        change_class_id,
        del_flag
        )
        values (
        #{instance.id},
        #{instance.orgCode},
        #{instance.orgName},
        #{instance.drainId},
        #{instance.workName},
        #{instance.workCode},
        #{instance.surveyWaterMileage},
        #{instance.holeNo},
        #{instance.viceHoleNo},
        #{instance.holeAzimuth},
        #{instance.holeObliquity},
        #{instance.holeDistance},
        #{instance.analysisHoleDistance},
        #{instance.status},
        #{instance.reportHoleDistance},
        #{instance.conclusionHoleDistance},
        #{instance.holeCondition},
        #{instance.startDrillTime},
        #{instance.endDrillTime},
        #{instance.holeAzimuthUrl},
        #{instance.holeObliquityUrl},
        #{instance.reportClassNumKey},
        #{instance.reportClassNumValue},
        #{instance.workOrgId},
        #{instance.workOrgName},
        #{instance.reportUserId},
        #{instance.reportUserName},
        #{instance.reportTime},
        #{instance.height},
        #{instance.reportHoleObliquity},
        #{instance.reportHoleAzimuth},
        #{instance.holeStatus},
        #{instance.workStatus},
        #{instance.createTime},
        #{instance.abnormalLocation},
        #{instance.abnormalType},
        #{instance.holeDiam},
        #{instance.casingLength},
        #{instance.casingDiam},
        #{instance.changeClassId},
        0
        )
    </insert>

    <!-- 批量添加 -->
    <insert id="batchAdd">
        insert into tfs_hole_details (
        uuid,
        org_code,
        org_name,
        drain_id,
        work_name,
        work_code,
        survey_water_mileage,
        hole_no,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        status,
        del_flag,
        create_time,
        abnormal_location,
        abnormal_type,
        hole_diam,
        casing_length,
        casing_diam,
        change_class_id,
        vice_hole_no
        )
        values
        <foreach collection="list" separator="," item="instance">
            (
            #{instance.id},
            #{instance.orgCode},
            #{instance.orgName},
            #{instance.drainId},
            #{instance.workName},
            #{instance.workCode},
            #{instance.surveyWaterMileage},
            #{instance.holeNo},
            #{instance.holeAzimuth},
            #{instance.holeObliquity},
            #{instance.holeDistance},
            #{instance.status},
            #{instance.delFlag},
            #{instance.createTime},
            #{instance.abnormalLocation},
            #{instance.abnormalType},
            #{instance.holeDiam},
            #{instance.casingLength},
            #{instance.casingDiam},
            #{instance.changeClassId},
            #{instance.viceHoleNo}
            )
        </foreach>
    </insert>

    <!-- 更新数据 -->
    <update id="updateHoleDetails">
        <foreach collection="holeDetails" item="item" index="index" open="" close="" separator=";">
            update tfs_hole_details
            set
            hole_azimuth=#{instance.holeAzimuth},
            hole_obliquity=#{instance.holeObliquity},
            hole_diam=#{instance.holeDiam},
            casing_length=#{instance.casingLength},
            casing_diam=#{instance.casingDiam},
            hole_distance=#{instance.holeDistance}
            where
            uuid = #{item.id}
            and
            org_code = #{instance.orgCode}
            and
            del_flag =0
        </foreach>
    </update>

    <!-- 根据Id查询数据是否存在 -->
    <select id="anyById" resultType="boolean">
        select (
        select count(*) from tfs_hole_details where uuid = #{id} and del_flag = 0
        ) > 0;
    </select>

    <!-- 根据工作面编码和探水里程查询数据 -->
    <select id="getByWorkCodeAndSurveyWaterMileage" resultMap="HoleDetailInstance">
        select uuid,
        org_code,
        org_name,
        drain_id,
        work_name,
        work_code,
        survey_water_mileage,
        hole_no,
        vice_hole_no,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        status,
        report_hole_distance,
        conclusion_hole_distance,
        hole_condition,
        start_drill_time,
        end_drill_time,
        completion_rate,
        recognition_rate,
        hole_azimuth_url,
        hole_obliquity_url,
        create_time,
        report_class_num_key,
        report_class_num_value,
        work_org_id,
        work_org_name,
        report_user_id,
        report_user_name,
        report_time,
        abnormal_location,
        abnormal_type,
        hole_diam,
        casing_length,
        casing_diam,
        change_class_id,
        del_flag,
               pole_num
        from tfs_hole_details
        where
        org_code = #{orgCode}
        and
        del_flag = 0
        and
        work_code = #{workCode}
        and
        survey_water_mileage = #{surveyWaterMileage}
        and hole_no = #{holeNo}
        and vice_hole_no=#{holeNoAttach}
    </select>

    <!-- 更改数据状态 -->
    <update id="updateStatus">
        update tfs_hole_details set status = #{status} where uuid = #{id} and del_flag = 0;
    </update>

    <!-- 验收 -->
    <update id="holeAcceptanceCheck">
        update tfs_acceptance_checks
        set
        hole_distance=#{instance.holeDistance},
        remark = #{instance.remark},
        analysis_hole_distance=#{instance.analysisHoleDistance},
        user_id=#{instance.userId},
        user_name=#{instance.userName},
        check_time=#{instance.checkTime},
        hole_detail_id=#{instance.holeDetailId},
        hole_azimuth=#{instance.holeAzimuth},
        hole_obliquity=#{instance.holeObliquity},
        hole_start_time=#{instance.holeStartTime},
        hole_class_num_key=#{instance.holeClassNumKey},
        hole_class_num_value=#{instance.holeClassNumValue},
        principal_name=#{instance.principalName},
        hole_org_name=#{instance.holeOrgName},
        height=#{instance.height},
        hole_condition=#{instance.holeCondition},
        drill_hole_place=#{instance.drillHolePlace},
        check_class_num_key=#{instance.checkClassNumKey},
        hole_diam=#{instance.holeDiam},
        casing_length=#{instance.casingLength},
        casing_diam=#{instance.casingDiam},
        check_class_num_value=#{instance.checkClassNumValue}
        where
        uuid = #{instance.id}
        and
        org_code = #{instance.orgCode}
        and
        del_flag = 0
        and
        status=8;
    </update>

    <!-- 根据Id获取数据 -->
    <select id="getById" resultMap="HoleDetailInstance">
        select
        uuid,
        org_code,
        org_name,
        drain_id,
        work_name,
        work_code,
        survey_water_mileage,
        hole_no,
        vice_hole_no,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        status,
        report_hole_distance,
        conclusion_hole_distance,
        hole_condition,
        start_drill_time,
        end_drill_time,
        completion_rate,
        recognition_rate,
        hole_azimuth_url,
        hole_obliquity_url,
        report_class_num_key,
        report_class_num_value,
        work_org_id,
        work_org_name,
        report_user_id,
        report_user_name,
        report_time,
        height,
        report_hole_obliquity,
        report_hole_azimuth,
        hole_status,
        work_status,
        create_time,
        abnormal_location,
        abnormal_type,
        hole_diam,
        casing_length,
        casing_diam,
        change_class_id,
        del_flag,
        pole_num
        from
        tfs_hole_details
        where
        uuid = #{id}
        and
        del_flag = 0
        LIMIT 1
    </select>

    <!-- 根据Ids获取数据 -->
    <select id="getByIds" resultMap="HoleDetailInstance">
        select
        uuid,
        org_code,
        org_name,
        drain_id,
        work_name,
        work_code,
        survey_water_mileage,
        hole_no,
        vice_hole_no,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        status,
        report_hole_distance,
        conclusion_hole_distance,
        hole_condition,
        start_drill_time,
        end_drill_time,
        completion_rate,
        recognition_rate,
        hole_azimuth_url,
        hole_obliquity_url,
        report_class_num_key,
        report_class_num_value,
        work_org_id,
        work_org_name,
        report_user_id,
        report_user_name,
        report_time,
        height,
        report_hole_obliquity,
        report_hole_azimuth,
        hole_status,
        create_time,
        abnormal_location,
        abnormal_type,
        hole_diam,
        casing_length,
        casing_diam,
        change_class_id,
        del_flag,
        pole_num
        from
        tfs_hole_details
        where
        uuid in ( <foreach collection="ids" item="id" separator=",">#{id}</foreach> )
        and
        del_flag = 0
    </select>


    <select id="getTotalTaskPushDistance" resultType="java.lang.Double">
        select sum(ifnull(report_hole_distance, 0.0)) from tfs_hole_details
        where uuid in (select ack.hole_detail_id from
        tfs_check_plans plans
        Left join tfs_drain_accounts account on plans.uuid = account.check_plan_id
        LEFT JOIN tfs_acceptance_checks ack on account.uuid = ack.drain_id
        where
        plans.uuid in (<foreach collection="ids" item="id" separator=",">#{id}</foreach>)
        )
    </select>


    <!-- 根据Id获取数据 -->
    <select id="getByDrainId" resultMap="HoleDetailInstance">
        select
        uuid,
        org_code,
        org_name,
        drain_id,
        work_name,
        work_code,
        survey_water_mileage,
        hole_no,
        vice_hole_no,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        status,
        report_hole_distance,
        conclusion_hole_distance,
        hole_condition,
        start_drill_time,
        end_drill_time,
        completion_rate,
        recognition_rate,
        hole_azimuth_url,
        hole_obliquity_url,
        report_class_num_key,
        report_class_num_value,
        work_org_id,
        work_org_name,
        report_user_id,
        report_user_name,
        report_time,
        height,
        report_hole_azimuth,
        report_hole_obliquity,
        create_time,
        hole_status,
        abnormal_location,
        abnormal_type,
        hole_diam,
        casing_length,
        casing_diam,
        change_class_id,
        del_flag,
        pole_num
        from
        tfs_hole_details
        where
        drain_id = #{drainId}
        and
        del_flag = 0
        and
        org_code = #{orgCode}
    </select>

    <!-- 获取当前实时任务孔数据信息 -->
    <select id="getByDrainIdNoHistory" resultMap="HoleDetailInstance">
        SELECT
            h.uuid,
            h.org_code,
            h.org_name,
            h.drain_id,
            h.work_name,
            h.survey_water_mileage,
            h.hole_no,
            h.vice_hole_no,
            h.hole_azimuth,
            h.hole_obliquity,
            h.hole_distance,
            h.analysis_hole_distance,
            h.STATUS,
            h.report_hole_distance,
            h.conclusion_hole_distance,
            h.hole_condition,
            h.start_drill_time,
            h.end_drill_time,
            h.completion_rate,
            h.recognition_rate,
            h.hole_azimuth_url,
            h.hole_obliquity_url,
            h.report_class_num_key,
            h.report_class_num_value,
            h.work_org_id,
            h.work_org_name,
            h.report_user_id,
            h.report_user_name,
            h.report_time,
            h.height,
            h.report_hole_azimuth,
            h.report_hole_obliquity,
            h.create_time,
            h.hole_status,
            h.abnormal_location,
            h.abnormal_type,
            h.hole_diam,
            h.casing_length,
            h.casing_diam,
            h.change_class_id,
            h.report_casing_length,
            h.del_flag,
            h.pole_num
        FROM
            tfs_hole_details h
        WHERE
            h.drain_id = #{drainId}
          AND h.del_flag = 0
          AND h.org_code = #{orgCode}
          AND NOT EXISTS (
                SELECT
                    1
                FROM
                    tfs_history_analysis_works d
                WHERE
                    d.hole_detail_id = h.uuid)
    </select>

    <update id="updateConclusionHoleDistanceById">
       update tfs_hole_details set conclusion_hole_distance=#{conclusionHoleDistance}
       WHERE uuid=#{id}
    </update>

    <!--根据钻孔编号查询孔列表-->
    <select id="getByHoleNo" resultMap="HoleDetailInstance">
        select
        uuid,
        org_code,
        org_name,
        drain_id,
        work_name,
        work_code,
        survey_water_mileage,
        hole_no,
        vice_hole_no,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        status,
        report_hole_distance,
        conclusion_hole_distance,
        hole_condition,
        start_drill_time,
        end_drill_time,
        completion_rate,
        recognition_rate,
        hole_azimuth_url,
        hole_obliquity_url,
        create_time,
        abnormal_location,
        abnormal_type,
        hole_diam,
        casing_length,
        casing_diam,
        change_class_id,
        del_flag,
        pole_num
        from
        tfs_hole_details
        where
        hole_no = #{holeNo}
        and
        drain_id=#{drainId}
        <if test="viceHoleNo==null || viceHoleNo.isEmpty()">
            and ifnull(vice_hole_no,'') =''
        </if>
        <if test="viceHoleNo!=null and !viceHoleNo.isEmpty()">
            and vice_hole_no=#{viceHoleNo}
        </if>
        and
        del_flag = 0
    </select>

    <!-- 获取自动识别列表 -->
    <select id="getAutoAnalysisList" resultMap="AnalysisResultList">
        select a.* ,(select ifnull((select 1 from tfs_acceptance_checks where tfs_acceptance_checks.hole_detail_id = a.uuid),0)) default_flag
        from tfs_hole_details a INNER JOIN tfs_drain_accounts c on a.drain_id=c.uuid and c.del_flag = 0
            inner JOIN tfs_acceptance_checks b on a.drain_id=b.drain_id and a.hole_no=b.hole_no and b.del_flag=0
        where
        not exists (select 1 from tfs_history_analysis_works d where d.hole_detail_id=a.uuid)
        and
        a.del_flag = 0
        and
        b.uuid= #{acceptanceCheckId}
        order by cast(vice_hole_no as SIGNED INTEGER)
    </select>
    <!-- 获取人工辅助识别列表 -->
    <select id="getArtificialAnalysisList" resultMap="AnalysisResultList">
        select a.uuid,
        a.org_code,
        a.org_name,
        a.drain_id,
        a.work_name,
        a.work_code,
        a.survey_water_mileage,
        a.hole_no,
        a.vice_hole_no,
        a.hole_azimuth,
        a.hole_obliquity,
        a.hole_distance,
        a.analysis_hole_distance,
        a.status,
        a.report_hole_distance,
        a.conclusion_hole_distance,
        a.hole_condition,
        a.completion_rate,
        a.recognition_rate,
        a.hole_azimuth_url,
        a.hole_obliquity_url,
        a.create_time,
        a.update_time,
        a.del_flag,
        a.report_class_num_key,
        a.report_class_num_value,
        a.work_org_id,
        a.work_org_name,
        a.report_user_id,
        a.report_user_name,
        a.report_time,
        a.height,
        a.report_hole_azimuth,
        a.report_hole_obliquity,
        a.hole_status,
        a.work_status,
        a.abnormal_location,
        a.abnormal_type,
        a.hole_diam,
        a.casing_length,
        a.casing_diam,
        a.change_class_id,
        a.report_casing_length,
        a.mac_code,
        a.pole_num,
        analy.start_time start_drill_time ,
        analy.end_time end_drill_time,
        (select ifnull((select 1 from tfs_acceptance_checks where tfs_acceptance_checks.hole_detail_id = a.uuid),0)) default_flag
        from tfs_hole_details a
        INNER JOIN tfs_drain_accounts c on a.drain_id = c.uuid and c.del_flag = 0
        LEFT JOIN tfs_acceptance_checks b on a.drain_id = b.drain_id and a.hole_no = b.hole_no and b.del_flag = 0
        left join tfs_history_analysis_works analy on a.uuid = analy.hole_detail_id
        where
        exists (select 1 from tfs_history_analysis_works d where d.hole_detail_id=a.uuid)
        and
        a.del_flag = 0
        and
        b.uuid= #{acceptanceCheckId}
        order by cast(vice_hole_no as SIGNED INTEGER)<!--将varch转换为int类型-->
    </select>

    <!-- 更新开始时间 -->
    <update id="updateStartTime">
        update tfs_hole_details
        set start_drill_time = #{date},status=1
        where org_code = #{orgCode}
        and work_code = #{workCode}
        and survey_water_mileage = #{surveyWaterMileage}
        and hole_no = #{holeNo}
        <if test="viceHoleNo==null || viceHoleNo.isEmpty()">
            and ifnull(vice_hole_no,'') =''
        </if>
        <if test="viceHoleNo!=null and !viceHoleNo.isEmpty()">
            and vice_hole_no=#{viceHoleNo}
        </if>
    </update>

    <!-- 更新结束时间 -->
    <update id="updateEndTime">
        update tfs_hole_details
        set end_drill_time = #{date},status=2
        where org_code = #{orgCode}
        and work_code = #{workCode}
        and survey_water_mileage = #{surveyWaterMileage}
        and hole_no = #{holeNo}
        <if test="viceHoleNo==null || viceHoleNo.isEmpty()">
            and ifnull(vice_hole_no,'') =''
        </if>
        <if test="viceHoleNo!=null and !viceHoleNo.isEmpty()">
            and vice_hole_no=#{viceHoleNo}
        </if>
    </update>
    <!-- 更新钻孔识别距离 -->
    <update id="updateAnalysisResult">
        update tfs_hole_details set analysis_hole_distance = #{analysisDistance} where drain_id=#{drainId} and hole_no = #{holeNo}
        <if test="viceHoleNo==null || viceHoleNo.isEmpty()">
            and ifnull(vice_hole_no,'') =''
        </if>
        <if test="viceHoleNo!=null and !viceHoleNo.isEmpty()">
            and vice_hole_no=#{viceHoleNo}
        </if>
        and del_flag = 0
    </update>

    <update id="updateHole">
            update tfs_hole_details
            set
            hole_azimuth=#{instance.holeAzimuth},
            hole_obliquity=#{instance.holeObliquity},
            hole_diam=#{instance.holeDiam},
            casing_length=#{instance.casingLength},
            casing_diam=#{instance.casingDiam},
            hole_distance=#{instance.holeDistance}
            where
            hole_no = #{instance.holeNo}
            and
            org_code = #{instance.orgCode}
            and
            del_flag =0
    </update>

    <!-- 删除数据 -->
    <delete id="deleteById">
        update tfs_hole_details set del_flag = 1 where uuid = #{id} and org_code=#{orgCode} and del_flag = 0;
    </delete>
    <!-- 根据台账Id和holeNo查询数据 -->
    <select id="getByDrainIdAndHoleNo" resultMap="HoleDetailInstance">
        select uuid,
        org_code,
        org_name,
        drain_id,
        work_name,
        work_code,
        survey_water_mileage,
        hole_no,
        vice_hole_no,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        status,
        report_hole_distance,
        conclusion_hole_distance,
        hole_condition,
        start_drill_time,
        end_drill_time,
        completion_rate,
        recognition_rate,
        hole_azimuth_url,
        hole_obliquity_url,
        create_time,
        height,
        report_class_num_key,
        report_class_num_value,
        work_org_id,
        work_org_name,
        report_user_id,
        report_user_name,
        report_time,
        report_hole_azimuth,
        report_hole_obliquity,
        hole_status,
        work_status,
        abnormal_location,
        abnormal_type,
        hole_diam,
        casing_length,
        casing_diam,
        change_class_id,
        del_flag,
               pole_num
        from tfs_hole_details
        where
        org_code = #{orgCode}
        and
        del_flag = 0
        and
        drain_id = #{drainId}
        and hole_no = #{holeNo}
        order by create_time desc
    </select>
    <!-- 根据id更新识别孔深 -->
    <update id="updateAnalysisHoleDistance">
        update tfs_hole_details set analysis_hole_distance = #{analysisHoleDistance} where uuid = #{id} and del_flag = 0;
    </update>
    <!--根据台账id和状态查询识别列表-->
    <select id="getByDrainIdAndStatus" resultMap="HoleDetailInstance">
        select * from tfs_hole_details
        where
        org_code = #{orgCode}
        and
        del_flag = 0
        and
        drain_id = #{drainId}
        and
        status = #{status}
        order by update_time desc
    </select>

    <select id="getByDrainIds" resultMap="HoleDetailInstance">
        select *
        from tfs_hole_details
        where del_flag=0
        and drain_id in (<foreach collection="drainIds" separator="," item="drainId">#{drainId}</foreach> )
    </select>

    <select id="getAbNormal" resultMap="CoalRockSegmentationInstance">
        SELECT
            a.uuid,
            a.hole_detail_id,
            a.hole_azimuth,
            a.hole_no,
            a.hole_obliquity,
            b.hole_distance,
            b.report_hole_distance,
            d.pole_length,
            CEIL( b.hole_distance / d.pole_length ) AS pole_number,
            b.abnormal_location,
            b.abnormal_type
        FROM
            tfs_acceptance_checks a
            LEFT JOIN tfs_hole_details b ON a.hole_detail_id = b.uuid
            AND b.del_flag = 0
            LEFT JOIN tfs_drain_accounts c ON a.drain_id = c.uuid
            AND c.del_flag = 0
            LEFT JOIN tfs_tunnel_designs d ON d.uuid = c.tunnel_id
            AND d.del_flag = 0
        WHERE
            a.uuid = #{acceptanceId}
        order by a.hole_no
    </select>

    <!-- 查询正在进行中的孔 -->
    <select id="getAnalysisHole" resultMap="HoleDetailInstance">
        select a.uuid,
        a.org_code,
        a.org_name,
        a.drain_id,
        a.work_name,
        a.survey_water_mileage,
        a.hole_no,
        a.vice_hole_no,
        a.hole_azimuth,
        a.hole_obliquity,
        a.hole_distance,
        a.analysis_hole_distance,
        a.status,
        a.report_hole_distance,
        a.conclusion_hole_distance,
        a.hole_condition,
        a.start_drill_time,
        a.end_drill_time,
        a.completion_rate,
        a.recognition_rate,
        a.hole_azimuth_url,
        a.hole_obliquity_url,
        a.create_time,
        a.report_class_num_key,
        a.report_class_num_value,
        a.work_org_id,
        a.work_org_name,
        a.report_user_id,
        a.report_user_name,
        a.report_time,
        a.abnormal_location,
        a.abnormal_type,
        a.hole_diam,
        a.casing_length,
        a.casing_diam,
        a.change_class_id,
        a.report_casing_length,
        a.del_flag,
               a.pole_num
        from tfs_hole_details a
        where
        a.org_code = #{orgCode}
        and
        a.del_flag = 0
        and
        a.drain_id = #{drainId}
        and
        status = 1
        and not exists (select 1 from tfs_history_analysis_works d where d.hole_detail_id=a.uuid) limit 1
    </select>

    <select id="getRecognitionRate" resultMap="RecognitionRateMap">
        SELECT
            detail.report_hole_distance,
            detail.analysis_hole_distance,
           (CASE WHEN detail.analysis_hole_distance IS NULL THEN 0 ELSE detail.analysis_hole_distance END ) / detail.report_hole_distance AS recognitionRate,
            checks.drain_id
        FROM
            tfs_acceptance_checks checks
                LEFT JOIN tfs_hole_details detail ON detail.uuid = checks.hole_detail_id
                AND detail.del_flag = 0
        WHERE
                checks.drain_id IN ( <foreach collection="drainIds" item="drainId" separator=",">#{drainId}</foreach> )
          AND detail.report_hole_distance IS NOT NULL
          AND detail.report_hole_distance  > 0
    </select>

    <select id="getAnalysisEndTime" resultType="date">
        SELECT
            IFNULL(
                    MAX( ana.end_time ),
                    MAX( hole.end_drill_time ))
        FROM
            tfs_drain_accounts dra
                LEFT JOIN tfs_analysis_works ana ON dra.uuid = ana.drain_id
                AND ana.del_flag = 0
                LEFT JOIN tfs_hole_details hole ON hole.del_flag = 0
                AND hole.STATUS = 2
                AND hole.drain_id = ana.drain_id
                AND NOT EXISTS ( SELECT 1 FROM tfs_history_analysis_works his WHERE his.del_flag = 0 AND his.hole_detail_id = hole.uuid )
        WHERE
            dra.uuid = #{drainId}
    </select>

    <select id="getFinishHoleInfo" resultType="jylink.cpds.serviceModel.dto.HoleDrillInfoDto">
        SELECT
            TIMESTAMPDIFF( SECOND, start_drill_time, end_drill_time ) AS timeDiff,
            uuid AS holeId,
            hole_no,
            vice_hole_no,
            start_drill_time,
            end_drill_time
        FROM
            tfs_hole_details
        WHERE
            del_flag = 0
          AND `status` = 2
          AND drain_id = #{drainId}
          AND NOT EXISTS ( SELECT 1 FROM tfs_history_analysis_works WHERE del_flag = 0 AND tfs_hole_details.uuid = hole_detail_id )
        ORDER BY
            start_drill_time
    </select>

</mapper>
