<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IDrawingExchangesDetailDao">

    <resultMap type="jylink.cpds.domain.DrawingExchangesDetail" id="DrawingExchangeDetailMap">
        <id property="id" column="uuid" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
        <result property="diyColumn" column="diy_column" jdbcType="VARCHAR"/>
        <result property="exchangeId" column="exchange_id" jdbcType="VARCHAR"/>
        <result property="receiveOrgCode" column="receive_org_code" jdbcType="VARCHAR"/>
        <result property="receiveOrgName" column="receive_org_name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="receiveTime" column="receive_time" jdbcType="TIMESTAMP"/>
        <result property="receiveUserId" column="receive_user_id" jdbcType="VARCHAR"/>
        <result property="receiveUserName" column="receive_user_name" jdbcType="VARCHAR"/>
        <association property="exchange" javaType="jylink.cpds.domain.DrawingExchanges">
            <id property="id" column="drawing_exchange_id" jdbcType="VARCHAR"/>
            <result property="orgCode" column="main_org_code" jdbcType="VARCHAR"/>
            <result property="orgName" column="main_org_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="main_create_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="main_del_flag" jdbcType="INTEGER"/>
            <result property="diyColumn" column="main_diy_column" jdbcType="VARCHAR"/>
            <result property="status" column="main_status" jdbcType="INTEGER"/>
            <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
            <result property="sendUserId" column="send_user_id" jdbcType="VARCHAR"/>
            <result property="sendUserName" column="send_user_name" jdbcType="VARCHAR"/>
        </association>
    </resultMap>

    <select id="selectDetail" resultMap="DrawingExchangeDetailMap">
        select * from tfs_drawing_exchanges_detail where exchange_id = #{uuid} and del_flag = 0 order by create_time desc
    </select>
</mapper>
