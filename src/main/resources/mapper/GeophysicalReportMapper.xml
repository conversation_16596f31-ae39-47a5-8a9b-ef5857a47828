<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IGeophysicalReportDao">
    <!--结果映射 -->
    <resultMap type="jylink.cpds.domain.GeophysicalReport" id="GeophysicalReport">
        <id property="id" column="uuid"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="tunnelId" column="tunnel_id"/>
        <result property="workName" column="work_name"/>
        <result property="person" column="person"/>
        <result property="position" column="position"/>
        <result property="company" column="company"/>
        <result property="equipment" column="equipment"/>
        <result property="workTime" column="work_time"/>
        <result property="detectionRange" column="detection_range"/>
        <result property="method" column="method"/>
        <result property="result" column="result"/>
        <result property="represent" column="represent"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="geophysicalPurpose" column="geophysical_purpose"/>
        <result property="number" column="number"/>
        <result property="mineCityzoneName" column="Mine_CityZone_NAME" />
        <result property="zoneCountyIdName" column="ZONE_COUNTY_ID_NAME" />
    </resultMap>

    <!--结果映射 -->
    <resultMap type="jylink.cpds.serviceModel.dto.OrgGeophysicalDto" id="OrgGeophysical">
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="total" column="total"/>
        <result property="normalNum" column="normalNum"/>
        <result property="abNormalNum" column="abNormalNum"/>
    </resultMap>


    <!-- 根据Id查询数据 -->
    <select id="getById" resultMap="GeophysicalReport">
        select uuid,
               org_code,
               org_name,
               tunnel_id,
               work_name,
               person,
               company,
               position,
               equipment,
               work_time,
               detection_range,
               method,
               result,
               represent,
               user_id,
               user_name,
               create_time,
                ifnull(status,2) status,
               geophysical_purpose,
               number,
               del_flag
        from tfs_geophysical_report
        where uuid = #{id}
          and del_flag = 0
    </select>

    <!-- 根据设计Id查询数据 -->
    <select id="getByTunnelId" resultMap="GeophysicalReport">
        select uuid,
               org_code,
               org_name,
               tunnel_id,
               work_name,
               person,
               company,
               position,
               equipment,
               work_time,
               detection_range,
               method,
               result,
               represent,
               user_id,
               user_name,
               create_time,
                ifnull(status,2) status,
               geophysical_purpose,
               number,
               del_flag
        from tfs_geophysical_report
        where tunnel_id = #{tunnelId}
        and org_code = #{orgCode}
          and del_flag = 0
    </select>


    <!-- 查询个数数据 -->
    <select id="getCount" resultType="long">
        SELECT
            count(*)
        FROM
            (
                SELECT
                    ifnull( report.STATUS, 2 ) STATUS
                FROM
                    tfs_geophysical_report report
                        LEFT JOIN dcm_mine_info_mj dcm ON report.org_code = dcm.cmpi_dmpro
                        LEFT JOIN tfs_work_face w ON w.uuid = report.tunnel_id
                WHERE
                    report.del_flag = 0
                  AND w.del_flag = 0
                  AND report.org_code in (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
        <if test="result !=null and !result.isEmpty() and result == '未知'">
            and report.result is null
        </if>
        <if test="result !=null and !result.isEmpty() and result != '未知'">
            and report.result=#{result}
        </if>
        <if test="method !=null ">
            and report.method=#{method}
        </if>
        <if test="type != null and type == 0">
            AND YEARWEEK( date_format( report.work_time, '%Y-%m-%d' ), 1 ) = YEARWEEK( now(), 1 )
        </if>
        <if test="type != null and type == 1">
            AND YEAR ( report.work_time ) = YEAR (NOW())
            AND MONTH ( report.work_time ) = MONTH (NOW())
        </if>
        <if test="type != null and type == 2">
            AND YEAR ( report.work_time ) = YEAR (NOW())
        </if>
        <if test="orgName !=null and !orgName.isEmpty()">
            and report.org_name like concat ('%',#{orgName},'%')
        </if>
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()" >
            and
            DATE_FORMAT (report.work_time,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (report.work_time,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (report.work_time,'%Y-%m-%d') >= #{startDate}
        </if>
        <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
            and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
        </if>
        <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
            and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
        </if>
        <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
            and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
        </if>
        <if test="orgCode != null and orgCode != '' ">
            and report.org_code = #{orgCode}
        </if>
        <if test="workFaceId != null and workFaceId != '' ">
            and report.tunnel_id = #{workFaceId}
        </if>
            ) t
        WHERE
            `status` = 2
    </select>

    <!-- 分页查询数据 -->
    <select id="getByOrgCode" resultMap="GeophysicalReport">
        SELECT
            uuid,
            org_code,
            org_name,
            tunnel_id,
            work_name,
            person,
            company,
            position,
            equipment,
            work_time,
            detection_range,
            method,
            result,
            represent,
            user_id,
            user_name,
            create_time,
            del_flag,
            STATUS,
            geophysical_purpose,
            number,
            Mine_CityZone_NAME,
            ZONE_COUNTY_ID_NAME,
            Mine_ProvZone_NAME
        FROM
            (
                SELECT
                    report.uuid,
                    report.org_code,
                    report.org_name,
                    report.tunnel_id,
                    report.work_name,
                    report.person,
                    report.company,
                    report.position,
                    report.equipment,
                    report.work_time,
                    report.detection_range,
                    report.method,
                    report.result,
                    report.represent,
                    report.user_id,
                    report.user_name,
                    report.create_time,
                    report.del_flag,
                    ifnull( report.STATUS, 2 ) STATUS,
                    report.geophysical_purpose,
                    report.number,
                    dcm.Mine_CityZone_NAME,
                    dcm.ZONE_COUNTY_ID_NAME,
                    dcm.Mine_ProvZone_NAME
                FROM
                    tfs_geophysical_report report
                        LEFT JOIN dcm_mine_info_mj dcm ON report.org_code = dcm.cmpi_dmpro
                        LEFT JOIN tfs_work_face w ON w.uuid = report.tunnel_id
                WHERE
                    report.del_flag = 0
                    AND w.del_flag = 0
                    AND report.org_code in (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
        <if test="result !=null and !result.isEmpty() and result == '未知'">
            and report.result is null
        </if>
        <if test="result !=null and !result.isEmpty() and result != '未知'">
            and report.result=#{result}
        </if>
        <if test="method !=null ">
            and report.method=#{method}
        </if>
        <if test="type != null and type == 0">
            AND YEARWEEK( date_format( report.work_time, '%Y-%m-%d' ), 1 ) = YEARWEEK( now(), 1 )
        </if>
        <if test="type != null and type == 1">
            AND YEAR ( report.work_time ) = YEAR (NOW())
            AND MONTH ( report.work_time ) = MONTH (NOW())
        </if>
        <if test="type != null and type == 2">
            AND YEAR ( report.work_time ) = YEAR (NOW())
        </if>
        <if test="orgName !=null and !orgName.isEmpty()">
            and report.org_name like concat ('%',#{orgName},'%')
        </if>
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()" >
            and
            DATE_FORMAT (report.work_time,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (report.work_time,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (report.work_time,'%Y-%m-%d') >= #{startDate}
        </if>
        <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
            and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
        </if>
        <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
            and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
        </if>
        <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
            and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
        </if>
        <if test="orgCode != null and orgCode != '' ">
            and report.org_code = #{orgCode}
        </if>
        <if test="workFaceId != null and workFaceId != '' ">
            and report.tunnel_id = #{workFaceId}
        </if>
            ) t
        WHERE
            `status` = 2
        ORDER BY
            work_time DESC
    </select>

    <!-- 添加数据 -->
    <insert id="add">
        insert
        into tfs_geophysical_report
        (
        uuid,
        org_code,
        org_name,
        tunnel_id,
        work_name,
        person,
        company,
        position,
        equipment,
        work_time,
        detection_range,
        method,
        result,
        represent,
        user_id,
        user_name,
        create_time,
        del_flag)
        values (#{instance.id},
                #{instance.orgCode},
                #{instance.orgName},
                #{instance.tunnelId},
                #{instance.workName},
                #{instance.person},
                #{instance.company},
                #{instance.position},
                #{instance.equipment},
                #{instance.workTime},
                #{instance.detectionRange},
                #{instance.method},
                #{instance.result},
                #{instance.represent},
                #{instance.userId},
                #{instance.userName},
                #{instance.createTime},
                0)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update tfs_geophysical_report
        set
        tunnel_id=#{instance.tunnelId},
        work_name=#{instance.workName},
        person=#{instance.person},
        company=#{instance.company},
        position=#{instance.position},
        equipment=#{instance.equipment},
        work_time=#{instance.workTime},
        detection_range=#{instance.detectionRange},
        method=#{instance.method},
        result=#{instance.result},
        represent=#{instance.represent},
        user_id=#{instance.userId},
        user_name=#{instance.userName}
        where uuid=#{instance.id}
        and del_flag = 0;
    </update>

    <!-- 查询数据是否存在 -->
    <select id="anyById" resultType="boolean">
        select (
                   select count(*) from tfs_geophysical_report where org_code = #{orgCode} and del_flag = 0 and uuid = #{id}
               ) > 0;
    </select>

    <!--删除-->
    <update id="delete">
        update tfs_geophysical_report set del_flag = 1 where org_code = #{orgCode} and del_flag = 0 and uuid = #{id}
    </update>

    <select id="getGeophysical" resultMap="OrgGeophysical">
        SELECT
            sum( CASE WHEN g.result = '正常' THEN 1 ELSE 0 END ) normalNum,
            sum( CASE WHEN g.result = '异常' THEN 1 ELSE 0 END ) abNormalNum,
            g.org_code,
            g.org_name
        FROM
            tfs_geophysical_report g
                LEFT JOIN tfs_work_face w ON w.uuid = g.tunnel_id
                LEFT JOIN tfs_tunnel_designs t ON t.del_flag = 0
                AND t.work_face_id = w.uuid
        WHERE
            g.del_flag = 0
          AND g.org_code IN (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
          AND w.del_flag = 0
          AND ( g.`status` IS NULL OR g.`status` = 2 )
        <if test="month != null">
            AND DATE_FORMAT( g.create_time, '%Y-%m' ) > DATE_FORMAT( DATE_ADD( now(), INTERVAL - #{month} MONTH ), '%Y-%m' )
        </if>
        <if test="orgName != null and orgName != '' ">
            and INSTR(g.org_name,#{orgName})
        </if>
        GROUP BY
            g.org_code
    </select>

    <select id="getAllCount" resultMap="OrgGeophysical">
        SELECT
            sum( CASE WHEN g.result = '正常' THEN 1 ELSE 0 END ) normalNum,
            sum( CASE WHEN g.result = '异常' THEN 1 ELSE 0 END ) abNormalNum
        FROM
            tfs_geophysical_report g
            LEFT JOIN tfs_work_face w ON w.uuid = g.tunnel_id
            LEFT JOIN tfs_tunnel_designs t ON t.del_flag = 0
            AND t.work_face_id = w.uuid
        WHERE
            g.org_code IN (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
            AND g.del_flag = 0
            AND w.del_flag = 0
            AND ( g.`status` IS NULL OR g.`status` = 2 )
            <if test="month != null">
                AND DATE_FORMAT( g.create_time, '%Y-%m' ) > DATE_FORMAT( DATE_ADD( now(), INTERVAL - #{month} MONTH ), '%Y-%m' )
            </if>
            <if test="orgName != null and orgName != '' ">
                and INSTR(g.org_name,#{orgName})
            </if>
    </select>

    <select id="getAllGeophysicalCount" resultMap="OrgGeophysical">
        SELECT
        sum( CASE WHEN g.result = '正常' THEN 1 ELSE 0 END ) normalNum,
        sum( CASE WHEN g.result = '异常' THEN 1 ELSE 0 END ) abNormalNum,
        COUNT( DISTINCT g.uuid ) total
        FROM
        tfs_geophysical_report g
        LEFT JOIN tfs_work_face w ON w.uuid = g.tunnel_id
        LEFT JOIN tfs_tunnel_designs t ON t.del_flag = 0
        AND t.work_face_id = w.uuid
        WHERE
        g.org_code IN (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
        AND g.del_flag = 0
        AND w.del_flag = 0
        AND ( g.`status` IS NULL OR g.`status` = 2 )
        <if test="result !=null and !result.isEmpty() and result == '未知'">
            and g.result is null
        </if>
        <if test="result !=null and !result.isEmpty() and result != '未知'">
            and g.result=#{result}
        </if>
        <if test="method !=null ">
            and g.method=#{method}
        </if>
        <if test="type != null and type == 0">
            AND YEARWEEK( date_format( g.work_time, '%Y-%m-%d' ), 1 ) = YEARWEEK( now(), 1 )
        </if>
        <if test="type != null and type == 1">
            AND YEAR ( g.work_time ) = YEAR (NOW())
            AND MONTH ( g.work_time ) = MONTH (NOW())
        </if>
        <if test="type != null and type == 2">
            AND YEAR ( g.work_time ) = YEAR (NOW())
        </if>
        <if test="orgName !=null and !orgName.isEmpty()">
            and g.org_name like concat ('%',#{orgName},'%')
        </if>
        <if test="startDate != null and !startDate.isEmpty() and endDate != null and !endDate.isEmpty()" >
            and
            DATE_FORMAT (g.work_time,'%Y-%m-%d') between #{startDate} and #{endDate}
        </if>
        <if test=" endDate != null and !endDate.isEmpty() and (startDate==null or startDate.isEmpty())" >
            and
            DATE_FORMAT (g.work_time,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and !startDate.isEmpty() and (endDate==null or endDate.isEmpty())" >
            and
            DATE_FORMAT (g.work_time,'%Y-%m-%d') >= #{startDate}
        </if>
        <if test="orgName != null and orgName != '' ">
            and INSTR(g.org_name,#{orgName})
        </if>
        <if test="orgCode != null and orgCode != ''">
            and g.org_code = #{orgCode}
        </if>
        <if test="workFaceId != null and workFaceId != ''">
            and g.tunnel_id = #{workFaceId}
        </if>
    </select>

    <select id="getGeophysicalCount" resultType="long">

        SELECT
            count(DISTINCT g.org_code)
        FROM
            tfs_geophysical_report g
            LEFT JOIN tfs_work_face w ON w.uuid = g.tunnel_id
            LEFT JOIN tfs_tunnel_designs t ON t.del_flag = 0
            AND t.work_face_id = w.uuid
        WHERE
        g.org_code IN (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
        AND g.del_flag = 0
        AND w.del_flag = 0
        AND ( g.`status` IS NULL OR g.`status` = 2 )
        <if test="month != null">
            AND DATE_FORMAT( g.create_time, '%Y-%m' ) > DATE_FORMAT( DATE_ADD( now(), INTERVAL - #{month} MONTH ), '%Y-%m' )
        </if>
        <if test="orgName != null and orgName != '' ">
            and INSTR(g.org_name,#{orgName})
        </if>
    </select>

    <select id="getGeophysicalNumByYear" resultType="long">

        SELECT
        count(DISTINCT g.uuid)
        FROM
        dcm_mine_info_mj dcm
        LEFT JOIN (SELECT uuid,tunnel_id,org_code,work_time from tfs_geophysical_report WHERE del_flag = 0  AND ( `status` IS NULL OR `status` = 2 )) g  ON dcm.cmpi_dmpro = g.org_code

        LEFT JOIN tfs_work_face w ON w.uuid = g.tunnel_id
        LEFT JOIN tfs_tunnel_designs t ON t.del_flag = 0

        AND t.work_face_id = w.uuid
        WHERE
        g.org_code IN (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )

        AND w.del_flag = 0

        <if test=" endDate != null and endDate != ''" >
            and
            DATE_FORMAT (g.work_time,'%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test=" startDate!=null and startDate != ''" >
            and
            DATE_FORMAT (g.work_time,'%Y-%m-%d') >= #{startDate}
        </if>
        <if test="mineCityZoneCode != null and mineCityZoneCode != ''">
            and dcm.Mine_CityZone_CODE = #{mineCityZoneCode}
        </if>
        <if test="mineCountyIdCode != null and mineCountyIdCode != '' ">
            and dcm.ZONE_COUNTY_ID_CODE = #{mineCountyIdCode}
        </if>
        <if test="mineProvzoneCode != null and mineProvzoneCode != '' ">
            and dcm.Mine_ProvZone_CODE = #{mineProvzoneCode}
        </if>
        <if test="orgName != null and !orgName.isEmpty()">
            and
            g.org_name like concat ('%',#{orgName},'%')
        </if>
    </select>

    <select id="getOrgGeophysicalNumByYear" resultType="jylink.cpds.serviceModel.dto.OrgMessageDto">
        SELECT
        g.org_code as orgCode,
        count(DISTINCT g.uuid) as geophysicalNumber
        FROM
        tfs_geophysical_report g
        LEFT JOIN tfs_work_face w ON w.uuid = g.tunnel_id
        LEFT JOIN tfs_tunnel_designs t ON t.del_flag = 0
        AND t.work_face_id = w.uuid
        WHERE
        g.org_code IN (<foreach collection="orgCodes" separator="," item="orgCode">#{orgCode}</foreach> )
        AND g.del_flag = 0
        AND w.del_flag = 0
        AND ( g.`status` IS NULL OR g.`status` = 2 )
        <if test="orgName != null and orgName != '' ">
            and INSTR(g.org_name,#{orgName})
        </if>
        group by g.org_code
    </select>

    <!-- 分页查询数据 -->
    <select id="getOrgCode" resultMap="GeophysicalReport">
        select
        g.uuid,
        g.org_code,
        g.org_name,
        g.tunnel_id,
        g.work_name,
        g.person,
        g.company,
        g.position,
        g.equipment,
        g.work_time,
        g.detection_range,
        g.method,
        g.result,
        g.represent,
        g.user_id,
        g.user_name,
        g.create_time,
        ifnull( g.STATUS, 2 ) STATUS,
        g.geophysical_purpose,
        g.number,
        g.del_flag
        from
        tfs_geophysical_report g
        LEFT JOIN tfs_work_face w ON w.uuid = g.tunnel_id
        AND w.del_flag = 0
        LEFT JOIN tfs_tunnel_designs t ON t.del_flag = 0
        AND t.work_face_id = w.uuid
        where g.org_code = #{orgCode}
        <if test="result !=null and !result.isEmpty()">
            and g.result=#{result}
        </if>
        <if test="workName !=null and !workName.isEmpty()">
            and g.work_name like concat('%',#{workName},'%')
        </if>
        AND g.del_flag = 0
        AND ( g.`status` IS NULL OR g.`status` = 2 )

        order by g.work_time desc
    </select>

    <!-- 查询个数数据 -->
    <select id="getNum" resultType="long">
        select count(*)
        from
        tfs_geophysical_report g
        LEFT JOIN tfs_work_face w ON w.uuid = g.tunnel_id
        AND w.del_flag = 0
        LEFT JOIN tfs_tunnel_designs t ON t.del_flag = 0
        AND t.work_face_id = w.uuid
        where g.org_code = #{orgCode}
        <if test="result !=null and !result.isEmpty()">
            and g.result=#{result}
        </if>
        <if test="workName !=null and !workName.isEmpty()">
            and g.work_name like concat('%',#{workName},'%')
        </if>
        AND g.del_flag = 0
        AND ( g.`status` IS NULL OR g.`status` = 2 )

    </select>

    <select id="getGeophysicalNum" resultType="long">
        SELECT
            count(*)
        FROM
            dcm_mine_info_mj dcm
            LEFT JOIN tfs_geophysical_report c ON dcm.cmpi_dmpro = CONVERT ( c.org_code USING utf8 ) COLLATE utf8_general_ci
            LEFT JOIN tfs_work_face w ON c.tunnel_id = w.uuid and w.del_flag = 0
            LEFT JOIN tfs_tunnel_designs t on t.work_face_id = w.uuid and t.del_flag = 0
        WHERE
            dcm.cmpi_dmpro IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
            AND ( c.`status` IS NULL OR c.`status` = 2 )
            AND c.del_flag = 0
            AND ( t.detection_status IS NULL OR t.detection_status = 0 )
    </select>

    <select id="getGeophysicalResultByPlanId" resultType="jylink.cpds.serviceModel.ListItem">
        SELECT
        `uuid` as `key`,
        `result` as `value`
        FROM `tfs_geophysical_report` WHERE
        `uuid` =   (SELECT `geophysical_id` FROM `tfs_check_plans` WHERE  `uuid` = #{planId} and org_code=#{orgCode})
    </select>

    <select id="getNumByType" resultType="jylink.cpds.serviceModel.dto.BaseAbnormalItem">
        SELECT
            sum( CASE WHEN geo.result = '正常' THEN 1 ELSE 0 END ) normalNum,
            sum( CASE WHEN geo.result = '异常' THEN 1 ELSE 0 END ) abnormalNum,
            COUNT(*) total
        FROM
            tfs_geophysical_report geo
            LEFT JOIN tfs_work_face face ON face.uuid = geo.tunnel_id
            LEFT JOIN tfs_tunnel_designs design ON design.work_face_id = face.uuid and design.del_flag = 0
        WHERE
                    geo.del_flag = 0
                AND ( geo.`status` IS NULL OR geo.`status` = 2 )
                AND face.del_flag = 0
                AND geo.org_code IN ( <foreach collection="orgCodes" item="orgCode" separator=",">#{orgCode}</foreach> )
                <if test="type != null and type == 0">
                    AND YEARWEEK( date_format( geo.work_time, '%Y-%m-%d' ), 1 ) = YEARWEEK( now(), 1 )
                </if>
                <if test="type != null and type == 1">
                    AND YEAR ( geo.work_time ) = YEAR (NOW())
                    AND MONTH ( geo.work_time ) = MONTH (NOW())
                </if>
                <if test="type != null and type == 2">
                    AND YEAR ( geo.work_time ) = YEAR (NOW())
                </if>
    </select>

    <select id="getLastGeophysical" resultType="jylink.cpds.serviceModel.dto.LastGeophysicalDto">
        SELECT
            uuid as id,
            work_time,
            work_name,
            position,
            number,
            0 AS STATUS
        FROM
            tfs_geophysical_report
        WHERE
            del_flag = 0
          AND `status` = 2
          AND org_code = #{orgCode}
          AND uuid IN ( <foreach collection="ids" item="id" separator=",">#{id}</foreach> ) UNION ALL
        (
            SELECT
                geo.uuid as id,
                geo.work_time,
                geo.work_name,
                geo.position,
                geo.number,
                1 AS STATUS
            FROM
                tfs_geophysical_report geo
                    LEFT JOIN tfs_document_expend doc ON doc.ref_data_id = geo.uuid
            WHERE
                geo.del_flag = 0
              AND geo.`status` = 2
              AND geo.org_code = #{orgCode}
              AND doc.uuid IN ( <foreach collection="ids" item="id" separator=",">#{id}</foreach> )
        )
    </select>

</mapper>