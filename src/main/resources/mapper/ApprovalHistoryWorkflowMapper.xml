<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IApprovalHistoryWorkflowDao">
    <!-- 结果映射 -->
    <resultMap type="jylink.cpds.domain.ApprovalHistoryWorkflow" id="HistoryFlowInstance">
        <id property="id" column="uuid"/>
        <result property="historyId" column="history_id"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="dutyName" column="duty_name"/>
        <result property="approvalOrder" column="approval_order"/>
        <result property="displayOrder" column="display_order"/>
        <result property="approvalReason" column="approval_reason"/>
        <result property="approvedTime" column="approved_time"/>
        <result property="approvedStatus" column="approved_status"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- 插入数据 -->
    <insert id="add">
        insert into tfs_approval_history_workflows (
        uuid,
        history_id,
        org_code,
        org_name,
        user_id,
        user_name,
        duty_name,
        approval_order,
        display_order,
        approved_time,
        approved_status,
        approval_reason,
        create_time,
        del_flag)
        values
        <foreach collection="list" separator="," item="instance">
            (
            #{instance.id},
            #{instance.historyId},
            #{instance.orgCode},
            #{instance.orgName},
            #{instance.userId},
            #{instance.userName},
            #{instance.dutyName},
            #{instance.approvalOrder},
            #{instance.displayOrder},
            #{instance.approvedTime},
            #{instance.approvedStatus},
            #{instance.approvalReason},
            #{instance.createTime},
            #{instance.delFlag}
            )
        </foreach>
    </insert>

    <!-- 根据数据Id获取最大批次 -->
    <select id="getMaxRelease" resultType="Integer">
        select max(back_version) from tfs_approval_history_workflows where approval_data_id = #{dataId} and del_flag = 0
    </select>

    <!-- 根据数据Id查询历史审批数据 -->
    <select id="getByDataId" resultMap="HistoryFlowInstance">
        select
        flow.uuid,
        flow.user_id,
        flow.user_name,
        flow.approval_data_id,
        flow.approval_order,
        flow.display_order,
        flow.approved_status,
        flow.back_version,
        flow.module_name,
        refused.uuid,
        refused.history_workflow_id,
        refused.back_reason,
        refused.back_time
        from
        tfs_approval_history_workflows as flow
        left join tfs_refused_reasons as refused
        on flow.uuid = refused.history_workflow_id
        where
        flow.approval_data_id = #{dataId}
        and
        flow.del_flag = 0
        and
        refused.del_flag = 0
        order by
        flow.approval_order,
        flow.display_order;
    </select>

    <!-- 查询是否有正常结束的 -->
    <select id="anyNormalFinish" resultType="boolean">
        select (
                   select count(*)
                   from tfs_history_analysis_works
                   where acceptance_id = #{acceptanceId}
                     and del_flag = 0
                     and status = #{status}
               ) > 0;
    </select>

    <!-- 根据数据Id删除数据 -->
    <delete id="deleteByDataId">
        update tfs_approval_history_workflows set del_flag = 1 where approval_data_id = #{id} and del_flag = 0
    </delete>
</mapper>