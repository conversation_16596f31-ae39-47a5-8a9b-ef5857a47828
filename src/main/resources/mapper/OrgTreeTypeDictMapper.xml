<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IOrgTreeTypeDictDao">
  <resultMap id="BaseResultMap" type="jylink.cpds.domain.OrgTreeTypeDict">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 09 09:49:53 CST 2020.
    -->
    <result column="uuid" jdbcType="VARCHAR" property="id" />
    <result column="tree_type" jdbcType="VARCHAR" property="treeType" />
    <result column="tree_name" jdbcType="VARCHAR" property="treeName" />
    <result column="create_time" jdbcType="DATE" property="createTime" />
    <result column="update_time" jdbcType="DATE" property="updateTime" />
  </resultMap>
  <insert id="insertSelective" parameterType="jylink.cpds.domain.OrgTreeTypeDict">
    insert into tfs_org_tree_type_dict
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        uuid,
      </if>
      <if test="treeType != null and treeType != '' ">
        tree_type,
      </if>
      <if test="treeName != null and treeName != ''">
        tree_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="treeType != null and treeType != ''">
        #{treeType,jdbcType=VARCHAR},
      </if>
      <if test="treeName != null and treeName != ''">
        #{treeName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=DATE},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=DATE},
      </if>
    </trim>
  </insert>

  <select id="selTreeTypeDict" resultMap="BaseResultMap">
    select tree_type, tree_name,uuid,create_time,update_time from tfs_org_tree_type_dict
    order by create_time desc
  </select>
  <select id="selTreeTypeDictCount" resultType="long">
    select count(1) from tfs_org_tree_type_dict
  </select>
  <select id="selAnyDict" resultMap="BaseResultMap">
    select tree_type, tree_name from tfs_org_tree_type_dict
   where tree_type = #{treeType}
  </select>
  <update id="updateTreeTypeDict">
    update tfs_org_tree_type_dict set tree_name = #{treeName} and update_time = #{updateTime} where tree_type = #{treeType}
  </update>
  <update id="deleteTreeTypeDict">
    delete from tfs_org_tree_type_dict where tree_type = #{treeType}
  </update>
</mapper>