<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.IAcceptanceCheckDao">
    <!--结果映射 -->
    <resultMap type="jylink.cpds.domain.AcceptanceCheck" id="AcceptanceCheckInstance">
        <id property="id" column="uuid"/>
        <result property="drainId" column="drain_id"/>
        <result property="planHoleNoId" column="plan_hole_no_id"/>
        <result property="workId" column="work_id"/>
        <result property="approveId" column="approve_id"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
        <result property="workName" column="work_name"/>
        <result property="surveyWaterMileage" column="survey_water_mileage"/>
        <result property="drillHolePlace" column="drill_hole_place"/>
        <result property="holeStartTime" column="hole_start_time"/>
        <result property="holeClassNumKey" column="hole_class_num_key"/>
        <result property="holeClassNumValue" column="hole_class_num_value"/>
        <result property="principalName" column="principal_name"/>
        <result property="holeOrgName" column="hole_org_name"/>
        <result property="holeNo" column="hole_no"/>
        <result property="height" column="height"/>
        <result property="holeAzimuth" column="hole_azimuth"/>
        <result property="holeObliquity" column="hole_obliquity"/>
        <result property="holeDistance" column="hole_distance"/>
        <result property="analysisHoleDistance" column="analysis_hole_distance"/>
        <result property="holeCondition" column="hole_condition"/>
        <result property="tfsMonitorName" column="tfs_monitor_name"/>
        <result property="safeName" column="safe_name"/>
        <result property="cjMonitorName" column="cj_monitor_name"/>
        <result property="checkName" column="check_name"/>
        <result property="checkTime" column="check_time"/>
        <result property="checkClassNumKey" column="check_class_num_key"/>
        <result property="checkClassNumValue" column="check_class_num_value"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="uploadTime" column="upload_time"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="holeDetailId" column="hole_detail_id"/>
        <result property="holeDiam" column="hole_diam"/>
        <result property="casingLength" column="casing_length"/>
        <result property="casingDiam" column="casing_diam"/>
    </resultMap>

    <!-- 根据机构编码查询数据 -->
    <select id="getByOrgCode" resultMap="AcceptanceCheckInstance">
        select
        uuid,
        drain_id,
        plan_hole_no_id,
        work_id,
        approve_id,
        org_code,
        org_name,
        work_name,
        survey_water_mileage,
        drill_hole_place,
        hole_start_time,
        hole_class_num_key,
        hole_class_num_value,
        principal_name,
        hole_org_name,
        hole_no,
        height,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        hole_condition,
        tfs_monitor_name,
        safe_name,
        cj_monitor_name,
        check_name,
        check_time,
        check_class_num_key,
        check_class_num_value,
        remark,
        status,
        upload_time,
        create_time,
        del_flag,
        user_id,
        hole_diam,
        casing_length,
        casing_diam,
        drill_condition,
        hole_detail_id,
        effective_flag
        from
        tfs_acceptance_checks
        where
        org_code = #{orgCode}
        and
        del_flag = 0
        order by create_time desc
    </select>

    <!-- 根据Id获取数据 -->
    <select id="getById" resultMap="AcceptanceCheckInstance">
        select
        uuid,
        drain_id,
        plan_hole_no_id,
        work_id,
        approve_id,
        org_code,
        org_name,
        work_name,
        survey_water_mileage,
        drill_hole_place,
        hole_start_time,
        hole_class_num_key,
        hole_class_num_value,
        principal_name,
        hole_org_name,
        hole_no,
        height,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        hole_condition,
        tfs_monitor_name,
        safe_name,
        cj_monitor_name,
        check_name,
        check_time,
        check_class_num_key,
        check_class_num_value,
        remark,
        status,
        upload_time,
        create_time,
        del_flag,
        user_id,
        user_Name,
        hole_diam,
        casing_length,
        casing_diam,
        drill_condition,
        hole_detail_id,
        effective_flag
        from
        tfs_acceptance_checks
        where
        uuid = #{id}
        and
        del_flag = 0
        LIMIT 1
    </select>

    <!--根据台账id进行查询-->
    <select id="getByDrainId" resultMap="AcceptanceCheckInstance">
        select
        uuid,
        drain_id,
        plan_hole_no_id,
        work_id,
        approve_id,
        org_code,
        org_name,
        work_name,
        survey_water_mileage,
        drill_hole_place,
        hole_start_time,
        hole_class_num_key,
        hole_class_num_value,
        principal_name,
        hole_org_name,
        hole_no,
        height,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        hole_condition,
        tfs_monitor_name,
        safe_name,
        cj_monitor_name,
        check_name,
        check_time,
        check_class_num_key,
        check_class_num_value,
        remark,
        status,
        upload_time,
        create_time,
        del_flag,
        user_id,
        user_Name,
        hole_diam,
        casing_length,
        casing_diam,
        drill_condition,
        hole_detail_id,
        effective_flag
        from tfs_acceptance_checks
        where drain_id = #{drainId}  and del_flag = 0
        order by hole_no
    </select>

    <!--根据台账id和钻孔编号进行查询-->
    <select id="getByDrainIdAndHoleNo" resultMap="AcceptanceCheckInstance">
        select
        uuid,
        drain_id,
        plan_hole_no_id,
        work_id,
        approve_id,
        org_code,
        org_name,
        work_name,
        survey_water_mileage,
        drill_hole_place,
        hole_start_time,
        hole_class_num_key,
        hole_class_num_value,
        principal_name,
        hole_org_name,
        hole_no,
        height,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        hole_condition,
        tfs_monitor_name,
        safe_name,
        cj_monitor_name,
        check_name,
        check_time,
        check_class_num_key,
        check_class_num_value,
        remark,
        status,
        upload_time,
        create_time,
        del_flag,
        user_id,
        hole_diam,
        casing_length,
        casing_diam,
        drill_condition,
        hole_detail_id,
        effective_flag
        from tfs_acceptance_checks
        where drain_id = #{drainId}
        and hole_no = #{holeNo}
        and del_flag = 0
        and org_code=#{orgCode}
    </select>

    <!--根据台账id进行查询所有钻孔编号-->
    <select id="getHoleNo" resultType="String">
        select
        hole_no
        from tfs_acceptance_checks
        where drain_id = #{drainId}  and del_flag = 0 and org_code=#{orgCode}
    </select>

    <!--根据台账id和钻孔编号进行查询-->
    <select id="getAddMessage" resultMap="AcceptanceCheckInstance">
        select
        uuid,
        drain_id,
        plan_hole_no_id,
        work_id,
        approve_id,
        org_code,
        org_name,
        work_name,
        survey_water_mileage,
        drill_hole_place,
        hole_start_time,
        hole_class_num_key,
        hole_class_num_value,
        principal_name,
        hole_org_name,
        hole_no,
        height,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        hole_condition,
        tfs_monitor_name,
        safe_name,
        cj_monitor_name,
        check_name,
        check_time,
        check_class_num_key,
        check_class_num_value,
        remark,
        status,
        upload_time,
        create_time,
        del_flag,
        user_id,
        hole_diam,
        casing_length,
        casing_diam,
        drill_condition,
        hole_detail_id,
        effective_flag
        from tfs_acceptance_checks
        where drain_id = #{drainId}  and del_flag = 0 and org_code=#{orgCode} and hole_no=#{holeNo}
    </select>

    <!--根据工作面编码和探水里程进行查询-->
    <select id="getCodeAndMileage" resultMap="AcceptanceCheckInstance">
        select
        a.uuid,
        a.drain_id,
        a.plan_hole_no_id,
        a.work_id,
        a.approve_id,
        a.org_code,
        a.org_name,
        a.work_name,
        a.survey_water_mileage,
        a.drill_hole_place,
        a.hole_start_time,
        a.hole_class_num_key,
        a.hole_class_num_value,
        a.principal_name,
        a.hole_org_name,
        a.hole_no,
        a.height,
        a.hole_azimuth,
        a.hole_obliquity,
        a.hole_distance,
        a.analysis_hole_distance,
        a.hole_condition,
        a.tfs_monitor_name,
        a.safe_name,
        a.cj_monitor_name,
        a.check_name,
        a.check_time,
        a.check_class_num_key,
        a.check_class_num_value,
        a.remark,
        a.status,
        a.upload_time,
        a.create_time,
        a.del_flag,
        a.user_id,
        a.hole_diam,
        a.casing_length,
        a.casing_diam,
        a.drill_condition,
        a.hole_detail_id,
        a.effective_flag
        from tfs_acceptance_checks a left join tfs_drain_accounts d
        on a.drain_id = d.uuid
        where
        a.org_code=#{orgCode}
        and
        a.del_flag = 0
        and
        d.work_code = #{workCode}
        and
        a.survey_water_mileage=#{surveyWaterMileage}
    </select>

    <!--根据工作面编码、状态和探水里程进行查询-->
    <select id="getCodeAndMileageAndStatus" resultMap="AcceptanceCheckInstance">
        select
        a.uuid,
        a.drain_id,
        a.plan_hole_no_id,
        a.work_id,
        a.approve_id,
        a.org_code,
        a.org_name,
        a.work_name,
        a.survey_water_mileage,
        a.drill_hole_place,
        a.hole_start_time,
        a.hole_class_num_key,
        a.hole_class_num_value,
        a.principal_name,
        a.hole_org_name,
        a.hole_no,
        a.height,
        a.hole_azimuth,
        a.hole_obliquity,
        a.hole_distance,
        a.analysis_hole_distance,
        a.hole_condition,
        a.tfs_monitor_name,
        a.safe_name,
        a.cj_monitor_name,
        a.check_name,
        a.check_time,
        a.check_class_num_key,
        a.check_class_num_value,
        a.remark,
        a.status,
        a.upload_time,
        a.create_time,
        a.del_flag,
        a.user_id,
        a.drill_condition,
        a.hole_detail_id,
        a.effective_flag,
        a.hole_diam,
        a.casing_length,
        a.casing_diam,
        a.user_name
        from tfs_acceptance_checks a left join tfs_drain_accounts d
        on a.drain_id = d.uuid
        where
        a.org_code=#{orgCode}
        and
        a.del_flag = 0
        and
        d.work_code = #{workCode}
        and
        a.survey_water_mileage = #{surveyWaterMileage}
    </select>

    <!--添加应用实例-->
    <insert id="add">
        insert into tfs_acceptance_checks (
        uuid,
        drain_id,
        plan_hole_no_id,
        work_id,
        approve_id,
        org_code,
        org_name,
        work_name,
        survey_water_mileage,
        drill_hole_place,
        hole_start_time,
        hole_class_num_key,
        hole_class_num_value,
        principal_name,
        hole_org_name,
        hole_no,
        height,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        hole_condition,
        tfs_monitor_name,
        safe_name,
        cj_monitor_name,
        check_name,
        check_time,
        check_class_num_key,
        check_class_num_value,
        remark,
        status,
        upload_time,
        create_time,
        del_flag,
        hole_diam,
        casing_length,
        casing_diam,
        hole_detail_id
        )
        values (
        #{instance.id},
        #{instance.drainId},
        #{instance.planHoleNoId},
        #{instance.workId},
        #{instance.approveId},
        #{instance.orgCode},
        #{instance.orgName},
        #{instance.workName},
        #{instance.surveyWaterMileage},
        #{instance.drillHolePlace},
        #{instance.holeStartTime},
        #{instance.holeClassNumKey},
        #{instance.holeClassNumValue},
        #{instance.principalName},
        #{instance.holeOrgName},
        #{instance.holeNo},
        #{instance.height},
        #{instance.holeAzimuth},
        #{instance.holeObliquity},
        #{instance.holeDistance},
        #{instance.analysisHoleDistance},
        #{instance.holeCondition},
        #{instance.tfsMonitorName},
        #{instance.safeName},
        #{instance.cjMonitorName},
        #{instance.checkName},
        #{instance.checkTime},
        #{instance.checkClassNumKey},
        #{instance.checkClassNumValue},
        #{instance.remark},
        #{instance.status},
        #{instance.uploadTime},
        #{instance.createTime},
        #{instance.delFlag},
        #{instance.holeDiam},
        #{instance.casingLength},
        #{instance.casingDiam},
        #{instance.holeDetailId}
        )
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update tfs_acceptance_checks
        set
        plan_hole_no_id=#{instance.planHoleNoId},
        work_id=#{instance.workId},
        survey_water_mileage=#{instance.surveyWaterMileage},
        hole_start_time=#{instance.holeStartTime},
        hole_class_num_key=#{instance.holeClassNumKey},
        hole_class_num_value=#{instance.holeClassNumValue},
        principal_name=#{instance.principalName},
        hole_org_name=#{instance.holeOrgName},
        hole_no=#{instance.holeNo},
        height=#{instance.height},
        hole_azimuth=#{instance.holeAzimuth},
        hole_obliquity=#{instance.holeObliquity},
        hole_distance=#{instance.holeDistance},
        analysis_hole_distance=#{instance.analysisHoleDistance},
        hole_condition=#{instance.holeCondition},
        tfs_monitor_name=#{instance.tfsMonitorName},
        safe_name=#{instance.safeName},
        cj_monitor_name=#{instance.cjMonitorName},
        check_name=#{instance.checkName},
        check_time=#{instance.checkTime},
        check_class_num_key=#{instance.checkClassNumKey},
        check_class_num_value=#{instance.checkClassNumValue},
        remark = #{instance.remark},
        hole_diam=#{instance.holeDiam},
        casing_length=#{instance.casingLength},
        casing_diam=#{instance.casingDiam},
        hole_detail_id = #{instance.holeDetailId}
        where
        uuid = #{instance.id}
        and
        org_code = #{instance.orgCode}
        and
        del_flag = 0;
    </update>

    <!-- 删除数据 -->
    <update id="delete">
        update tfs_acceptance_checks set del_flag = 1 where uuid = #{id} and org_code = #{orgCode} and del_flag = 0;
    </update>

    <!-- 根据台账Id删除数据 -->
    <update id="deleteDrainId">
        update tfs_acceptance_checks set del_flag = 1
        where drain_id = #{drainId}   and org_code = #{orgCode} and del_flag = 0;
    </update>

    <!-- 修改上报状态 -->
    <update id="upload">
        update tfs_acceptance_checks
        set status = 4 , upload_time = #{uploadTime}
        where uuid = #{id} and org_code = #{orgCode} and del_flag = 0;
    </update>

    <!-- 根据台账id数据上报 -->
    <update id="drainUpload">
        update tfs_acceptance_checks set status = 4,upload_time = #{uploadTime} where drain_id = #{drainId} and org_code = #{orgCode} and del_flag = 0;
    </update>

    <select id="anyById" resultType="boolean">
        select (
          select count(*) from tfs_acceptance_checks where uuid = #{id} and del_flag = 0
        )>0;
    </select>

    <!-- 查询数据是否已上报 -->
    <select id="existUpload" resultType="boolean">
        select (
          select count(*) from tfs_acceptance_checks where uuid = #{id} and org_code =#{orgCode} and status = 5 and del_flag = 0
        )>0;
    </select>

    <!-- 台账数据machineId是否存在 -->
    <select id="anyByDrainId" resultType="boolean">
        select (
          select count(*) from tfs_acceptance_checks where drain_id = #{drainId} and org_code =#{orgCode} and del_flag = 0
        )>0;
    </select>

    <!-- 查询数据条数 -->
    <select id="getCount" resultType="long">
        select
        count(*)
        from
        tfs_acceptance_checks
        where
        org_code = #{orgCode}
        and
        del_flag = 0
        and
        drain_id = #{drainId}
    </select>

    <!-- 根据探水验收表Id集合查询数据 -->
    <select id="getWorkName" resultType="String">
        select
        DISTINCT work_name
        from tfs_acceptance_checks
        where
        del_flag = 0
        and uuid in (<foreach collection="ids" item="id" separator=",">#{id}</foreach> )
    </select>

    <select id="getHoleNoByDrainId" resultType="jylink.cpds.serviceModel.ListItem">
        SELECT `uuid` `key`,`hole_no` `value` FROM `tfs_acceptance_checks` WHERE `drain_id` =#{drainid} AND `org_code`=#{orgcode} AND`del_flag`=0 ORDER BY `hole_no`

    </select>

    <!--修改探水下发任务 userId-->
    <update id="updateUserId">
        UPDATE
        tfs_acceptance_checks
        SET
        user_id=#{userId}
        WHERE
        uuid=#{id}
        and
        org_code=#{orgCode}
        and
        del_flag = 0
    </update>

    <!-- 添加数据（基本） -->
    <insert id="addBasic">
        insert into tfs_acceptance_checks
        (uuid,
        drain_id,
        plan_hole_no_id,
        org_code,
        org_name,
        work_name,
        survey_water_mileage,
        hole_no,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        status,
        create_time,
        del_flag,
        hole_detail_id,
        hole_diam,
        casing_length,
        casing_diam,
        user_id) values
        <foreach collection="list" item="instance" separator=",">
            (
            #{instance.id},
            #{instance.drainId},
            #{instance.planHoleNoId},
            #{instance.orgCode},
            #{instance.orgName},
            #{instance.workName},
            #{instance.surveyWaterMileage},
            #{instance.holeNo},
            #{instance.holeAzimuth},
            #{instance.holeObliquity},
            #{instance.holeDistance},
            #{instance.status},
            #{instance.createTime},
            #{instance.delFlag},
            #{instance.holeDetailId},
            #{instance.holeDiam},
            #{instance.casingLength},
            #{instance.casingDiam},
            #{instance.userId}
            )
        </foreach>
    </insert>

    <!-- 更新钻孔识别距离 -->
    <update id="updateAnalysisResult">
        update tfs_acceptance_checks set analysis_hole_distance = #{distance} where drain_id=#{drainId} and hole_no = #{holeNo} and del_flag = 0
    </update>

    <!--根据孔编号查询对象实例-->
    <select id="getByHeloNo" resultMap="AcceptanceCheckInstance">
        select
        uuid,
        drain_id,
        plan_hole_no_id,
        work_id,
        approve_id,
        org_code,
        org_name,
        work_name,
        survey_water_mileage,
        drill_hole_place,
        hole_start_time,
        hole_class_num_key,
        hole_class_num_value,
        principal_name,
        hole_org_name,
        hole_no,
        height,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        hole_condition,
        tfs_monitor_name,
        safe_name,
        cj_monitor_name,
        check_name,
        check_time,
        check_class_num_key,
        check_class_num_value,
        remark,
        status,
        upload_time,
        create_time,
        del_flag,
        user_id,
        hole_diam,
        casing_length,
        casing_diam,
        drill_condition,
        hole_detail_id,
        effective_flag
        from tfs_acceptance_checks
        where
        hole_no = #{holeNo}
        and drain_id=#{drainId}
        and del_flag = 0
    </select>

    <!--增加孔信息-->
    <insert id="addHoleDetail">
        insert into tfs_acceptance_checks (
        uuid,
        drain_id,
        org_name,
        org_code,
        work_name,
        survey_water_mileage,
        hole_no,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        create_time,
        status,
        user_id,
        hole_detail_id,
        hole_diam,
        casing_length,
        casing_diam,
        del_flag
        )
        values (
        #{instance.id},
        #{instance.drainId},
        #{instance.orgName},
        #{instance.orgCode},
        #{instance.workName},
        #{instance.surveyWaterMileage},
        #{instance.holeNo},
        #{instance.holeAzimuth},
        #{instance.holeObliquity},
        #{instance.holeDistance},
        #{instance.createTime},
        #{instance.status},
        #{instance.userId},
        #{instance.holeDiam},
        #{instance.casingLength},
        #{instance.casingDiam},
        #{instance.holeDetailId},
        0
        )
    </insert>

    <!-- 更改状态 -->
    <update id="updateStatus" >
        update tfs_acceptance_checks set status = 9
        where drain_id = #{drainId}
        and del_flag = 0
    </update>

    <!-- 根据状态查询台账Id -->
    <select id="getByStatus" resultType="String">
        select distinct
        drain_id
        from tfs_acceptance_checks
        where status = #{status}  and del_flag = 0 and org_code=#{orgCode}
    </select>

    <!-- 更新探水里程 -->
    <update id="updateSurveyWaterMileageByDrainId">
        update tfs_acceptance_checks set survey_water_mileage = #{surveyWaterMileage} where drain_id = #{drainId} and del_flag = 0
    </update>

    <!-- 根据台账Id集合获取孔数据 -->
    <select id="getByDrainIds" resultMap="AcceptanceCheckInstance">
        select *
        from tfs_acceptance_checks
        where del_flag = 0
        and drain_id in (<foreach collection="drainIds" separator="," item="drainId">#{drainId}</foreach>)
    </select>

    <!-- 根据台账Id集合获取孔数据 -->
    <select id="getHoleStatusList" resultType="jylink.cpds.serviceModel.dto.HoleListDto">
        SELECT
        acc.hole_no,
        acc.uuid AS acceptanceId,
        hole.uuid AS holeDetailId,
        hole.`status`,
        acc.drain_id
        FROM
        tfs_acceptance_checks acc
        LEFT JOIN tfs_hole_details hole ON hole.uuid = acc.hole_detail_id
        where acc.drain_id in (<foreach collection="drainIds" separator="," item="drainId">#{drainId}</foreach>)
        order by hole_no
    </select>

    <!--根据主表Id查询对象实例-->
    <select id="getByApproveId" resultMap="AcceptanceCheckInstance">
        select
        uuid,
        drain_id,
        plan_hole_no_id,
        work_id,
        approve_id,
        org_code,
        org_name,
        work_name,
        survey_water_mileage,
        drill_hole_place,
        hole_start_time,
        hole_class_num_key,
        hole_class_num_value,
        principal_name,
        hole_org_name,
        hole_no,
        height,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        hole_condition,
        tfs_monitor_name,
        safe_name,
        cj_monitor_name,
        check_name,
        check_time,
        check_class_num_key,
        check_class_num_value,
        remark,
        status,
        upload_time,
        create_time,
        del_flag,
        user_id,
        hole_diam,
        casing_length,
        casing_diam,
        drill_condition,
        hole_detail_id,
        effective_flag
        from tfs_acceptance_checks
        where
        approve_id=#{approveId}
        and del_flag = 0
        order by hole_no
    </select>

    <!-- 根据验收主表Id集合获取孔数据 -->
    <select id="getByApproveIds" resultMap="AcceptanceCheckInstance">
        select
        uuid,
        drain_id,
        plan_hole_no_id,
        work_id,
        approve_id,
        org_code,
        org_name,
        work_name,
        survey_water_mileage,
        drill_hole_place,
        hole_start_time,
        hole_class_num_key,
        hole_class_num_value,
        principal_name,
        hole_org_name,
        hole_no,
        height,
        hole_azimuth,
        hole_obliquity,
        hole_distance,
        analysis_hole_distance,
        hole_condition,
        tfs_monitor_name,
        safe_name,
        cj_monitor_name,
        check_name,
        check_time,
        check_class_num_key,
        check_class_num_value,
        remark,
        status,
        upload_time,
        create_time,
        del_flag,
        user_id,
        hole_diam,
        casing_length,
        casing_diam,
        drill_condition,
        hole_detail_id,
        effective_flag
        from tfs_acceptance_checks
        where del_flag = 0
        and approve_id in (<foreach collection="approveIds" separator="," item="drainId">#{drainId}</foreach>)
    </select>

    <!-- 根据Id获取数据 -->
    <select id="getByHoleDetailId" resultMap="AcceptanceCheckInstance">
        select
            uuid,
            drain_id,
            plan_hole_no_id,
            work_id,
            approve_id,
            org_code,
            org_name,
            work_name,
            survey_water_mileage,
            drill_hole_place,
            hole_start_time,
            hole_class_num_key,
            hole_class_num_value,
            principal_name,
            hole_org_name,
            hole_no,
            height,
            hole_azimuth,
            hole_obliquity,
            hole_distance,
            analysis_hole_distance,
            hole_condition,
            tfs_monitor_name,
            safe_name,
            cj_monitor_name,
            check_name,
            check_time,
            check_class_num_key,
            check_class_num_value,
            remark,
            status,
            upload_time,
            create_time,
            del_flag,
            user_id,
            user_Name,
            hole_diam,
            casing_length,
            casing_diam,
            drill_condition,
            hole_detail_id,
            effective_flag
        from
            tfs_acceptance_checks
        where
            hole_detail_id = #{holeDetailId}
          and
            del_flag = 0

    </select>

</mapper>
