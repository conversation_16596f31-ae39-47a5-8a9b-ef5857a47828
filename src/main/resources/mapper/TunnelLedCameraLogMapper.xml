<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="jylink.cpds.dao.ITunnelLedCameraLogDao">

    <resultMap type="jylink.cpds.domain.TunnelLedCameraLog" id="TunnelLedCameraLogMap">
        <result property="id" column="uuid" jdbcType="VARCHAR"/>
        <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
        <result property="diyColumn" column="diy_column" jdbcType="VARCHAR"/>
        <result property="extendedFields" column="extended_fields" jdbcType="VARCHAR"/>
        <result property="cameraIp" column="camera_ip" jdbcType="VARCHAR"/>
        <result property="cameraName" column="camera_name" jdbcType="VARCHAR"/>
        <result property="workFaceId" column="work_face_id" jdbcType="VARCHAR"/>
        <result property="workName" column="work_name" jdbcType="VARCHAR"/>
        <result property="ledId" column="led_id" jdbcType="VARCHAR"/>
        <result property="configId" column="config_id" jdbcType="VARCHAR"/>
        <result property="bindTime" column="bind_time" jdbcType="TIMESTAMP"/>
        <result property="bindUserId" column="bind_user_id" jdbcType="VARCHAR"/>
        <result property="bindUserName" column="bind_user_name" jdbcType="VARCHAR"/>
        <result property="unbindTime" column="unbind_time" jdbcType="TIMESTAMP"/>
        <result property="unbindUserId" column="unbind_user_id" jdbcType="VARCHAR"/>
        <result property="indexCode" column="indexCode" jdbcType="VARCHAR"/>
        <result property="channel" column="channel" jdbcType="INTEGER"/>
        <result property="unbindUserName" column="unbind_user_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!--查询单个,主键查询-->
    <select id="queryById" resultMap="TunnelLedCameraLogMap">
        select
          uuid, org_code, org_name, create_time, update_time, del_flag, diy_column, extended_fields, camera_ip, camera_name, work_face_id, work_name, led_id, config_id, bind_time, bind_user_id, bind_user_name, unbind_time, unbind_user_id, unbind_user_name
        from tfs_tunnel_led_camera_log
        where uuid = #{id} and del_flag = 0 and org_code = #{orgCode}
    </select> 

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="TunnelLedCameraLogMap">
        select uuid, org_code, org_name, create_time,  del_flag, diy_column, extended_fields, camera_ip, camera_name, work_face_id, work_name, led_id, config_id, bind_time, bind_user_id, bind_user_name, unbind_time, unbind_user_id, unbind_user_name,
        JSON_UNQUOTE(
        JSON_EXTRACT ( extended_fields, '$.indexCode' )) AS indexCode,
        JSON_UNQUOTE(
        JSON_EXTRACT ( extended_fields, '$.channel' )) AS channel
          from tfs_tunnel_led_camera_log
        <where>
            del_flag = #{delFlag}
            <if test=" id != null and id != ''">
                and uuid = #{id}
            </if>

            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="diyColumn != null and diyColumn != ''">
                and diy_column = #{diyColumn}
            </if>
            <if test="extendedFields != null and extendedFields != ''">
                and extended_fields = #{extendedFields}
            </if>
            <if test="cameraIp != null and cameraIp != ''">
                and camera_ip = #{cameraIp}
            </if>
            <if test="cameraName != null and cameraName != ''">
                and camera_name = #{cameraName}
            </if>
            <if test="workFaceId != null and workFaceId != ''">
                and work_face_id = #{workFaceId}
            </if>
            <if test="workName != null and workName != ''">
                and work_name = #{workName}
            </if>
            <if test="ledId != null and ledId != ''">
                and led_id = #{ledId}
            </if>
            <if test="configId != null and configId != ''">
                and config_id = #{configId}
            </if>
            <if test="bindTime != null">
                and bind_time = #{bindTime}
            </if>
            <if test="bindUserId != null and bindUserId != ''">
                and bind_user_id = #{bindUserId}
            </if>
            <if test="bindUserName != null and bindUserName != ''">
                and bind_user_name = #{bindUserName}
            </if>
            <if test="unbindTime != null">
                and unbind_time = #{unbindTime}
            </if>
            <if test="unbindUserId != null and unbindUserId != ''">
                and unbind_user_id = #{unbindUserId}
            </if>
            <if test="unbindUserName != null and unbindUserName != ''">
                and unbind_user_name = #{unbindUserName}
            </if>
        </where>
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="getIndexCodeList" resultMap="TunnelLedCameraLogMap">
        select uuid, org_code, org_name, create_time,  del_flag, diy_column, extended_fields, camera_ip, camera_name, work_face_id, work_name, led_id, config_id, bind_time, bind_user_id, bind_user_name, unbind_time, unbind_user_id, unbind_user_name,
        JSON_UNQUOTE(
        JSON_EXTRACT ( extended_fields, '$.indexCode' )) AS indexCode,
        JSON_UNQUOTE(
        JSON_EXTRACT ( extended_fields, '$.channel' )) AS channel
        from tfs_tunnel_led_camera_log
        where
        del_flag = 0
        and work_face_id = #{workFaceId}
        and org_code = #{orgCode}
        and LENGTH(JSON_EXTRACT ( extended_fields, '$.indexCode' )) > 0
        order by bind_time desc
    </select>

    <select id="getIndexCodeCount" resultType="long">
        select count(*)
        from tfs_tunnel_led_camera_log
        where
            del_flag = 0
          and work_face_id = #{workFaceId}
          and org_code = #{orgCode}
          and LENGTH(JSON_EXTRACT ( extended_fields, '$.indexCode' )) > 0
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAllCount" resultType="long">
        select count(1) from tfs_tunnel_led_camera_log
        <where>
            del_flag = #{delFlag}
            <if test=" id != null and id != ''">
                and uuid = #{id}
            </if>

            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="orgName != null and orgName != ''">
                and org_name = #{orgName}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="diyColumn != null and diyColumn != ''">
                and diy_column = #{diyColumn}
            </if>
            <if test="extendedFields != null and extendedFields != ''">
                and extended_fields = #{extendedFields}
            </if>
            <if test="cameraIp != null and cameraIp != ''">
                and camera_ip = #{cameraIp}
            </if>
            <if test="cameraName != null and cameraName != ''">
                and camera_name = #{cameraName}
            </if>
            <if test="workFaceId != null and workFaceId != ''">
                and work_face_id = #{workFaceId}
            </if>
            <if test="workName != null and workName != ''">
                and work_name = #{workName}
            </if>
            <if test="ledId != null and ledId != ''">
                and led_id = #{ledId}
            </if>
            <if test="configId != null and configId != ''">
                and config_id = #{configId}
            </if>
            <if test="bindTime != null">
                and bind_time = #{bindTime}
            </if>
            <if test="bindUserId != null and bindUserId != ''">
                and bind_user_id = #{bindUserId}
            </if>
            <if test="bindUserName != null and bindUserName != ''">
                and bind_user_name = #{bindUserName}
            </if>
            <if test="unbindTime != null">
                and unbind_time = #{unbindTime}
            </if>
            <if test="unbindUserId != null and unbindUserId != ''">
                and unbind_user_id = #{unbindUserId}
            </if>
            <if test="unbindUserName != null and unbindUserName != ''">
                and unbind_user_name = #{unbindUserName}
            </if>
            <if test="indexCode != null and indexCode != ''">
                and unbind_user_name = #{indexCode}
            </if>
        </where>
    </select>

    <select id="getCameraServiceLogCount" resultType="long">
        select count(1) from tfs_tunnel_led_camera_log
        <where>
            del_flag = 0
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="cameraIp != null and cameraIp != ''">
                and camera_ip = #{cameraIp}
            </if>
            <if test="workFaceId != null and workFaceId != ''">
                and work_face_id = #{workFaceId}
            </if>
            <if test="bindStartTime != null and bindEndTime == null">
                and DATE_FORMAT(bind_time,'%Y-%m-%d') &gt;= #{bindStartTime}
            </if>
            <if test="bindStartTime != null and bindEndTime != null">
                and DATE_FORMAT(bind_time,'%Y-%m-%d') between #{bindStartTime} and #{bindEndTime}
            </if>
            <if test="bindStartTime == null and bindEndTime != null">
                and DATE_FORMAT(bind_time,'%Y-%m-%d') &lt;= #{bindEndTime}
            </if>
            <if test="unbindStartTime != null and unbindEndTime == null">
                and DATE_FORMAT(unbind_time,'%Y-%m-%d') &gt;= #{unbindStartTime}
            </if>
            <if test="unbindStartTime != null and unbindEndTime != null">
                and DATE_FORMAT(unbind_time,'%Y-%m-%d') between #{unbindStartTime} and #{unbindEndTime}
            </if>
            <if test="unbindStartTime == null and unbindEndTime != null">
                and DATE_FORMAT(unbind_time,'%Y-%m-%d') &lt;= #{unbindEndTime}
            </if>
        </where>
    </select>

    <select id="getCameraServiceLogs" resultMap="TunnelLedCameraLogMap">
        select uuid, org_code, org_name, create_time, update_time, del_flag, camera_ip, camera_name, work_face_id, work_name, led_id, config_id, bind_time, bind_user_id, bind_user_name, unbind_time, unbind_user_id, unbind_user_name
          from tfs_tunnel_led_camera_log
        <where>
            del_flag = 0
            <if test="orgCode != null and orgCode != ''">
                and org_code = #{orgCode}
            </if>
            <if test="cameraIp != null and cameraIp != ''">
                and camera_ip = #{cameraIp}
            </if>
            <if test="workFaceId != null and workFaceId != ''">
                and work_face_id = #{workFaceId}
            </if>
            <if test="bindStartTime != null and bindEndTime == null">
                and DATE_FORMAT(bind_time,'%Y-%m-%d') &gt;= #{bindStartTime}
            </if>
            <if test="bindStartTime != null and bindEndTime != null">
                and DATE_FORMAT(bind_time,'%Y-%m-%d') between #{bindStartTime} and #{bindEndTime}
            </if>
            <if test="bindStartTime == null and bindEndTime != null">
                and DATE_FORMAT(bind_time,'%Y-%m-%d') &lt;= #{bindEndTime}
            </if>
            <if test="unbindStartTime != null and unbindEndTime == null">
                and DATE_FORMAT(unbind_time,'%Y-%m-%d') &gt;= #{unbindStartTime}
            </if>
            <if test="unbindStartTime != null and unbindEndTime != null">
                and DATE_FORMAT(unbind_time,'%Y-%m-%d') between #{unbindStartTime} and #{unbindEndTime}
            </if>
            <if test="unbindStartTime == null and unbindEndTime != null">
                and DATE_FORMAT(unbind_time,'%Y-%m-%d') &lt;= #{unbindEndTime}
            </if>
        </where>
        order by bind_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert" >
        insert into tfs_tunnel_led_camera_log(uuid, org_code, org_name, create_time, update_time, del_flag, diy_column, extended_fields, camera_ip, camera_name, work_face_id, work_name, led_id, config_id, bind_time, bind_user_id, bind_user_name, unbind_time, unbind_user_id, unbind_user_name)
        values (    #{id} ,     #{orgCode} ,     #{orgName} ,     #{createTime} ,     #{updateTime} ,     #{delFlag} ,     #{diyColumn} ,     #{extendedFields} ,     #{cameraIp} ,     #{cameraName} ,     #{workFaceId} ,     #{workName} ,     #{ledId} ,     #{configId} ,     #{bindTime} ,     #{bindUserId} ,     #{bindUserName} ,     #{unbindTime} ,     #{unbindUserId} ,     #{unbindUserName} )
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tfs_tunnel_led_camera_log
        <set>
            <if test="orgCode != null and orgCode != ''">
                org_code = #{orgCode},
            </if>
            <if test="orgName != null and orgName != ''">
                org_name = #{orgName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="diyColumn != null and diyColumn != ''">
                diy_column = #{diyColumn},
            </if>
            <if test="extendedFields != null and extendedFields != ''">
                extended_fields = #{extendedFields},
            </if>
            <if test="cameraIp != null and cameraIp != ''">
                camera_ip = #{cameraIp},
            </if>
            <if test="cameraName != null and cameraName != ''">
                camera_name = #{cameraName},
            </if>
            <if test="workFaceId != null and workFaceId != ''">
                work_face_id = #{workFaceId},
            </if>
            <if test="workName != null and workName != ''">
                work_name = #{workName},
            </if>
            <if test="ledId != null and ledId != ''">
                led_id = #{ledId},
            </if>
            <if test="configId != null and configId != ''">
                config_id = #{configId},
            </if>
            <if test="bindTime != null">
                bind_time = #{bindTime},
            </if>
            <if test="bindUserId != null and bindUserId != ''">
                bind_user_id = #{bindUserId},
            </if>
            <if test="bindUserName != null and bindUserName != ''">
                bind_user_name = #{bindUserName},
            </if>
            <if test="unbindTime != null">
                unbind_time = #{unbindTime},
            </if>
            <if test="unbindUserId != null and unbindUserId != ''">
                unbind_user_id = #{unbindUserId},
            </if>
            <if test="unbindUserName != null and unbindUserName != ''">
                unbind_user_name = #{unbindUserName},
            </if>
        </set>
        where uuid = #{id} and del_flag = 0
    </update>

    <!--通过主键逻辑删除-->
    <update id="deleteById">
        update tfs_tunnel_led_camera_log set del_flag = 1 where uuid = #{id} and del_flag = 0
    </update>

</mapper>
