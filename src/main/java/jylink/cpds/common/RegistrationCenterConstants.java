package jylink.cpds.common;

/**
 * @Author: hupf
 * @Data:2023/3/2 9:54
 * @Description: 注册中心相关常量类
 */
public class RegistrationCenterConstants {


    public static final String NACOS = "nacos";

    public static final String EUREKA = "eureka";

    public static final String EUREKA_AUTO_CONFIGURATION_CLASSES = "org.springframework.cloud.netflix.eureka.EurekaClientAutoConfiguration";

    public static final String EUREKA_DISCOVERY_CLIENT_CONFIGURATION = "org.springframework.cloud.netflix.eureka.EurekaDiscoveryClientConfiguration";

    public static final String NACOS_SERVICE_REGISTRY_AUTO_CONFIGURATION = "com.alibaba.cloud.nacos.registry.NacosServiceRegistryAutoConfiguration";

    public static final String NACOS_RIBBON_AUOT_CONFIGURATION = "com.alibaba.cloud.nacos.ribbon.RibbonNacosAutoConfiguration";

}
