package jylink.cpds.common;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import java.util.Date;
import java.util.Objects;

/**
 * @DESCRIPTION: 时间工具类$
 * @author: hanpt
 * @DATE: 2021/1/21 15:40
 */
public class DateUtils extends DateUtil {
    public enum CalculationEnum {
        /**
         * 加
         */
        ADD,
        /**
         * 减
         */
        SUBTRACT;
    }

    public static String getInterval(long timeMillis) {
        // 秒
        long allMills = timeMillis / 1000;
        String hours = String.valueOf(allMills / (60 * 60));
        String minute = String.valueOf((allMills % (60 * 60)) / 60);
        String mills = String.valueOf(allMills % (60 * 60) % 60);
        return (hours.length() == 1? ("0"+hours) : hours )+ ":" + (minute.length()==1?("0"+minute):minute ) + ":" + (mills.length()==1?("0"+mills):mills);
    }

    public static boolean isBefore(Date date, Date checkDate) {
        if (Objects.isNull(date) || Objects.isNull(checkDate)){
            return false;
        }
        if (date.compareTo(checkDate) == 0){
            return true;
        }
        return date instanceof DateTime ? ((DateTime)date).isBefore(checkDate) : (new DateTime(date)).isBefore(checkDate);
    }
    public static boolean isAfter(Date date, Date checkDate) {
        if (Objects.isNull(date) || Objects.isNull(checkDate)){
            return false;
        }
        if (date.compareTo(checkDate) == 0){
            return true;
        }
        return date instanceof DateTime ? ((DateTime)date).isAfter(checkDate) : (new DateTime(date)).isAfter(checkDate);
    }

    /**
     * 时间间隔
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return
     */
    public static long timeInterval(Date startDate,Date endDate){
        return (endDate.getTime() - startDate.getTime())/1000;
    }
    /**
     *  时间,s +- 计算时间
     * <AUTHOR>
     * @date 2021/9/26 10:23
     * @param sourceDate 源时间
     * @param millis 秒
     * @param calculationEnum 计算枚举
     * @return java.util.Date
     */
    public static Date calculationDate(Date sourceDate, long millis, CalculationEnum calculationEnum) {
        if (calculationEnum == CalculationEnum.ADD) {
            // 加法
            return new Date(sourceDate.getTime() + millis * 1000);
        }
        if (calculationEnum == CalculationEnum.SUBTRACT) {
            // 减法
            return new Date(sourceDate.getTime() - millis * 1000);
        }
        return sourceDate;
    }
}
