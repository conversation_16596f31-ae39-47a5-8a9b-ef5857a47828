package jylink.cpds.common;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理验证失败异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Object> handleValidationExceptions(MethodArgumentNotValidException ex) {
        // 提取所有错误消息
        String errors = ex.getBindingResult().getAllErrors().get(0).getDefaultMessage();
        // 3. 创建响应体
        Map<String, Object> body = new LinkedHashMap<>();
        body.put("statusCode", HttpStatus.BAD_REQUEST.value());
        // 只包含消息列表
        body.put("messages", errors);
        body.put("data",null);
        return ResponseEntity.badRequest().body(body);
    }
}
