package jylink.cpds.common;

import com.github.dozermapper.core.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Dozer帮助类
 */
@Component
public class DozerUtils {
    /**
     * Dozer实例
     */
    @Autowired
    private Mapper mapper;

    /**
     * Dozer List to List
     *
     * @param resource       数据对象
     * @param destination    目标类型
     * @param <TSource>      源类型
     * @param <TDestination> 目标类型
     * @return 目标类型集合
     */
    public <TSource, TDestination> List<TDestination> mapList(List<TSource> resource, Class<TDestination> destination) {
        List<TDestination> listDestination = new ArrayList<>();
        for (TSource item : resource) {
            TDestination model = mapper.map(item, destination);
            listDestination.add(model);
        }
        return listDestination;
    }

    /**
     * Dozer List to List
     *
     * @param resource       数据对象
     * @param destination    目标类型
     * @param mapId          mappingId
     * @param <TSource>      源类型
     * @param <TDestination> 目标类型
     * @return 目标类型集合
     */
    public <TSource, TDestination> List<TDestination> mapList(List<TSource> resource, Class<TDestination> destination, String mapId) {
        List<TDestination> listDestination = new ArrayList<>();
        for (TSource item : resource) {
            TDestination model = mapper.map(item, destination, mapId);
            listDestination.add(model);
        }
        return listDestination;
    }

    /**
     * Dozer List to List
     *
     * @param resource       数据对象
     * @param destination    目标类型
     * @param <TSource>      源类型
     * @param <TDestination> 目标类型
     */
    public <TSource, TDestination> void mapList(List<TSource> resource, List<TDestination> destination) {
        if (resource.size() != destination.size()) {
            throw new IllegalArgumentException("转换集合对象数量不相等！");
        }

        for (int i = 0; i < resource.size(); i++) {
            TSource resourceModel = resource.get(i);
            TDestination destinationModel = destination.get(i);

            mapper.map(resourceModel, destinationModel);
        }
    }

    public  <TSource, TDestination> List<List<TDestination>> list2ListList(List<List<TSource>> resource, Class<TDestination> destination) {
        List<List<TDestination>> list= new ArrayList<>();
        for (int i = 0; i < resource.size(); i++) {
            List subList = new ArrayList();
            for (int i1 = 0; i1 < resource.get(i).size(); i1++) {
                TSource resourceModel = resource.get(i).get(i1);
                TDestination tDestination = mapper.map(resourceModel, destination);
                subList.add(tDestination);
            }
            list.add(subList);
        }
        return list;
    }
}
