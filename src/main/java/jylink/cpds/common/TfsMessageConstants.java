package jylink.cpds.common;

public class TfsMessageConstants {



    /**
     * 数据id错误返回值
     */
    public static final String ID_NOT_FOUND = "数据id错误";

    /**
     * "数据状态冲突！"返回值
     */
    public static final String STATUS_CONFLICT = "数据状态冲突！";

    /**
     * "数据状态错误！"返回值
     */
    public static final String STATUS_ERROR = "数据状态错误";

    /**
     * 台账id错误返回值
     */
    public static final String DRAIN_ID_NOT_FOUND = "台账id错误!";

    /**
     * 计划id错误返回值
     */
    public static final String PLAN_ID_NOT_FOUND = "计划id错误";

    /**
     * 工作面id错误返回值
     */
    public static final String WORK_FACE_ID_NOT_FOUND = "工作面id错误";

    /**
     * 台账id错误返回值
     */
    public static final String HOLE_DETAIL_ID_NOT_FOUND = "孔数据Id错误！";

    /**
     * 数据id错误返回值
     */
    public static final String ADD_FAIL = "添加失败！";

    /**
     * 请求参数不正确错误返回值
     */
    public static final String REQUEST_PARAMETER_ERROR = "请求参数不正确";

    /**
     * 文件为空错误返回值
     */
    public static final String FILE_EMPTY = "文件为空!";

    /**
     * 文件保存失败错误返回值
     */
    public static final String FILE_SAVE_ERROR = "文件保存失败";

    /**
     * 文件格式错误返回值
     */
    public static final String FILE_FORMAT_ERROR = "文件格式错误";

    /**
     * 修改状态失提示语
     */
    public static final String STATUS_UPDATE_ERROR = "修改状态失败";

    /**
     * 视频剪辑线程结束提示语
     */
    public static final String VIDEO_THREAD_FINISH = "视频剪辑线程结束";

    /**
     * 获取摄像头信息失败消息提示
     */
    public static final String GET_CAMERA_FAIL = "获取摄像头信息失败！";

    /**
     * 作业班次不能早于最近一次的作业班次消息提示
     */
    public static final String WORK_CLASS_COMPARE = "作业班次不能早于最近一次的作业班次";

    /**
     * AI对接失败
     */
    public static final String AI_PAIR_FAIL = "AI对接失败";

    /**
     * AI对接失败
     */
    public static final String AI_PAIR_SUCCESS = "AI对接成功";

    /**
     * AI反馈结果
     */
    public static final String AI_FEEDBACK_RESULT = "AI反馈结果：";

    /**
     * 台账id输入错误提示语
     */
    public static final String DRAIN_ID_INPUT_ERROR = "台账id输入错误!";

    /**
     * 作业信息验证通过提示语
     */
    public static final String JOB_INFORMATION_VERIFICATION_PASSED = "作业信息验证通过";

    /**
     * 该孔已验收提示语
     */
    public static final String HOLE_ACCEPTED = "该孔已验收";

    /**
     * 台账对应设计数据为空提示语
     */
    public static final String TUNNEL_DATA_EMPTY = "台账对应设计数据为空,请检查数据正确性!";

    /**
     * "AI反馈MQUrl为空"提示语
     */
    public static final String MQ_URL_EMPTY = "AI反馈MQUrl为空";

    /**
     * 没有找到配置数据，请先绑定硬件提示语
     */
    public static final String CONFIGURATION_DATA_EMPTY = "没有找到配置数据，请先绑定硬件！";

    /**
     * 未进行煤矿大脑配置绑定，请先绑定煤矿大脑！提示语
     */
    public static final String COAL_MINE_BRAIN_EMPTY = "未进行煤矿大脑配置绑定，请先绑定煤矿大脑！";

    /**
     * 未找到接收单位的详细信息提示语
     */
    public static final String RECEIVE_ORG_INFO_NOT_FOUND = "未找到接收单位的详细信息";

    /**
     * "未配置三单两表模板!"提示语
     */
    public static final String NO_TEMPLATE_CONFIGURED = "未配置三单两表模板!";

    /**
     * "矿机构不允许添加子节点"提示语
     */
    public static final String ORG_ADD_CHILD_NODE = "矿机构不允许添加子节点";

    /**
     * 获取平台数据失败提示语
     */
    public static final String GET_PLAT_INFO_FAIL = "获取平台数据失败";

    /**
     * 用户权限不足提示语
     */
    public static final String INSUFFICIENT_PERMISSIONS = "用户权限不足";

    /**
     * "更新历史任务表状态失败！！"提示语
     */
    public static final String UPDATE_HISTORY_WORK_FAIL = "更新历史任务表状态失败！！";

    /**
     * "更新实时任务表失败！！"提示语
     */
    public static final String UPDATE_REAL_WORK_FAIL = "更新实时任务表失败！！";

    /**
     * 指令未识别提示语
     */
    public static final String INSTRUCTION_NOT_RECOGNIZED = "指令未识别";

    /**
     * 指令未识别提示语
     */
    public static final String HOLE_TASK_ISSUED_SUCCESS = "孔任务下发成功";

    /**
     * 提交审批成功提示语
     */
    public static final String SUBMIT_SUCCESS = "提交审批成功!";

    /**
     * "提交审批失败!"提示语
     */
    public static final String SUBMIT_FAIL = "提交审批失败!";

    /**
     * 班次标识错误提示语
     */
    public static final String CLASS_FLAG_ERROR = "班次标识错误";

    /**
     * 该机构下不存在数据提示语
     */
    public static final String ORG_NO_DATA = "该机构下不存在数据！";

    /**
     * 该机构下不存在数据提示语
     */
    public static final String KEY_CAN_NOT_NULL = "Key不允许为null！";

    /**
     * 任务未找到提示语
     */
    public static final String JOB_NOT_FOUND = "该任务未找到";

    /**
     * 该用户已完成审批提示语
     */
    public static final String USER_COMPLETE_APPROVAL = "该用户已完成审批！";

    /**
     * 表单参数不正确提示语
     */
    public static final String FORM_PARAMETER_ERROR = "表单参数不正确";

    /**
     * 同步监控提示语
     */
    public static final String SYN_MONITOR = "[同步监控]";

    /**
     * 同步监控提示语
     */
    public static final String AI_INTERACTIVE_FAIL = "AI交互失败";

    /**
     * 订单状态有误提示语
     */
    public static final String ORDER_STATUS_ERROR = "订单状态有误,请稍后再试";

    /**
     * 同步监控提示语
     */
    public static final String AI_INTERACTIVE_SUCCESS = "AI交互成功";

    /**
     * 煤矿大脑客户端登录失败提示语
     */
    public static final String COAL_MINE_LOGIN_ERROR = "煤矿大脑客户端登录失败!";

    private TfsMessageConstants() {
    }

}
