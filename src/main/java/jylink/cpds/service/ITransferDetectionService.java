package jylink.cpds.service;

import jylink.cpds.serviceModel.dto.AddTransferDetectionDto;
import jylink.cpds.serviceModel.dto.TransferDetectionDto;
import jylink.cpds.serviceModel.params.ApprovalReasonEditModel;
import jylink.cpds.serviceModel.params.ApprovalWorkflowParamsModel;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
/**
 * <AUTHOR>
 * 探放水安全确认移交表 服务接口
 */
public interface ITransferDetectionService {
    /**
     * 根据id查询
     *
     * @param id 查询信息
     * @return dto模型集合
     */
    TransferDetectionDto getById(String id,String orgCode);

    /**
     * 根据台账id查询
     *
     * @param drainId 台账id
     * @param orgCode 机构编码
     * @return 对象实例
     */
    TransferDetectionDto getByDrainId( String drainId,  String orgCode);

    /**
     * 根据台账id查询 添加所需要的信息
     *
     * @param drainId 台账id
     * @param orgCode 机构编码
     * @return 对象实例
     */
    AddTransferDetectionDto getAddMessage(String drainId, String orgCode);

    /**
     * 根据机构编码查询
     *
     * @param orgCode 查询信息
     * @return dto模型集合
     */
    List<TransferDetectionDto> getByOrgCode(String orgCode);

    /**
     * 根据工作面名称和探水时间查询
     *
     * @param workName  工作面名称
     * @param surveyWaterDate  探水时间
     * @param   orgCode   根据机构编码
     * @return 对象实例
     */
    List<TransferDetectionDto> getByWorkAndDate(@Param("workName") String workName, @Param("surveyWaterDate") String surveyWaterDate, @Param("orgCode") String orgCode);

    /**
     * 添加数据
     *
     * @param instance 对象实例
     * @return 是否添加成功
     */
    boolean add(TransferDetectionDto instance,List<List<ApprovalWorkflowParamsModel>> flows);

    /**
     * 添加验收信息
     * @param instance
     * @return
     */
    boolean addMessage(TransferDetectionDto instance,String drainId);

    /**
     * 删除数据
     *
     * @param id      数据Id
     * @param orgCode 机构编码
     * @return 是否删除成功
     */
    boolean delete(String id, String orgCode);

    /**
     * 根据台账删除数据
     *
     * @param drainId      数据drainId
     * @param orgCode 机构编码
     * @return 是否删除成功
     */
    boolean deleteDrainId(String drainId, String orgCode);

    /**
     * 修改数据
     *
     * @param instance 对象实例
     * @return 是否修改成功
     */
    boolean update(TransferDetectionDto instance,List<List<ApprovalWorkflowParamsModel>> flows);

    /**
     * 修改验收信息
     *
     * @param instance 对象实例
     * @return  是否修改成功
     */
    boolean updateMessage(TransferDetectionDto instance);
    /**
     * 根据台账id更新数据
     *
     * @param instance 对象实例
     * @return 是否修改成功
     */
    boolean updateDrainId(TransferDetectionDto instance);

    /**
     * 上报
     *
     * @param id         数据id
     * @param orgCode    机构编码
     * @param uploadTime 上报时间
     * @return 是否上报成功
     */
    boolean upload(String id, String orgCode, Date uploadTime);

    /**
     * 上级单位已经查看
     *
     * @param id      数据Id
     * @param orgCode 机构编码
     * @return 是否更改为已查看状态
     */
    boolean check(String id, String orgCode);

    /**
     * 判断数据是否存在
     *
     * @param id      数据Id
     * @param orgCode 机构编码
     * @return 数据是否存在
     */
    boolean anyById(String id, String orgCode);

    /**
     * 判断台账id数据是否存在
     *
     * @param drainId      drainId
     * @param orgCode 机构编码
     * @return 数据是否存在
     */
    boolean anyByDrainId(String drainId,  String orgCode);

    /**
     * 根据设计Id获取数据
     *
     * @param orgCode  机构编码
     * @param tunnelId 探水设计Id
     * @return 查询结果
     */
    List<TransferDetectionDto> getByTunnelId(String orgCode, String tunnelId);

    /**
     * 修改审批状态
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  是否成功
     */
    boolean updateStatus(String id,String orgCode,Integer status);

    /**
     * 审批
     *
     * @param model 参数对象
     * @param orgCode 机构编码
     * @return 是否成功
     */
    boolean approved(ApprovalReasonEditModel model,String orgCode);

    /**
     * 进入审批
     * @param id 数据Id
     * @param orgCode 机构编码
     * @return 是否成功
     */
    boolean approving(String id,String orgCode);

    /**
     * 拒绝
     * @param model 参数对象
     * @param orgCode 机构编码
     * @return 是否成功
     */
    boolean refused(ApprovalReasonEditModel model,String orgCode);

    /**
     * 撤回
     * @param model 参数对象
     * @param orgCode 机构编码
     * @return 是否成功
     */
    boolean revoke(ApprovalReasonEditModel model,String orgCode);
}
