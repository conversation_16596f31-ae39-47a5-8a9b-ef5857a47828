package jylink.cpds.service.impl;

import com.ada.jykjcloudx.sdk.security.service.JykjCloudxUser;
import com.ada.jykjcloudx.sdk.security.util.SecurityUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.CpdsConstants;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.config.MinioConfig;
import jylink.cpds.config.ObsProConfig;
import jylink.cpds.dao.IFileDao;
import jylink.cpds.dao.IHistoryWarnMessageDao;
import jylink.cpds.dao.IWarnMessageDao;
import jylink.cpds.dao.IWarnMessageReplyDao;
import jylink.cpds.domain.File;
import jylink.cpds.domain.HistoryWarnMessage;
import jylink.cpds.domain.WarnMessage;
import jylink.cpds.domain.WarnMessageReply;
import jylink.cpds.service.IWarnMessageReplyService;
import jylink.cpds.serviceModel.WarnMessageReplyType;
import jylink.cpds.serviceModel.dto.FileDto;
import jylink.cpds.serviceModel.dto.WarnMessageReplyDto;
import org.jetbrains.annotations.NotNull;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Service
public class WarnMessageReplyServiceImpl extends ServiceImpl<IWarnMessageReplyDao, WarnMessageReply> implements IWarnMessageReplyService {

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private IWarnMessageReplyDao warnMessageReplyDao;

    /**
     * dozer转换类
     */
    @Autowired
    private Mapper mapper;

    /**
     * dozer帮助类
     */
    @Autowired
    private DozerUtils mapperUtils;

    @Autowired
    private IWarnMessageDao warnMessageDao;

    @Autowired
    private IHistoryWarnMessageDao historyWarnMessageDao;

    @Autowired
    private ObsProConfig obsProConfig;

    /**
     * 文件服务
     */
    @Autowired
    private IFileDao fileDao;

    @Override
    public boolean insert(WarnMessageReplyDto warnMessageReplyDto, String orgCode) {
        WarnMessageReply warnMessageReply = mapper.map(warnMessageReplyDto, WarnMessageReply.class);
        warnMessageReply.setCreateTime(new Date());
        WarnMessage warnMessage = warnMessageDao.getOnlyById(warnMessageReplyDto.getWarnId());
        List<HistoryWarnMessage> historyWarnMessages = historyWarnMessageDao.getBydataIdList(Arrays.asList(warnMessageReply.getWarnId()));
        List<WarnMessageReplyDto> replyDtos = getByWarnId(warnMessageReply.getWarnId());
        if (warnMessage != null) {
            warnMessageReply.setOrgCode(warnMessage.getOrgCode());
            warnMessageReply.setOrgName(warnMessage.getOrgName());
        }else {
            warnMessageReply.setOrgName(historyWarnMessages.get(0).getOrgName());
            warnMessageReply.setOrgCode(historyWarnMessages.get(0).getOrgCode());
        }
        if ((warnMessageReply.getReplyType() == null || warnMessageReply.getReplyType() == 0) && orgCode.equals(warnMessageReply.getOrgCode())){
            warnMessageReply.setReplyType(WarnMessageReplyType.MINE_REPLY.getValue());
            if (replyDtos.stream().anyMatch(e -> WarnMessageReplyType.MINE_REPLY.getValue() == e.getReplyType())){
                if (warnMessage != null) {
                    warnMessage.setReplyFlag(0);
                    warnMessageDao.update(warnMessage);
                }
                for (HistoryWarnMessage historyWarnMessage : historyWarnMessages) {
                    historyWarnMessage.setReplyFlag(0);
                    historyWarnMessageDao.update(historyWarnMessage);
                }
            }
        }else if ((warnMessageReply.getReplyType() == null || warnMessageReply.getReplyType() == 0) && !orgCode.equals(warnMessageReply.getOrgCode())) {
            warnMessageReply.setReplyType(WarnMessageReplyType.SUPERVISE_REPLY.getValue());
            if (warnMessage != null) {
                warnMessage.setReplyFlag(1);
                warnMessageDao.update(warnMessage);
            }
            for (HistoryWarnMessage historyWarnMessage : historyWarnMessages) {
                historyWarnMessage.setReplyFlag(1);
                historyWarnMessageDao.update(historyWarnMessage);
            }
        }

        return warnMessageReplyDao.insert(warnMessageReply) > 0;
    }

    /**
     * 报警对话查询
     * @param warnId
     * @return
     */
    @Override
    public List<WarnMessageReplyDto> getByWarnId(String warnId) {
        LambdaQueryWrapper<WarnMessageReply> eq = new LambdaQueryWrapper<WarnMessageReply>().eq(WarnMessageReply::getWarnId, warnId)
                .orderByAsc(WarnMessageReply::getCreateTime);
        List<WarnMessageReply> warnMessageReplies = warnMessageReplyDao.selectList(eq);
        List<WarnMessageReplyDto> warnMessageReplyDtos = mapperUtils.mapList(warnMessageReplies, WarnMessageReplyDto.class);
        for (WarnMessageReplyDto warnMessageReply : warnMessageReplyDtos) {
            List<File> files = fileDao.getByDataId(warnMessageReply.getId());
            String baseUrl = "/" + CpdsConstants.CPDS_SPVS_CONTEXT;
            if (minioConfig.isOpenFlag()) {
                baseUrl = minioConfig.getEndPoint() + "/" + minioConfig.getBucketName();
            } else if (obsProConfig.isSaveObs() && obsProConfig.isCloud()) {
                baseUrl = obsProConfig.getBosUrl();
            }
            // 转换文件url，生成可直接用于获取文件的文件url
            if (!files.isEmpty()){
                List<FileDto> fileDtos = mapperUtils.mapList(files, FileDto.class);
                for (FileDto fileDto : fileDtos) {
                    fileDto.setFileUrl(baseUrl + fileDto.getFileUrl());
                }
                warnMessageReply.setFileDtos(fileDtos);
            }
        }
        return warnMessageReplyDtos;
    }

    @Override
    public void oneClickSupervision(List<String> drainIds,Integer type) {
        if (Objects.isNull(drainIds) || drainIds.isEmpty()) {
            throw new IllegalArgumentException("台账ID列表不可为空");
        }
        List<WarnMessage> warnListByDrainIds = warnMessageDao.getWarnListByDrainIds(drainIds);
        if (Objects.nonNull(type)) {
            if (type == 0) {
                warnListByDrainIds = warnListByDrainIds.stream().filter(w -> Objects.isNull(w.getReplyFlag())).collect(Collectors.toList());
            } else {
                warnListByDrainIds = warnListByDrainIds.stream().filter(w -> Objects.nonNull(w.getReplyFlag())).collect(Collectors.toList());
            }
        }
        List<WarnMessageReply> warnMessageReplies = getWarnMessageReplies(warnListByDrainIds);
        if (!warnMessageReplies.isEmpty()) {
            warnMessageReplyDao.addAll(warnMessageReplies);
        }
    }

    @NotNull
    private List<WarnMessageReply> getWarnMessageReplies(List<WarnMessage> warnListByDrainIds) {
        List<WarnMessageReply> warnMessageReplies = new ArrayList<>();
        JykjCloudxUser user = SecurityUtils.getUser();
        warnListByDrainIds.forEach(warnMessage -> {
            WarnMessageReply warnMessageReply = new WarnMessageReply();
            warnMessageReply.setWarnId(warnMessage.getId());
            warnMessageReply.setReplyMessage("请尽快处理异常！！");
            warnMessageReply.setReplyOrgCode(user.getOrgCode());
            warnMessageReply.setReplyOrgName(user.getOrgName());
            warnMessageReply.setReplyUserId(user.getId());
            warnMessageReply.setReplyUserName(user.getRealname());
            warnMessageReply.setReplyType(1);
            warnMessageReply.setId(UUID.randomUUID().toString());
            warnMessageReply.setOrgCode(warnMessage.getOrgCode());
            warnMessageReply.setOrgName(warnMessage.getOrgName());
            warnMessageReply.setCreateTime(LocalDateTime.now().toDate());
            warnMessageReplies.add(warnMessageReply);
            warnMessage.setReplyFlag(1);
            warnMessageDao.update(warnMessage);
        });
        return warnMessageReplies;
    }
}
