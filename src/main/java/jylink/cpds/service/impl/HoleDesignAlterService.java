package jylink.cpds.service.impl;

import jylink.cpds.common.DozerUtils;
import jylink.cpds.dao.IHoleDesignAlterDao;
import jylink.cpds.domain.HoleDesignAlter;
import jylink.cpds.service.IHoleDesignAlterService;
import jylink.cpds.serviceModel.dto.HoleDesignAlterDto;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * 钻孔变更记录表服务实现类
 */
@Component
public class HoleDesignAlterService implements IHoleDesignAlterService {
    /**
     * dao服务
     */
    private final IHoleDesignAlterDao dao;

    /**
     * dozer帮助类
     */
    private final DozerUtils mapperUtils;

    @Autowired
    public HoleDesignAlterService(IHoleDesignAlterDao dao, DozerUtils mapperUtils) {
        this.dao = dao;
        this.mapperUtils = mapperUtils;
    }

    /**
     * 批量添加
     *
     * @param list 数据集合
     * @return 是否添加成功
     */
    @Override
    public boolean batchAdd(List<HoleDesignAlterDto> list) {
        List<HoleDesignAlter> models = mapperUtils.mapList(list, HoleDesignAlter.class);
        models.forEach(model -> {
            if (StringUtils.isEmpty(model.getId())) {
                model.setId(UUID.randomUUID().toString());
            }
            model.setCreateTime(LocalDateTime.now().toDate());
            model.setDelFlag(false);
        });

        return dao.batchAdd(models);
    }

    /**
     * 根据变更表Id删除数据
     *
     * @param orgCode       机构编码
     * @param tunnelAlterId 变更Id
     * @return 是否删除成功
     */
    @Override
    public boolean deleteByAlterId(String orgCode, String tunnelAlterId) {
        return dao.deleteByAlterId(orgCode, tunnelAlterId);
    }

    /**
     * 根据变更Id集合查询数据
     *
     * @param orgCode  机构编码
     * @param alterIds 变更Id集合
     * @return 查询结果
     */
    @Override
    public List<HoleDesignAlterDto> getByAlterIds(String orgCode, List<String> alterIds) {
        List<HoleDesignAlter> models = dao.getByAlterIds(alterIds);
        return mapperUtils.mapList(models, HoleDesignAlterDto.class);
    }
}
