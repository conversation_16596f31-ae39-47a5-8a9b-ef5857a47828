package jylink.cpds.service.impl;

import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.*;
import jylink.cpds.dao.IClassNoManagerDao;
import jylink.cpds.domain.ClassNoManager;
import jylink.cpds.serviceModel.*;
import jylink.cpds.serviceModel.dto.ClassNoManagerDto;
import jylink.cpds.service.IClassNoManagerService;

import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * 班次管理服务实现类
 */
@Component
public class ClassNoManagerService implements IClassNoManagerService {
    /**
     * dozer 帮助类对象
     */
    @Autowired
    private DozerUtils mapperList;

    /**
     * dao对象
     */
    @Autowired
    private IClassNoManagerDao dao;

    /**
     * Redis操作类
     */
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * dozer 实例对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * 根据机构编码查询数据
     *
     * @param orgCode 机构编码
     * @return dto模型集合
     */
    @Override
    public List<ClassNoManagerDto> getByOrgCode(String orgCode) {
        List<ClassNoManager> dataList;
        //尝试从Redis中获取缓存
        RedisUtils redis = new RedisUtils(redisTemplate);
        Tuple<Boolean, List<ClassNoManager>> tupleValue = redis.tryGetArray(CpdsConstants.REDIS_CLASS_MANAGER_KEY + orgCode, ClassNoManager.class);
        if (tupleValue.getFirstItem()) {
            dataList = tupleValue.getSecondItem();
        } else {
            dataList = dao.getByOrgCode(orgCode);
            redis.set(CpdsConstants.REDIS_CLASS_MANAGER_KEY + orgCode, dataList);
        }

        return mapperList.mapList(dataList, ClassNoManagerDto.class);
    }

    /**
     * 根据id回去数据
     *
     * @param orgCode 机构编码
     * @return 查询结果
     */
    @Override
    public ClassNoManagerDto getById(String orgCode, String id) {
        ClassNoManager classNoManager = dao.getById(id, orgCode);
        return mapper.map(classNoManager, ClassNoManagerDto.class);
    }

    /**
     * 删除数据
     *
     * @param orgCode 机构编码
     * @param id      数据id
     * @return dto模型集合
     */
    @Override
    public Boolean deleteById(String orgCode, String id) {
        if (dao.deleteById(orgCode, id)) {
            List<ClassNoManager> classNoManagerList = dao.getByOrgCode(orgCode);
            //存储到Redis中
            RedisUtils redis = new RedisUtils(redisTemplate);
            redis.set(CpdsConstants.REDIS_CLASS_MANAGER_KEY + orgCode, classNoManagerList);
            return true;
        }
        return false;
    }

    @Override
    public Boolean addClassNoMessage(ClassNoManagerDto classManagerMessage, String orgCode, String orgName) {
        //获取原有的数据
        List<ClassNoManager> classNoManagerList = dao.getByOrgCode(orgCode);

        ClassNoManager classNoManager = mapper.map(classManagerMessage, ClassNoManager.class);
        String id = UUID.randomUUID().toString();
        classNoManager.setId(id);
        classNoManager.setOrgCode(orgCode);
        classNoManager.setOrgName(orgName);
        classNoManager.setCreateTime(LocalDateTime.now().toDate());
        classNoManager.setDelFlag(false);
        classNoManagerList.add(classNoManager);

        //存储到Redis中
        RedisUtils redis = new RedisUtils(redisTemplate);
        redis.set(CpdsConstants.REDIS_CLASS_MANAGER_KEY + orgCode, classNoManagerList);
        return dao.addClassNoManager(classNoManager);
    }

    /**
     * 修改数据
     *
     * @param editClassNoManager 班次信息
     * @param orgCode            机构编码
     * @param orgName            机构名称
     * @return 修改是否成功
     */
    @Override
    public Boolean updateClassNoMessage(ClassNoManagerDto editClassNoManager, String orgCode, String orgName) {
        ClassNoManager classNoManager = mapper.map(editClassNoManager, ClassNoManager.class);
        classNoManager.setOrgCode(orgCode);
        classNoManager.setOrgName(orgName);
        if (dao.updateClassNoMessage(classNoManager)) {

            List<ClassNoManager> classNoManagerList = dao.getByOrgCode(orgCode);
            //存储到Redis中
            RedisUtils redis = new RedisUtils(redisTemplate);
            redis.set(CpdsConstants.REDIS_CLASS_MANAGER_KEY + orgCode, classNoManagerList);
            return true;
        }
        return false;
    }

    /**
     * 判断数据是否存在
     *
     * @param id      数据Id
     * @param orgCode 机构编码
     * @return 查询结果
     */
    @Override
    public boolean anyById(String id, String orgCode) {
        return dao.anyById(id, orgCode);
    }

    /**
     * 根据Key查询
     *
     * @param orgCode 机构编码
     * @param key     Key
     * @return 查询结果
     */
    @Override
    public ClassNoManagerDto getByKey(String orgCode, String key) {
        ClassNoManager model = dao.getClassName(orgCode, key);
        return mapper.map(model, ClassNoManagerDto.class);
    }
}
