package jylink.cpds.service.impl;

import com.ada.jykjcloudx.sdk.security.service.JykjCloudxUser;
import com.ada.jykjcloudx.sdk.security.util.SecurityUtils;
import com.github.dozermapper.core.Mapper;
import com.github.pagehelper.page.PageMethod;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.common.UserUtils;
import jylink.cpds.dao.ILogRecordDao;
import jylink.cpds.dao.ILogRecordDetailDao;
import jylink.cpds.domain.LogRecord;
import jylink.cpds.service.ILogRecordService;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.PagerParams;
import jylink.cpds.serviceModel.dto.LogRecordDto;
import jylink.cpds.serviceModel.params.LogRecordSearModel;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
public class LogRecordService implements ILogRecordService {

    /**
     * 日志信息 dao 服务
     */
    @Autowired
    private ILogRecordDao dao;
    /**
     * 日志信息 dao 服务
     */
    @Autowired
    private ILogRecordDetailDao logRecordDetailDao;

    /**
     * dozer帮助类
     */
    @Autowired
    private DozerUtils mapperUtils;

    /**
     * dozer转换类
     */
    @Autowired
    private Mapper mapper;
    /**
     * Redis操作类
     */
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 添加
     * @param logRecordDto  数据实体
     * @return  是否添加成功
     */
    @Override
    public LogRecord add(LogRecordDto logRecordDto) {
        try {
            LogRecord logRecord = mapper.map(logRecordDto, LogRecord.class);
            JykjCloudxUser user= SecurityUtils.getUser();
            logRecord.setOrgCode(UserUtils.getRedisOrgCode(redisTemplate));
            logRecord.setOrgName(user.getOrgName());
            logRecord.setOperationPersonId(user.getId());
            logRecord.setOperationPersonName(user.getRealname());
            LogRecord logRecordSearch=dao.getByParam(logRecord.getWorkCode(), logRecord.getSurveyWaterMileage(), logRecord.getHoleNo(), logRecord.getViceHoleNo(), logRecord.getOrgCode(), logRecord.getOperationType());
            if(logRecordSearch!=null){
                logRecord.setId(logRecordSearch.getId());
                logRecord.setCreateTime(LocalDateTime.now().toDate());
                Boolean uResult= dao.update(logRecord);
                logRecordDetailDao.delete(logRecordSearch.getId(),logRecord.getOrgCode());
                if(!uResult) {
                    return new LogRecord();
                }
                return logRecord;
            } else{
                String uuid = UUID.randomUUID().toString();
                logRecord.setId(uuid);
                logRecord.setCreateTime(LocalDateTime.now().toDate());
                Boolean aResult=dao.add(logRecord);
                if(!aResult) {
                    return new LogRecord();
                }
                return logRecord;
            }
        }catch (Exception e){
            e.getStackTrace();
        }
        return new LogRecord();
    }

    /**
     * 根绝机构编码分页查询
     * @param pagerParams  分页查询参数
     * @return
     */
    @Override
    public Pager<LogRecordDto> getByPager(PagerParams<LogRecordSearModel> pagerParams) {
        long count = dao.getCount(pagerParams.getObjParams());
        PageMethod.startPage(pagerParams.getCurrentPage(),pagerParams.getPageSize());
        List<LogRecord> logRecords = dao.getByPager(pagerParams.getObjParams());
        List<LogRecordDto> logRecordDtos = mapperUtils.mapList(logRecords, LogRecordDto.class);
        return new Pager<>(count,logRecordDtos);

    }

    /**
     * 根据参数查询数据信息
     * @param workCode  机构编码
     * @param surveyWaterMileage  探水里程
     * @param holeNo  孔号
     * @param viceHoleNo  副孔号
     * @param orgCode  机构编码
     * @param operationType  操作类型
     * @return
     */
    @Override
    public LogRecordDto getByParam(String workCode, Double surveyWaterMileage, String holeNo, String viceHoleNo, String orgCode, Integer operationType) {
        LogRecord logRecord = dao.getByParam(workCode, surveyWaterMileage, holeNo, viceHoleNo, orgCode, operationType);
        if (logRecord==null) {
            return new LogRecordDto();
        }
        return mapper.map(logRecord,LogRecordDto.class);
    }

    /**
     * 根据主键id查询数据是否存在
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  查询结果
     */
    @Override
    public boolean anyById(String id, String orgCode) {
        return dao.anyById(id, orgCode);
    }

    @Override
    public LogRecord addNoUser(LogRecordDto logRecordDto) {
        try {
            LogRecord logRecord = mapper.map(logRecordDto, LogRecord.class);
            LogRecord logRecordSearch=dao.getByParam(logRecord.getWorkCode(), logRecord.getSurveyWaterMileage(), logRecord.getHoleNo(), logRecord.getViceHoleNo(), logRecord.getOrgCode(), logRecord.getOperationType());
            if(logRecordSearch!=null){
                logRecord.setId(logRecordSearch.getId());
                logRecord.setCreateTime(LocalDateTime.now().toDate());
                Boolean uResult= dao.update(logRecord);
                logRecordDetailDao.delete(logRecordSearch.getId(),logRecord.getOrgCode());
                if(!uResult){
                    return new LogRecord();
                }
                return logRecord;
            } else{
                String uuid = UUID.randomUUID().toString();
                logRecord.setId(uuid);
                logRecord.setCreateTime(LocalDateTime.now().toDate());
                Boolean aResult=dao.add(logRecord);
                if(!aResult){
                    return new LogRecord();
                }
                return logRecord;
            }
        }catch (Exception e){
            e.getStackTrace();
        }
        return new LogRecord();
    }

}
