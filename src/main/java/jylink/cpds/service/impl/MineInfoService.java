package jylink.cpds.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ada.jykjcloudx.sdk.api.entity.OrgTree;
import com.ada.jykjcloudx.sdk.api.service.OrgLogicService;
import com.ada.jykjcloudx.sdk.api.service.OrgService;
import com.ada.jykjcloudx.sdk.core.constant.enums.Enumondition;
import com.ada.jykjcloudx.sdk.core.constant.enums.OrgTypeEnum;
import com.ada.jykjcloudx.sdk.core.util.R;
import com.ada.jykjcloudx.sdk.security.util.SecurityUtils;
import com.github.dozermapper.core.Mapper;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.common.UserUtils;
import jylink.cpds.config.MinioConfig;
import jylink.cpds.config.ObsProConfig;
import jylink.cpds.dao.*;
import jylink.cpds.domain.*;
import jylink.cpds.service.*;
import jylink.cpds.serviceModel.BasicOrgTree;
import jylink.cpds.serviceModel.ListItem;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.TreeNodeTypeEnum;
import jylink.cpds.serviceModel.analysis.AnalysisStatus;
import jylink.cpds.serviceModel.dto.*;
import jylink.cpds.serviceModel.enumeration.WarnMessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 煤矿基本信息服务
 */
@Slf4j
@Component
public class MineInfoService implements IMineInfoService {

    /**
     * 逻辑树
     */
    @Autowired
    private OrgLogicService orgLogicService;

    /**
     * 四邻关系
     */
    @Autowired
    private IFourNeighborRelationshipService fourNeighborRelationshipService;

    @Autowired
    private ObsProConfig obsProConfig;

    /**
     * 五证服务
     */
    @Autowired
    private IMineCertDao mineCertDao;

    /**
     * 煤层配置服务
     */
    @Autowired
    private ICoalLayerDao coalLayerDao;

    @Autowired
    private IMaterialCurrentService materialCurrentService;

    /**
     * 煤矿基本信息服务
     */
    @Autowired
    private IDcmMineInfoMjDao dcmMineInfoMjDao;

    /**
     * 基础服务
     */
    @Autowired
    private IBasicService basicService;

    /**
     * 文件服务
     */
    @Autowired
    private IFileService fileService;

    @Autowired
    private IDrainAccountDao accountDao;

    /**
     * Redis操作类
     */
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 识别 dao服务
     */
    @Autowired
    private IAnalysisWorkDao analysisWorkDao;

    /**
     * 机构服务
     */
    @Autowired
    private OrgService orgService;
    /**
     * dozer 帮助类对象
     */
    @Autowired
    private DozerUtils mapperUtils;

    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * dao对象
     */
    @Autowired
    private IMineInfoDao mineInfoDao;

    @Autowired
    private MinioConfig minioConfig;

    @Autowired
    private IWarnMessageDao warnMessageDao;

    /**
     * 所属煤层及煤层厚度再库里存的分割符号
     */
    private static final String SPLIT_STRING = "\\|\\_\\|";
    @Autowired
    private WorkFaceServiceImpl workFaceServiceImpl;
    @Autowired
    private TunnelDesignService tunnelDesignService;
    @Autowired
    private CheckPlanService checkPlanService;
    @Autowired
    private HoleDesignAlterService holeDesignAlterService;

    @Autowired
    private IEnforcementClassifyThreeService enforcementClassifyThreeService;

    /**
     * 添加煤矿基本信息
     *
     * @param instance MineInfoDto实体对象
     * @return 是否添加成功
     */
    @Override
    public boolean add(MineInfoDto instance) {
        MineInfo model = mapper.map(instance, MineInfo.class);
        model.setCreateTime(LocalDateTime.now().toDate());
        model.setDelFlag(false);
        return mineInfoDao.add(model);
    }


    private MineInfoDto createBy(String orgCode, MineInfo mineInfo) {
        MineInfoDto mineInfoDto = mapper.map(mineInfo, MineInfoDto.class);
        List<CoalLayer> coalLayers = coalLayerDao.getByOrgCode(orgCode);
        List<CoalSeamInfoDTo> list = new ArrayList<>();
        coalLayers.forEach(coalLayer -> {
            CoalSeamInfoDTo coalSeamInfo = new CoalSeamInfoDTo();
            coalSeamInfo.setCoalSeam(coalLayer.getCoalLayerName());
            coalSeamInfo.setCoalLayerKey(coalLayer.getCoalLayerKey());
            coalSeamInfo.setCoalSeamThickness(coalLayer.getCoalLayerThickness());
            list.add(coalSeamInfo);
        });
        List<FourNeighborRelationshipDto> dtos = fourNeighborRelationshipService.getByOrgCode(orgCode);
        mineInfoDto.setFourNeighborRelationshipDtos(dtos);
        mineInfoDto.setCoalSeamInfo(list);
        List<FileDto> fileDtoList = fileService.getByDataId(mineInfo.getId());
        mineInfoDto.setHydrogeological(fileDtoList.stream().filter(f -> f.getModuleName().equalsIgnoreCase(MineInfo.HYDROGEOLOGICAL)).collect(Collectors.toList()));
        mineInfoDto.setProductionGeological(fileDtoList.stream().filter(f -> f.getModuleName().equalsIgnoreCase(MineInfo.PRODUCTION_GEOLOGICAL)).collect(Collectors.toList()));
        mineInfoDto.setFourNeighborhood(fileDtoList.stream().filter(f -> f.getModuleName().equalsIgnoreCase(MineInfo.FOUR_NEIGHBORHOOD)).collect(Collectors.toList()));
        mineInfoDto.setTraffic(fileDtoList.stream().filter(f -> f.getModuleName().equalsIgnoreCase(MineInfo.TRAFFIC)).collect(Collectors.toList()));
        return mineInfoDto;
    }

    private List<EnforcementClassifyTwoDto> createMaterialInfoBy(String orgCode) {
        List<EnforcementClassifyTwoDto> enforcementClassifyThreeServiceList =
                enforcementClassifyThreeService.getList("A", orgCode);
        for (int i=0 ; i<enforcementClassifyThreeServiceList.size(); i++) {
            EnforcementClassifyTwoExpendDto expendDto = new EnforcementClassifyTwoExpendDto();
            BeanUtils.copyProperties(enforcementClassifyThreeServiceList.get(i), expendDto);

            for (EnforcementClassifyThreeDto enforcementClassifyThreeDto:
                    enforcementClassifyThreeServiceList.get(i).getThreeDtos()) {
                List<FileCycleDto> fileCycleDtos = materialCurrentService.getFile(orgCode,
                        enforcementClassifyThreeDto.getId(), null, 1, 100).getList();
                enforcementClassifyThreeDto.setFileCycleDtos(fileCycleDtos);
            }

            enforcementClassifyThreeServiceList.set(i, expendDto);
        }
        return enforcementClassifyThreeServiceList;
    }


    @Override
    public EnforcementClassifyTwoDto createSingleMaterialInfoBy(String dataId, String orgCode) {
        EnforcementClassifyTwoDto enforcementClassifyTwoDto =
                enforcementClassifyThreeService.getList("A", orgCode)
                        .stream().filter( t -> t.getId().equals(dataId)).findFirst().get();
            EnforcementClassifyTwoExpendDto expendDto = new EnforcementClassifyTwoExpendDto();
            BeanUtils.copyProperties(enforcementClassifyTwoDto, expendDto);

            for (EnforcementClassifyThreeDto enforcementClassifyThreeDto:
                    enforcementClassifyTwoDto.getThreeDtos()) {
                List<FileCycleDto> fileCycleDtos = materialCurrentService.getFile(orgCode,
                        enforcementClassifyThreeDto.getId(), null, 1, 100).getList();
                enforcementClassifyThreeDto.setFileCycleDtos(fileCycleDtos);
            }
        return enforcementClassifyTwoDto;
    }


    private List<WorkFaceExpendDto> createWorkFaceInfoBy(String orgCode) {
        long startTime = System.currentTimeMillis();
        List<WorkFaceDto> workFaces = workFaceServiceImpl.getByOrgCode(orgCode, null);
        List<WorkFaceExpendDto> workFacesExpend = new ArrayList<>();
        for (WorkFaceDto face : workFaces) {
            WorkFaceExpendDto workFaceExpendDto = new WorkFaceExpendDto();
            BeanUtils.copyProperties(face, workFaceExpendDto);
            workFacesExpend.add(workFaceExpendDto);
        }
        workFaces.sort(Comparator.comparing(WorkFaceDto::getWorkName));
        Map<String, TunnelDesignDto> workFaceToDesignMap = Maps.newHashMap();
        workFaces.forEach(workFace -> {
            workFaceToDesignMap.put(workFace.getId(), tunnelDesignService.getByWorkFaceId(workFace.getId(), orgCode));
        });
        log.info("time: {}", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        Map<String, List<CheckPlanDto>> designToCheckPlanMap = Maps.newHashMap();
        workFaceToDesignMap.values().forEach(design -> {
            if (design != null) {
                List<CheckPlanDto> byDesignId = checkPlanService.getByDesignId(orgCode, design.getId());
                designToCheckPlanMap.put(design.getId(), byDesignId);
            }
        });
        log.info("time: {}", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        Map<String, List<HoleDesignAlterDto>> stringListMap = holeDesignAlterService
                .getByAlterIds(orgCode, workFaceToDesignMap.values().stream()
                        .filter(Objects::nonNull)
                        .map(TunnelDesignDto::getAlterId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(HoleDesignAlterDto::getTunnelAlterId));
        ;
        Map<String, List<FileDto>> stringListMap1 = fileService.getByDataIds(workFaceToDesignMap.values()
                        .stream().filter(Objects::nonNull).map(TunnelDesignDto::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(FileDto::getDataId));
        log.info("time: {}", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        for (WorkFaceExpendDto d : workFacesExpend) {
            TunnelDesignDto tunnelDesignDto = workFaceToDesignMap.get(d.getId());
            if (Objects.nonNull(tunnelDesignDto)) {
                d.setMachineType(tunnelDesignDto.getMachineType());
                d.setPoleLength(tunnelDesignDto.getPoleLength());
                if (designToCheckPlanMap.containsKey(tunnelDesignDto.getId())) {
                    d.setCheckPlanCount(designToCheckPlanMap.get(tunnelDesignDto.getId()).size());
                }
                List<FileDto> fileDtos = stringListMap1.get(tunnelDesignDto.getId());
                if (fileDtos != null) {
                    d.setDesignDocuments(fileDtos.stream()
                            .filter(f -> TunnelDesign.DESIGN_DOCUMENT.equals(f.getModuleName()))
                            .collect(Collectors.toList()));
                } else {
                    d.setDesignDocuments(new ArrayList<>());
                }
                List<HoleDesignAlterDto> holeDesignAlters = stringListMap.get(tunnelDesignDto.getAlterId());
                d.setHoleDesigns(mapperUtils.mapList(holeDesignAlters, HoleDesignDto.class));
                if (tunnelDesignDto.getDetectionStatus()){
                    d.setTunnelingStatus("已掘完");
                } else {
                    if (!designToCheckPlanMap.get(tunnelDesignDto.getId()).isEmpty()) {
                        d.setTunnelingStatus("掘进中");
                    } else {
                        d.setTunnelingStatus("未开始");
                    }
                }
            } else {
                d.setTunnelingStatus("未开始");
            }
        }
        log.info("time: {}", System.currentTimeMillis() - startTime);
        startTime = System.currentTimeMillis();
        return workFacesExpend;
    }


    @Override
    public WorkFaceExpendDto createSingleWorkFaceInfoBy(String workFaceId, String orgCode) {
        WorkFaceDto workFace = workFaceServiceImpl.queryById(workFaceId, orgCode);
        WorkFaceExpendDto workFaceExpendDto = new WorkFaceExpendDto();
        BeanUtils.copyProperties(workFace, workFaceExpendDto);

        Map<String, TunnelDesignDto> workFaceToDesignMap = Maps.newHashMap();
        workFaceToDesignMap.put(workFace.getId(), tunnelDesignService.getByWorkFaceId(workFace.getId(), orgCode));

        Map<String, List<CheckPlanDto>> designToCheckPlanMap = Maps.newHashMap();
        workFaceToDesignMap.values().forEach(design -> {
            if (design != null) {
                List<CheckPlanDto> byDesignId = checkPlanService.getByDesignId(orgCode, design.getId());
                designToCheckPlanMap.put(design.getId(), byDesignId);
            }
        });
        Map<String, List<HoleDesignAlterDto>> stringListMap = holeDesignAlterService
                .getByAlterIds(orgCode, workFaceToDesignMap.values().stream()
                        .filter(Objects::nonNull)
                        .map(TunnelDesignDto::getAlterId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(HoleDesignAlterDto::getTunnelAlterId));
        ;
        Map<String, List<FileDto>> stringListMap1 = fileService.getByDataIds(workFaceToDesignMap.values()
                        .stream().filter(Objects::nonNull).map(TunnelDesignDto::getAlterId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(FileDto::getDataId));

            WorkFaceExpendDto d = workFaceExpendDto;
            TunnelDesignDto tunnelDesignDto = workFaceToDesignMap.get(d.getId());
            if (Objects.nonNull(tunnelDesignDto)) {
                d.setMachineType(tunnelDesignDto.getMachineType());
                d.setPoleLength(tunnelDesignDto.getPoleLength());
                if (designToCheckPlanMap.containsKey(tunnelDesignDto.getId())) {
                    d.setCheckPlanCount(designToCheckPlanMap.get(tunnelDesignDto.getId()).size());
                }
                List<FileDto> fileDtos = stringListMap1.get(tunnelDesignDto.getAlterId());
                if (fileDtos != null) {
                    d.setDesignDocuments(fileDtos.stream()
                            .filter(f -> TunnelDesign.DESIGN_DOCUMENT.equals(f.getModuleName()))
                            .collect(Collectors.toList()));
                } else {
                    d.setDesignDocuments(new ArrayList<>());
                }
                List<HoleDesignAlterDto> holeDesignAlters = stringListMap.get(tunnelDesignDto.getAlterId());
                d.setHoleDesigns(mapperUtils.mapList(holeDesignAlters, HoleDesignDto.class));
                if (tunnelDesignDto.getDetectionStatus()){
                    d.setTunnelingStatus("已掘完");
                } else {
                    if (!designToCheckPlanMap.get(tunnelDesignDto.getId()).isEmpty()) {
                        d.setTunnelingStatus("掘进中");
                    } else {
                        d.setTunnelingStatus("未开始");
                    }
                }
            } else {
                d.setTunnelingStatus("未开始");
            }
        return workFaceExpendDto;
    }


    /**
     * 根据机构编码获取数据
     *
     * @param orgCode 机构编码
     * @return MineInfoDto实体对象集合
     */
    @Override
    public MineInfoDto getByOrgCode(String orgCode) {
        MineInfo mineInfo = mineInfoDao.getByOrgCode(orgCode);
        if (Objects.isNull(mineInfo)) {
            return null;
        }
        MineInfoDto mineInfoDto = createBy(orgCode, mineInfo);
//        long start = System.currentTimeMillis();
//        mineInfoDto.setWorkFaceExpendDtos(createWorkFaceInfoBy(orgCode));
//        log.info("cost:{}", System.currentTimeMillis() - start);
//        start = System.currentTimeMillis();
//        mineInfoDto.setEnforcementClassifyTwoDtos(createMaterialInfoBy(orgCode));
//        log.info("cost:{}", System.currentTimeMillis() - start);
        return mineInfoDto;
    }

    @Override
    public Optional<List<MineInfoDto>> getByOrgNameKeyWord(String orgName, boolean isAccess) throws Exception {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(orgName), "机构名称关键字不允许为空!");

        List<BasicOrgTree> treeNode = basicService.getOrgTreeByOrgId(SecurityUtils.getUser().getOrgId());
        treeNode = treeNode.stream().filter(t -> t.getNodeType()
                .equals(TreeNodeTypeEnum.KUANG_NODE.getCode())).collect(Collectors.toList());
        List<MineInfo> mineInfos = treeNode.stream().map(node -> {
            MineInfo mineInfo = new MineInfo();
            mineInfo.setOrgCode(node.getOrgCode());
            mineInfo.setOrgName(node.getOrgName());
            return mineInfo;
        }).collect(Collectors.toList());


        List<MineInfo> temp = basicService.getMineInfos(mineInfos.stream()
                .map(MineInfo::getOrgCode).collect(Collectors.toList()));
        List<String> tempOrgCodes = temp.stream().map(MineInfo::getOrgCode).collect(Collectors.toList());
        List<WarnMessage> warnMessages = warnMessageDao.getByOrgCodesAndTypes(tempOrgCodes, Collections.singletonList(WarnMessageTypeEnum.NETWORK_DISCONNECT.getValue()));
        List<String> warnOrgCodes = warnMessages.stream().map(WarnMessage::getOrgCode).distinct().collect(Collectors.toList());

        if (isAccess) {
            mineInfos = temp;
        }

        mineInfos = mineInfos.stream().filter(mineInfo -> mineInfo.getOrgName().contains(orgName))
                .collect(Collectors.toList());

        if (mineInfos.isEmpty()) {
            return Optional.empty();
        }

        List<MineInfoDto> res = Collections.synchronizedList(new ArrayList<>());
        ExecutorService executorService = Executors.newFixedThreadPool(mineInfos.size());
        CountDownLatch countDownLatch = new CountDownLatch(mineInfos.size());
        for (MineInfo mineInfo : mineInfos) {
            executorService.submit(() -> {
                try {
                    res.add(createBy(mineInfo.getOrgCode(), mineInfo));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        executorService.shutdown();
        res.forEach(mineInfoDto -> {
            mineInfoDto.setIsAccess(tempOrgCodes.contains(mineInfoDto.getOrgCode()));
            mineInfoDto.setStatus(warnOrgCodes.contains(mineInfoDto.getOrgCode())?1:0);
        });
        res.sort(Comparator.comparing(MineInfoDto::getOrgName));
        return Optional.of(res);
    }

    /**
     * 根据数据Id获取数据
     *
     * @param id 数据Id
     * @return MineInfoDto实体对象
     */
    @Override
    public MineInfoDto getById(String id) {
        MineInfo model = mineInfoDao.getById(id);
        if (Objects.isNull(model)) {
            return null;
        }
        List<FileDto> fileDtoList = fileService.getByDataId(id);
        MineInfoDto mineInfoDto = mapper.map(model, MineInfoDto.class);
        List<CoalSeamInfoDTo> coalSeamInfo = getCoalSeamInfoList(model);
        mineInfoDto.setCoalSeamInfo(coalSeamInfo);
        mineInfoDto.setProductionGeological(fileDtoList.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(mineInfoDto.getId())).filter(f -> f.getModuleName().equalsIgnoreCase(MineInfo.PRODUCTION_GEOLOGICAL)).collect(Collectors.toList()));
        return mineInfoDto;
    }

    /**
     * 根据机构编码判断该机构下是否存在数据
     *
     * @param orgCode 机构编码
     * @return 编码对应的机构是否存在煤矿基本信息数据
     */
    @Override
    public boolean anyByOrgCode(String orgCode) {
        return mineInfoDao.anyByOrgCode(orgCode);
    }

    /**
     * 更新数据
     *
     * @param instance 实体对象
     * @return 是否更新成功
     */
    @Override
    public boolean update(MineInfoDto instance) {
        MineInfo model = mapper.map(instance, MineInfo.class);
        return mineInfoDao.update(model);
    }

    /**
     * 删除数据
     *
     * @param id 数据Id
     * @return 是否删除成功
     */
    @Override
    public boolean delete(String id) {
        return mineInfoDao.delete(id);
    }

    /**
     * 根据Id查询数据是否存在
     *
     * @param id      数据Id
     * @param orgCode 机构编码
     * @return 查询结果
     */
    @Override
    public boolean anyById(String id, String orgCode) {
        return mineInfoDao.anyById(id, orgCode);
    }

    /**
     * 获取煤矿基本信息
     *
     * @param orgId 机构id
     * @return 查询结果
     */
    @Override
    public List<MineInfoOrgDto> getByOrgCodes(String orgId) {
        R<List<OrgTree>> rsListOrgTree = orgService.getSimpleTree(orgId, OrgTypeEnum.GROUP, Enumondition.NO);
        if (rsListOrgTree.getCode() != 0) {
            return new ArrayList<>();
        }
        List<OrgTree> listOrgTree = rsListOrgTree.getData();
        List<ListItem> listItems = new ArrayList<>();
        getOrgCode(listOrgTree, listItems);
        List<MineInfoOrgDto> mineInfoOrgDtos = new ArrayList<>();
        List<String> orgCodes = listItems.stream().map(ListItem::getKey).collect(Collectors.toList());
        List<MineInfo> mineInfo = basicService.getMineInfos(orgCodes);
        //获取所有正在识别的识别任务
        List<AnalysisWork> analysisWorks = analysisWorkDao.getAnalysisByOrgCodes(orgCodes, AnalysisStatus.Analyzing.getValue());
        //获取所有状态不为0的识别任务中最大的创建时间和台账id
        List<AnalysisWork> list = analysisWorkDao.getByOrgCodes();
        List<String> ids = list.stream().map(AnalysisWork::getDrainId).collect(Collectors.toList());
        if (ids.isEmpty()) {
            listItems.forEach(listItem -> {
                MineInfoOrgDto mineInfoOrgDto = new MineInfoOrgDto();
                mineInfoOrgDto.setWork(false);
                mineInfoOrgDto.setMineInfoDto(new MineInfoDto());
                mineInfoOrgDto.setOrgCode(listItem.getKey());
                mineInfoOrgDto.setOrgName(listItem.getValue());
                mineInfoOrgDto.setNumber(0);
                mineInfo.stream().filter(m -> m.getOrgCode().equalsIgnoreCase(listItem.getKey())).findFirst().ifPresent(info -> {
                    MineInfoDto mineInfoDto = mapper.map(info, MineInfoDto.class);
                    mineInfoOrgDto.setMineInfoDto(mineInfoDto);
                });
                mineInfoOrgDtos.add(mineInfoOrgDto);
            });
            return mineInfoOrgDtos;
        }
        List<DrainAccount> drainAccounts = accountDao.getByIds(ids);
        listItems.forEach(listItem -> {
            MineInfoOrgDto mineInfoOrgDto = new MineInfoOrgDto();
            mineInfoOrgDto.setWork(false);
            mineInfoOrgDto.setMineInfoDto(new MineInfoDto());
            mineInfo.stream().filter(m -> m.getOrgCode().equalsIgnoreCase(listItem.getKey())).findFirst().ifPresent(info -> {
                MineInfoDto mineInfoDto = mapper.map(info, MineInfoDto.class);
                mineInfoOrgDto.setMineInfoDto(mineInfoDto);
            });
            List<AnalysisWork> analysisWorkList = analysisWorks.stream().filter(a -> a.getOrgCode().equalsIgnoreCase(listItem.getKey())).collect(Collectors.toList());
            mineInfoOrgDto.setOrgCode(listItem.getKey());
            mineInfoOrgDto.setOrgName(listItem.getValue());
            mineInfoOrgDto.setNumber(analysisWorkList.size());
            list.stream().filter(a -> a.getOrgCode().equalsIgnoreCase(listItem.getKey())).findFirst().ifPresent(analysisWork -> {
                mineInfoOrgDto.setStartTime(analysisWork.getCreateTime());
                drainAccounts.stream().filter(d -> d.getId().equalsIgnoreCase(analysisWork.getDrainId())).findFirst().ifPresent(drainAccount -> {
                    mineInfoOrgDto.setWorkCode(drainAccount.getWorkCode());
                    mineInfoOrgDto.setWorkName(drainAccount.getWorkName());
                    mineInfoOrgDto.setSurveyWaterMileage(drainAccount.getSurveyWaterMileage());
                });
            });
            if (analysisWorkList.size() > 0) {
                mineInfoOrgDto.setWork(true);
            }
            mineInfoOrgDtos.add(mineInfoOrgDto);
        });

        return mineInfoOrgDtos;
    }

    @Override
    public List<MineInfoDto> getInfoByCodes(List<String> orgCodes, String orgName) {
        if (orgCodes != null && orgCodes.size() > 0) {
            List<MineInfo> mineInfos = basicService.getMineInfos(orgCodes);
            if (mineInfos != null && mineInfos.size() > 0) {
                List<String> ids = mineInfos.stream().map(MineInfo::getId).collect(Collectors.toList());
                List<MineInfoDto> dtos = mapperUtils.mapList(mineInfos, MineInfoDto.class);
                List<CoalLayer> coalLayers = coalLayerDao.getByOrgCodes(orgCodes);
                List<FileDto> fileDtoList = fileService.getByDataIds(ids);
                dtos.forEach(mineInfoDto -> {
                    mineInfoDto.setHydrogeological(fileDtoList.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(mineInfoDto.getId())).filter(f -> f.getModuleName().equalsIgnoreCase(MineInfo.HYDROGEOLOGICAL)).collect(Collectors.toList()));
                    mineInfoDto.setTraffic(fileDtoList.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(mineInfoDto.getId())).filter(f -> f.getModuleName().equalsIgnoreCase(MineInfo.TRAFFIC)).collect(Collectors.toList()));
                    mineInfoDto.setFourNeighborhood(fileDtoList.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(mineInfoDto.getId())).filter(f -> f.getModuleName().equalsIgnoreCase(MineInfo.FOUR_NEIGHBORHOOD)).collect(Collectors.toList()));
                    mineInfoDto.setProductionGeological(fileDtoList.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(mineInfoDto.getId())).filter(f -> f.getModuleName().equalsIgnoreCase(MineInfo.PRODUCTION_GEOLOGICAL)).collect(Collectors.toList()));
                    List<CoalLayer> layers = coalLayers.stream().filter(coalLayer -> coalLayer.getOrgCode().equalsIgnoreCase(mineInfoDto.getOrgCode())).collect(Collectors.toList());
                    List<CoalSeamInfoDTo> list = new ArrayList<>();
                    layers.forEach(layer -> {
                        CoalSeamInfoDTo coalSeamInfo = new CoalSeamInfoDTo();
                        coalSeamInfo.setCoalLayerKey(layer.getCoalLayerKey());
                        coalSeamInfo.setCoalSeam(layer.getCoalLayerName());
                        coalSeamInfo.setCoalSeamThickness(layer.getCoalLayerThickness());
                        list.add(coalSeamInfo);
                    });
                    mineInfoDto.setProductionGeological(fileDtoList.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(mineInfoDto.getId())).filter(f -> f.getModuleName().equalsIgnoreCase(MineInfo.PRODUCTION_GEOLOGICAL)).collect(Collectors.toList()));
                    mineInfoDto.setCoalSeamInfo(list);
                });
                return dtos;
            }
        }
        return new ArrayList<>();
    }

    /**
     * 获取该机构下全部煤矿及基本信息数据
     *
     * @return 查询结果
     */
    @Override
    public Pager<OrgMessageDto> getAccessInfo(String orgLogicId, String orgName, int currentPage, int pageSize) {
        //  开始页
        int begin = currentPage <= 0 ? 0 : ((currentPage - 1) * pageSize);
        int end = currentPage <= 0 ? pageSize : (currentPage * pageSize);
        String baseUrl = obsProConfig.getBosUrl();
        if (minioConfig.isOpenFlag()) {
            baseUrl = minioConfig.getEndPoint() + "/" + minioConfig.getBucketName();
        }
        String treeType = UserUtils.getTreeType(redisTemplate);
        List<OrgTree> trees = orgLogicService.getTreeInner(treeType, orgLogicId, "1");
        if ( Objects.isNull(trees) || trees.isEmpty()) {
            return new Pager<>(0, new ArrayList<>());
        }
        if (!trees.isEmpty()) {
            List<String> allLogicOrgCodes = basicService.getAllLogicOrgCodes(orgLogicId);
            List<MineInfo> mineInfos = basicService.getMineInfos(allLogicOrgCodes);
            if (StringUtils.isNotBlank(orgName)) {
                mineInfos = mineInfos.stream().filter(m -> m.getOrgName().contains(orgName)).collect(Collectors.toList());
            }
            if (Objects.nonNull(mineInfos) && !mineInfos.isEmpty()) {
                if (mineInfos.size() < end) {
                    end = mineInfos.size();
                }
                List<String> orgCodes = mineInfos.stream().map(MineInfo::getOrgCode).collect(Collectors.toList());
                List<OrgMessageDto> orgMessageDtos = new ArrayList<>();
                List<OrgMessageDto> dtos = dcmMineInfoMjDao.getBaseInfo(orgCodes, orgName);
                List<MineCertPlus> mineCertPluses = mineCertDao.getByOrgCodes(orgCodes, orgName);
                for (int i = begin; i < end; i++) {
                    if (mineInfos.size() > i) {
                        MineInfo mineInfo = mineInfos.get(i);
                        OrgMessageDto orgMessageDto = new OrgMessageDto();
                        orgMessageDto.setOrgCode(mineInfo.getOrgCode());
                        orgMessageDto.setOrgName(mineInfo.getOrgName());
                        orgMessageDto.setMineInfoDto(mapper.map(mineInfo,MineInfoDto.class));
                        dtos.stream().filter(d -> d.getOrgCode().equalsIgnoreCase(mineInfo.getOrgCode())).findFirst().ifPresent(o -> {
                            orgMessageDto.setCityName(o.getCityName());
                            orgMessageDto.setCountryName(o.getCountryName());
                        });
                        //五证信息查询
                        List<MineCertPlus> mineCertDtos = mineCertPluses.stream().filter(m -> m.getOrgCode().equalsIgnoreCase(mineInfo.getOrgCode())).collect(Collectors.toList());
                        for (MineCertPlus mineCertPlus : mineCertDtos) {
                            if (obsProConfig.isSaveObs() && obsProConfig.isCloud() && mineCertPlus != null) {
                                mineCertPlus.setFileUrl(baseUrl + mineCertPlus.getFileUrl());
                            }
                        }
                        orgMessageDto.setMineCertDtos(mineCertDtos);
                        orgMessageDtos.add(orgMessageDto);
                    }
                }
                return new Pager<>(mineInfos.size(), orgMessageDtos);
            }
        }
        return new Pager<>(0, new ArrayList<>());
    }

    @Override
    public Pager<BasicOrgTree> getAllInfo(String code, String orgName, Integer type, int currentPage, int pageSize,String orgId) {
        int begin = currentPage <= 0 ? 0 : ((currentPage - 1) * pageSize);
        int end = currentPage <= 0 ? pageSize : (currentPage * pageSize);
        if (ObjectUtil.isNull(type)){
            type = 0;
        }
        // 当前节点下 所有节点
        if (StringUtils.isBlank(orgId)) {
            orgId = SecurityUtils.getUser().getOrgId();
        }
        List<BasicOrgTree> trees = basicService.getOrgTreeByOrgId( orgId);
        trees = trees.stream().filter(t -> t.getNodeType().equals(TreeNodeTypeEnum.KUANG_NODE.getCode())).collect(Collectors.toList());

        if (StringUtils.isNotEmpty(orgName)) {
            trees = trees.stream().filter(o -> o.getOrgName().contains(orgName)).collect(Collectors.toList());
        }
        List<BasicOrgTree> treeNode = basicService.getOrgTreeByOrgId( orgId);
        treeNode = treeNode.stream().filter(t -> t.getNodeType().equals(TreeNodeTypeEnum.KUANG_NODE.getCode())).collect(Collectors.toList());
        List<String> orgCodes = treeNode.stream().map(BasicOrgTree::getOrgCode).collect(Collectors.toList());
        if (orgCodes.isEmpty()) {
            orgCodes = new ArrayList<>();
            orgCodes.add("");
        }
        if (orgCodes.isEmpty()) {
            return new Pager<>(0, new ArrayList<>());
        }
        List<MineInfo> mineInfos = basicService.getMineInfos(orgCodes);
        List<String> accessOrgCodes = mineInfos.stream().map(MineInfo::getOrgCode).collect(Collectors.toList());
        List<WarnMessage> warnMessages = warnMessageDao.getByOrgCodesAndTypes(orgCodes, Arrays.asList(WarnMessageTypeEnum.NETWORK_DISCONNECT.getValue()));
        List<String> warnOrgCodes = warnMessages.stream().map(WarnMessage::getOrgCode).distinct().collect(Collectors.toList());
        if (type==0){
            // 已接入   工作面基本信息里有的
            trees = trees.stream().filter(t -> accessOrgCodes.contains(t.getOrgCode())).collect(Collectors.toList());
        } else if (type==1) {
            // 未接入
            trees = trees.stream().filter(t -> !accessOrgCodes.contains(t.getOrgCode())).collect(Collectors.toList());
            trees.forEach(tree -> {
                tree.setStatus(1);
            });
        } else if (type==2) {
            // 在线
            trees = trees.stream().filter(t -> accessOrgCodes.contains(t.getOrgCode()) && !warnOrgCodes.contains(t.getOrgCode())).collect(Collectors.toList());
            trees.forEach(tree -> {
                tree.setStatus(2);
            });
        } else if (type==3) {
            // 离线
            trees = trees.stream().filter(t -> accessOrgCodes.contains(t.getOrgCode()) && warnOrgCodes.contains(t.getOrgCode())).collect(Collectors.toList());
            trees.forEach(tree -> {
                tree.setStatus(3);
            });
        } else {
            trees.stream().filter(t -> !accessOrgCodes.contains(t.getOrgCode())).forEach(tree -> {
                tree.setStatus(1);
            });
        }
        if (trees.size() < end) {
            end = trees.size();
        }
        if (trees.size() < begin) {
            return new Pager<>(trees.size(), new ArrayList<>());
        }
        List<BasicOrgTree> basicOrgTrees = trees.subList(begin, end);
        return new Pager<>(trees.size(), basicOrgTrees);
    }

    @Override
    public OrgMessageDto getMineInfo(String orgCode) {
        List<String> orgCodes = new ArrayList<>();
        OrgMessageDto orgMessageDto = new OrgMessageDto();
        orgCodes.add(orgCode);
        List<OrgMessageDto> dtos = dcmMineInfoMjDao.getBaseInfo(orgCodes, null);
        if(ObjectUtil.isNull(dtos)||dtos.size()==0){
            return orgMessageDto;
        }
        orgMessageDto = dtos.get(0);
        //五证信息
        List<MineCertPlus> mineCertPluses = mineCertDao.getByOrgCodes(orgCodes, null);
        //煤矿详情
        List<MineInfoDto> mineInfoDtos = getInfoByCodes(orgCodes, null);
        if(ObjectUtil.isNotNull(mineInfoDtos)&&mineInfoDtos.size()>0){
            orgMessageDto.setMineInfoDto(mineInfoDtos.get(0));
        }
        orgMessageDto.setMineCertDtos(mineCertPluses);
        return orgMessageDto;
    }


    private OrgTree getOrgCode(List<OrgTree> orgTrees, List<ListItem> listItems) {
        OrgTree tree = new OrgTree();
        for (OrgTree orgTree : orgTrees) {
            if (orgTree.getChildren().size() > 0) {
                getOrgCode(orgTree.getChildren(), listItems);
            } else {
                ListItem listItem = new ListItem();
                listItem.setKey(orgTree.getCode());
                listItem.setValue(orgTree.getName());
                listItems.add(listItem);
            }
            mapper.map(orgTree, tree);
        }
        return tree;
    }

    private List<CoalSeamInfoDTo> getCoalSeamInfoList(MineInfo mineInfo) {
        List<CoalSeamInfoDTo> coalSeamInfo = new ArrayList<>();
        //  需要把所属煤层及煤层厚度  按标识符分割成list
        String coalSeam = mineInfo.getCoalSeam();
        String coalSeamThickness = mineInfo.getCoalSeamThickness();
        if (StringUtils.isNotBlank(coalSeam) && StringUtils.isNotBlank(coalSeamThickness)) {
            String[] coalSeamArr = coalSeam.split(SPLIT_STRING);
            String[] coalSeamThicknessArr = coalSeamThickness.split(SPLIT_STRING);
            for (int i = 0; i < coalSeamArr.length; i++) {
                CoalSeamInfoDTo coalSeamInfoDTo = new CoalSeamInfoDTo();
                coalSeamInfoDTo.setCoalSeam(coalSeamArr[i]);
                coalSeamInfoDTo.setCoalSeamThickness(Double.parseDouble(coalSeamThicknessArr[i]));
                coalSeamInfo.add(coalSeamInfoDTo);
            }
        }
        return coalSeamInfo;
    }
}
