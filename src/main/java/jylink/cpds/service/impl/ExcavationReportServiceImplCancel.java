//package jylink.cpds.service.impl;
//
//import jylink.cpds.domain.ExcavationReport;
//import jylink.cpds.service.IExcavationReportService;
//import jylink.cpds.serviceModel.JsonResponseGeneric;
//import jylink.cpds.serviceModel.Pager;
//import jylink.cpds.serviceModel.dto.ExcavationReportDto;
//import jylink.cpds.serviceModel.params.ApprovalReasonEditModel;
//import jylink.cpds.serviceModel.params.ApprovalWorkflowParamsModel;
//import org.springframework.stereotype.Service;
//
//import java.text.ParseException;
//import java.util.Date;
//import java.util.List;
//
//@Service("excavationReportServiceImplCancel")
//public class ExcavationReportServiceImplCancel implements IExcavationReportService {
//    @Override
//    public boolean add(ExcavationReport instance, List<List<ApprovalWorkflowParamsModel>> flows) {
//        return false;
//    }
//
//    @Override
//    public List<ExcavationReport> getByTunnelId(String tunnelId, String orgCode) {
//        return null;
//    }
//
//    @Override
//    public List<ExcavationReport> getByTunnelIdAndIsCorrect(String tunnelId, String orgCode, int isCorrect) {
//        return null;
//    }
//
//    @Override
//    public ExcavationReportDto getById(String id, String orgCode) {
//        return null;
//    }
//
//    @Override
//    public Pager<ExcavationReportDto> getData(String tunnelId, String orgCode, String startDate, String endDate, int currentPage, int pageSize) {
//        return null;
//    }
//
//    @Override
//    public boolean delete(String id, String orgCode) {
//        return false;
//    }
//
//    @Override
//    public boolean update(ExcavationReport excavationReport, List<List<ApprovalWorkflowParamsModel>> flows) {
//        return false;
//    }
//
//    @Override
//    public List<ExcavationReport> getByClassAndTunnelIdAndWorkDate(String tunnelId, String orgCode, String workClassNumKey, Date workDate, String isCorrect) throws ParseException {
//        return null;
//    }
//
//    @Override
//    public List<ExcavationReport> getByTunnelIdAndWorkDate(String tunnelId, String orgCode, Date workDate, String isCorrect) throws ParseException {
//        return null;
//    }
//
//    @Override
//    public boolean anyById(String id, String orgCode) {
//        return false;
//    }
//
//    @Override
//    public JsonResponseGeneric approval(ApprovalReasonEditModel approvalReasonEditModel, ExcavationReport excavationReport) {
//        return null;
//    }
//
//    @Override
//    public ExcavationReportDto getPosition(String tunnelId, String orgCode) {
//        return null;
//    }
//
//    @Override
//    public boolean updateStatus(String id, String orgCode, Integer status) {
//        return false;
//    }
//
//    @Override
//    public boolean refuse(ApprovalReasonEditModel model) {
//        return false;
//    }
//
//    @Override
//    public boolean reVoke(ApprovalReasonEditModel model) {
//        return false;
//    }
//
//    @Override
//    public boolean approving(String id, String orgCode) {
//        return false;
//    }
//}
