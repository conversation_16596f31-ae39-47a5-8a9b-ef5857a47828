package jylink.cpds.service.impl;

import com.ada.jykjcloudx.sdk.api.entity.SysOrgStructure;
import com.ada.jykjcloudx.sdk.api.service.OrgService;
import com.ada.jykjcloudx.sdk.core.util.R;
import com.ada.jykjcloudx.sdk.security.util.SecurityUtils;
import com.alibaba.fastjson.JSONArray;
import com.github.dozermapper.core.Mapper;
import com.github.pagehelper.page.PageMethod;
import jylink.cpds.common.AESUtils;
import jylink.cpds.common.CpdsConstants;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.dao.IDrawingExchangesDao;
import jylink.cpds.domain.DrawingExchanges;
import jylink.cpds.service.IBasicService;
import jylink.cpds.service.IDrawingExchangesConfigService;
import jylink.cpds.service.IDrawingExchangesService;
import jylink.cpds.serviceModel.*;
import jylink.cpds.serviceModel.dto.DrawingExchangesDto;
import jylink.cpds.serviceModel.dto.OrgFileExchangeInfoDto;
import jylink.cpds.serviceModel.dto.TimeDto;
import jylink.cpds.serviceModel.params.DrawingExchangesQueryModel;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 图纸交换记录主表(DrawingExchanges)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-31 17:21:44
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class DrawingExchangesServiceImpl implements IDrawingExchangesService {

    @Autowired
    private OrgService orgService;

    /**
     * 图纸交换配置
     */
    @Autowired
    private IDrawingExchangesConfigService drawingExchangesConfigService;

    /**
     * 基础服务
     */
    @Autowired
    private IBasicService basicService;

    @Autowired
    private IDrawingExchangesDao drawingExchangesDao;

    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * dozer 帮助类
     */
    @Autowired
    private DozerUtils mapperUtils;

    /**
     * 通过ID查询单条数据
     *
     * @param id      主键
     * @return 实例对象
     */
    @Override
    public DrawingExchangesDto queryById(String id) {
        DrawingExchanges drawingExchanges = drawingExchangesDao.queryById(id);
        if (drawingExchanges == null) {
            return null;
        }
        return mapper.map(drawingExchanges, DrawingExchangesDto.class);
    }

    /**
     * 通过ID确认数据是否存在
     *
     * @param id 主键
     * @return boolean
     */
    @Override
    public boolean anyById(String id) {
        return queryById(id) != null;
    }

    /**
     * 数据查询
     *
     * @return
     */
    @Override
    public Pager<OrgFileExchangeInfoDto> getByPager(PagerParams<DrawingExchangesQueryModel> queryModel, String orgId) {
        if (StringUtils.isNotBlank(queryModel.getObjParams().getOrgCode())) {
            R<SysOrgStructure> structureR = orgService.getByCode(queryModel.getObjParams().getOrgCode());
            if (Objects.isNull(structureR) || structureR.getCode() != 0 || Objects.isNull(structureR.getData())) {
                return new Pager<>(0,new ArrayList<>());
            }
            orgId = structureR.getData().getId();
        }
        List<BasicOrgTree> orgTrees = basicService.getOrgTreeByOrgId(orgId);
        orgTrees = orgTrees.stream().filter(t -> t.getNodeType().equalsIgnoreCase(TreeNodeTypeEnum.KUANG_NODE.getCode())).collect(Collectors.toList());
        List<String> orgCodes = orgTrees.stream().map(BasicOrgTree::getOrgCode).collect(Collectors.toList());
        if (orgCodes.isEmpty()) {
            return new Pager<>(0,new ArrayList<>());
        }
        List<OrgFileExchangeInfoDto> fileExchangeInfo = drawingExchangesDao.getFileExchangeInfo(orgCodes);
        List<OrgFileExchangeInfoDto> exchangeInfoDtos = fileExchangeInfo.stream().filter(f -> StringUtils.isNotBlank(f.getDiyColumn())).collect(Collectors.toList());
        for (OrgFileExchangeInfoDto exchangeInfoDto : exchangeInfoDtos) {
            String decrypt = AESUtils.decode(CpdsConstants.ORG_CODE_KEY, exchangeInfoDto.getDiyColumn());
            if (StringUtils.isNotBlank(decrypt)) {
                List<String> strings = JSONArray.parseArray(decrypt, String.class);
                if (Objects.nonNull(strings)) {
                    if (strings.contains(SecurityUtils.getUser().getOrgCode())) {
                        fileExchangeInfo.remove(exchangeInfoDto);
                    }
                }
            }
        }
        List<String> collect = fileExchangeInfo.stream().map(OrgFileExchangeInfoDto::getOrgCode).distinct().collect(Collectors.toList());
        if (collect.isEmpty()) {
            List<OrgFileExchangeInfoDto> orgFileExchangeInfoDtos = mapperUtils.mapList(orgTrees, OrgFileExchangeInfoDto.class);
            fileExchangeInfo = orgFileExchangeInfoDtos.stream().skip((long) (queryModel.getCurrentPage() -1) * queryModel.getPageSize()).limit(queryModel.getPageSize()).collect(Collectors.toList());
            return new Pager<>(orgTrees.size(),fileExchangeInfo);
        }
        TimeDto time = drawingExchangesConfigService.getTime(SecurityUtils.getUser().getOrgCode(),0);
        TimeDto laseTime = null;
        boolean needCheck = false;
        boolean needLast = false;
        Date date = LocalDateTime.now().toDate();
        SimpleDateFormat format = new SimpleDateFormat(CpdsConstants.DATE_FORMAT);
        String nowDay = format.format(date);
        List<String> sendFileOrg = new ArrayList<>();
        List<String> lastSendFileOrg = new ArrayList<>();
        if (Objects.nonNull(time) && StringUtils.isNotBlank(time.getStartDate()) && StringUtils.isNotBlank(time.getEndDate())) {
            needCheck = true;
            sendFileOrg = drawingExchangesDao.getSendFileOrg(collect, time.getStartDate(), time.getEndDate());
            if (nowDay.compareTo(time.getStartDate()) >= 0 && nowDay.compareTo(time.getEndDate()) <= 0) {
                laseTime = drawingExchangesConfigService.getTime(SecurityUtils.getUser().getOrgCode(),1);
                if (Objects.nonNull(laseTime) && StringUtils.isNotBlank(laseTime.getStartDate()) && StringUtils.isNotBlank(laseTime.getEndDate())) {
                    needLast = true;
                    lastSendFileOrg = drawingExchangesDao.getSendFileOrg(collect, laseTime.getStartDate(), laseTime.getEndDate());
                }
            }
        }
        List<OrgFileExchangeInfoDto> receiveNumByOrgCode = new ArrayList<>();
        if (needCheck) {
            receiveNumByOrgCode = drawingExchangesDao.getReceiveNumByOrgCode(collect, time.getStartDate(), time.getEndDate());
        }
        for (OrgFileExchangeInfoDto fileExchangeInfoDto : fileExchangeInfo) {
            if (Objects.nonNull(fileExchangeInfoDto.getSendTime())) {
                String sendDate = format.format(fileExchangeInfoDto.getSendTime());
                fileExchangeInfoDto.setStatus(0);
                if (needCheck) {
                    //这月传了，无需考虑上月，均不飘红，这月没传，查是否需要判断上月穿没穿，上月传了不飘红，没传，飘红
                    if (sendFileOrg.stream().noneMatch(s ->s.equalsIgnoreCase(fileExchangeInfoDto.getOrgCode()))) {
                        fileExchangeInfoDto.setStatus(1);
                        if (needLast && lastSendFileOrg.stream().anyMatch(s ->s.equalsIgnoreCase(fileExchangeInfoDto.getOrgCode()))) {
                            fileExchangeInfoDto.setStatus(0);
                        }
                    }
                }
            }
            if (needCheck) {
                receiveNumByOrgCode.stream().filter(r -> r.getOrgCode().equalsIgnoreCase(fileExchangeInfoDto.getOrgCode())).findFirst().ifPresent(dto -> {
                    fileExchangeInfoDto.setReceiveOrgNum(dto.getReceiveOrgNum());
                });
            }
        }
        for (BasicOrgTree basicOrgTree : orgTrees) {
            if (fileExchangeInfo.stream().noneMatch(f -> f.getOrgCode().equalsIgnoreCase(basicOrgTree.getOrgCode()))) {
                OrgFileExchangeInfoDto orgFileExchangeInfoDto = new OrgFileExchangeInfoDto();
                orgFileExchangeInfoDto.setOrgCode(basicOrgTree.getOrgCode());
                orgFileExchangeInfoDto.setOrgName(basicOrgTree.getOrgName());
                orgFileExchangeInfoDto.setAccessFlag(false);
                fileExchangeInfo.add(orgFileExchangeInfoDto);
            }
        }
        fileExchangeInfo = fileExchangeInfo.stream().skip((long) (queryModel.getCurrentPage() -1) * queryModel.getPageSize()).limit(queryModel.getPageSize()).collect(Collectors.toList());
        Pager<OrgFileExchangeInfoDto> pager = new Pager<>(orgTrees.size(), fileExchangeInfo);
        return pager;
    }

    /**
     * 获取图纸交换发送记录
     *
     * @param orgCode     机构编码
     * @param mineOrgCode 相邻煤矿编码
     * @param sendTime    发送时间
     * @param status      审批状态
     * @param currentPage 当前页
     * @param pageSize    页面大小
     * @return 查询结果
     */
    @Override
    public Pager<DrawingExchangesDto> getSendInfo(String orgCode, String mineOrgCode, String sendTime, Integer status, int currentPage, int pageSize) {
        long count = drawingExchangesDao.getSendCount(orgCode, mineOrgCode, sendTime, status);
        PageMethod.startPage(currentPage, pageSize);
        List<DrawingExchanges> sendInfo = drawingExchangesDao.getSendInfo(orgCode, mineOrgCode, sendTime, status);
        List<DrawingExchangesDto> DrawingExchangesDtos = mapperUtils.mapList(sendInfo, DrawingExchangesDto.class);
        DrawingExchangesDtos.forEach(DrawingExchangesDto -> {
            if (DrawingExchangesDto.getStatus().equals(WorkFlowStatus.Approved)) {
                DrawingExchangesDto.setApprovalStatus("已发送");
            } else {
                DrawingExchangesDto.setApprovalStatus(DrawingExchangesDto.getStatus().getInterpretation());
            }
        });
        return new Pager<>(count, DrawingExchangesDtos);
    }

    @Override
    public Pager<DrawingExchangesDto> getReceiveInfo(String orgCode, String mineOrgCode, String receiveTime, Integer status, int currentPage, int pageSize) {
        long count = drawingExchangesDao.getReceiveCount(orgCode, mineOrgCode, receiveTime, status);
        PageMethod.startPage(currentPage, pageSize);
        List<DrawingExchanges> sendInfo = drawingExchangesDao.getReceiveInfo(orgCode, mineOrgCode, receiveTime, status);
        List<DrawingExchangesDto> DrawingExchangesDtos = mapperUtils.mapList(sendInfo, DrawingExchangesDto.class);
        DrawingExchangesDtos.forEach(DrawingExchangesDto -> {
            if (DrawingExchangesDto.getStatus().equals(WorkFlowStatus.Approved)) {
                DrawingExchangesDto.setApprovalStatus("已接收");
            } else {
                DrawingExchangesDto.setApprovalStatus(DrawingExchangesDto.getStatus().getInterpretation());
            }
        });
        return new Pager<>(count, DrawingExchangesDtos);
    }

}
