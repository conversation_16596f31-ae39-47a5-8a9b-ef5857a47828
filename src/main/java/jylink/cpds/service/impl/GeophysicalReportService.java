package jylink.cpds.service.impl;

import com.github.dozermapper.core.Mapper;
import com.github.pagehelper.page.PageMethod;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.dao.IGeophysicalReportDao;
import jylink.cpds.domain.GeophysicalReport;
import jylink.cpds.service.IBasicService;
import jylink.cpds.service.IFileService;
import jylink.cpds.service.IGeophysicalReportService;
import jylink.cpds.serviceModel.FileBaseEnum;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.dto.FileDto;
import jylink.cpds.serviceModel.dto.GeophysicalReportDto;
import jylink.cpds.serviceModel.dto.OrgGeophysicalDto;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class GeophysicalReportService implements IGeophysicalReportService {

    /**
     * 基本服务
     */
    @Autowired
    private IBasicService basicService;

    /**
     * 物探报告dao服务
     */
    @Autowired
    private IGeophysicalReportDao dao;

    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * dozer帮助类
     */
    @Autowired
    private DozerUtils mapperUtils;

    /**
     * 文件服务
     */
    @Autowired
    private IFileService fileService;


    /**
     * 根据主键id查询数据
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  查询结果
     */
    @Override
    public GeophysicalReportDto getById(String id, String orgCode) {
        GeophysicalReport geophysicalReport = dao.getById(id);
        return mapper.map(geophysicalReport,GeophysicalReportDto.class);
    }

    /**
     * 分页查询
     * @param orgName  机构名称
     * @param endDate  结束时间
     * @param result  物探结果
     * @param currentPage  当前页
     * @param pageSize  页面大小
     * @return  查询结果
     */
    @Override
    public Pager<GeophysicalReportDto> getByOrgCode(String orgName, String startDate, String endDate, String result,Integer method,Integer type, int currentPage, int pageSize,
                                                    String mineCityZoneCode,String  mineCountyIdCode,String mineProvzoneCode , String orgCode , String workFaceId) {
        List<String> orgCodes = basicService.getAllAccessOrgCodes();
        long count = dao.getCount(orgCodes,type,orgName, startDate,endDate, result,method, mineCityZoneCode, mineCountyIdCode,mineProvzoneCode,orgCode,workFaceId);
        PageMethod.startPage(currentPage,pageSize);
        List<GeophysicalReport> geophysicalReports = dao.getByOrgCode(orgCodes,type,orgName,startDate,endDate,result,method, mineCityZoneCode, mineCountyIdCode,mineProvzoneCode,orgCode,workFaceId);
        List<GeophysicalReportDto> geophysicalReportDtos = mapperUtils.mapList(geophysicalReports, GeophysicalReportDto.class);
        List<String> collect = geophysicalReportDtos.stream().map(GeophysicalReportDto::getId).collect(Collectors.toList());
        if (collect.isEmpty()) {
            return new Pager<>(count,new ArrayList<>());
        }
        List<FileDto> fileDtos = fileService.getByDataIds(collect);
        geophysicalReportDtos.forEach(geophysicalReportDto -> {
            geophysicalReportDto.setFileDtos(fileDtos.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(geophysicalReportDto.getId()) && fileDto.getModuleName().equalsIgnoreCase(FileBaseEnum.REPORT.getValue())).collect(Collectors.toList()));
            geophysicalReportDto.setGeophysicalPicture(fileDtos.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(geophysicalReportDto.getId()) && fileDto.getModuleName().equalsIgnoreCase(FileBaseEnum.GEOPHYSICAL_PICTURE.getValue())).collect(Collectors.toList()));
            geophysicalReportDto.setGeophysicalDesign(fileDtos.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(geophysicalReportDto.getId()) && fileDto.getModuleName().equalsIgnoreCase(FileBaseEnum.GEOPHYSICAL_DESIGN.getValue())).collect(Collectors.toList()));
            geophysicalReportDto.setMethodChinese(geophysicalReportDto.getMethod().getInterpretation());
            geophysicalReportDto.setGeophysicalPurposeChinese(geophysicalReportDto.getGeophysicalPurpose() != null ? geophysicalReportDto.getGeophysicalPurpose().getInterpretation():null);
        });
        return new Pager<>(count,geophysicalReportDtos);
    }

    @Override
    public OrgGeophysicalDto getAllGeophysicalCount(String orgName, String startDate, String endDate, String result, Integer method, Integer type , String orgCode , String workFaceId) {
        List<String> orgCodes = basicService.getAllAccessOrgCodes();
        if (orgCodes.isEmpty()) {
            return null;
        }
        return dao.getAllGeophysicalCount(orgCodes,orgName,type,startDate,endDate,result,method , orgCode , workFaceId);
    }

    /**
     *
     * @param orgCode  机构编码
     * @param workName  设计id
     * @param result  物探结果
     * @param currentPage  当前页
     * @param pageSize  页面大小
     * @return
     */
    @Override
    public Pager<GeophysicalReportDto> getOrgCode(String orgCode, String workName, String result, int currentPage, int pageSize) {
        long count = dao.getNum(orgCode, workName, result);
        PageMethod.startPage(currentPage,pageSize);
        List<GeophysicalReport> geophysicalReports = dao.getOrgCode(orgCode, workName, result);
        List<GeophysicalReportDto> geophysicalReportDtos = mapperUtils.mapList(geophysicalReports, GeophysicalReportDto.class);
        List<String> collect = geophysicalReportDtos.stream().map(GeophysicalReportDto::getId).collect(Collectors.toList());
        if (collect==null||collect.isEmpty()) {
            return new Pager<>(count,new ArrayList<>());
        }
        List<FileDto> fileDtos = fileService.getByDataIds(collect);
        geophysicalReportDtos.forEach(dto -> {
            dto.setFileDtos(fileDtos.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(dto.getId()) && fileDto.getModuleName().equalsIgnoreCase(FileBaseEnum.REPORT.getValue())).collect(Collectors.toList()));
            dto.setGeophysicalPicture(fileDtos.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(dto.getId()) && fileDto.getModuleName().equalsIgnoreCase(FileBaseEnum.GEOPHYSICAL_PICTURE.getValue())).collect(Collectors.toList()));
            dto.setGeophysicalDesign(fileDtos.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(dto.getId()) && fileDto.getModuleName().equalsIgnoreCase(FileBaseEnum.GEOPHYSICAL_DESIGN.getValue())).collect(Collectors.toList()));
            dto.setMethodChinese(dto.getMethod().getInterpretation());
            dto.setGeophysicalPurposeChinese(dto.getGeophysicalPurpose() != null ? dto.getGeophysicalPurpose().getInterpretation():null);
        });
        return new Pager<>(count,geophysicalReportDtos);
    }

    /**
     * 添加
     * @param instance  数据实体
     * @return
     */
    @Override
    public boolean add(GeophysicalReportDto instance) {
        GeophysicalReport geophysicalReport = mapper.map(instance, GeophysicalReport.class);
        geophysicalReport.setCreateTime(LocalDateTime.now().toDate());
        return dao.add(geophysicalReport);
    }

    /**
     * 更新
     * @param instance  数据实体
     * @return
     */
    @Override
    public boolean update(GeophysicalReportDto instance) {
        GeophysicalReport geophysicalReport = mapper.map(instance, GeophysicalReport.class);
        return dao.update(geophysicalReport);
    }

    /**
     * 根据主键id查询数据是否存在
     * @param id  主键id
     * @param orgCode  机构编码
     * @return
     */
    @Override
    public boolean anyById(String id, String orgCode) {
        return dao.anyById(id, orgCode);
    }

    /**
     * 根据主键id删除
     * @param id 主键id
     * @return  是否删除成功
     */
    @Override
    public boolean delete(String id,String orgCode) {
        return dao.delete(id,orgCode);
    }

    @Override
    public List<GeophysicalReportDto> getByTunnelId(String tunnelId, String orgCode) {
        List<GeophysicalReport> geophysicalReports = dao.getByTunnelId(tunnelId, orgCode);
        List<String> ids = geophysicalReports.stream().map(GeophysicalReport::getId).collect(Collectors.toList());
        List<GeophysicalReportDto> geophysicalReportDtos = mapperUtils.mapList(geophysicalReports, GeophysicalReportDto.class);
        if (ids!=null && !ids.isEmpty()) {
            List<FileDto> fileDtos = fileService.getByDataIds(ids);
            geophysicalReportDtos.forEach(geophysicalReportDto -> {
                geophysicalReportDto.setFileDtos(fileDtos.stream().filter(fileDto -> fileDto.getDataId().equalsIgnoreCase(geophysicalReportDto.getId()) && fileDto.getModuleName().equalsIgnoreCase(GeophysicalReport.REPORT)).collect(Collectors.toList()));
                geophysicalReportDto.setMethodChinese(geophysicalReportDto.getMethod().getInterpretation());
            });
        }
        return geophysicalReportDtos;
    }

    /**
     * 根据机构名称和近几月查询物探管理数据
     * @param orgName  机构名称
     * @param month  近几月
     * @param currentPage  当前页
     * @param pageSize  页面大小
     * @return
     */
    @Override
    public Pager<OrgGeophysicalDto> getGeophysical(String orgName, Integer month, int currentPage, int pageSize) {
        List<String> orgCodes = basicService.getAllAccessOrgCodes();
        if (orgCodes != null && !orgCodes.isEmpty()) {
            long count = dao.getGeophysicalCount(orgCodes, orgName, month);
            PageMethod.startPage(currentPage,pageSize);
            List<OrgGeophysicalDto> orgGeophysicalDtos = dao.getGeophysical(orgCodes, orgName, month);
            OrgGeophysicalDto dto = dao.getAllCount(orgCodes, orgName, month);
            if (Objects.nonNull(dto)) {
                dto.setOrgGeophysicalDtoList(orgGeophysicalDtos);
                return new Pager<>(count, Arrays.asList(dto));
            }
        }
        return new Pager<>(0,new ArrayList<>());
    }

    @Override
    public OrgGeophysicalDto getGeophysicalCount(String orgName, Integer month) {
        List<String> orgCodes = basicService.getAllAccessOrgCodes();
        if (orgCodes != null && !orgCodes.isEmpty()) {
            OrgGeophysicalDto dto = dao.getAllCount(orgCodes, orgName, month);
            return dto;
        }
        return new OrgGeophysicalDto();
    }
}
