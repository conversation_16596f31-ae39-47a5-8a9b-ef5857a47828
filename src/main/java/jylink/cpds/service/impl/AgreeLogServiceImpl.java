package jylink.cpds.service.impl;

import com.ada.jykjcloudx.sdk.security.util.SecurityUtils;
import jylink.cpds.domain.AgreeLog;
import jylink.cpds.dao.IAgreeLogDao;
import jylink.cpds.service.IAgreeLogService;
import org.joda.time.LocalDateTime;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

/**
 * 技术交底书同意记录(AgreeLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2020-03-13 16:27:58
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AgreeLogServiceImpl implements IAgreeLogService {
    @Autowired
    private IAgreeLogDao agreeLogDao;

    /**
     * 通过ID查询单条数据
     *
     * @param drainId 主键
     * @return 实例对象
     */
    @Override
    public AgreeLog queryByDrainId(String drainId) {
        return this.agreeLogDao.queryByDrainId(drainId);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    @Override
    public List<AgreeLog> queryAllByLimit(int offset, int limit) {
        return this.agreeLogDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     *
     * @param agreeLog 实例对象
     * @return 实例对象
     */
    @Override
    public boolean insert(AgreeLog agreeLog) {
        agreeLog.setId(UUID.randomUUID().toString());
        agreeLog.setOrgCode(SecurityUtils.getUser().getOrgCode());
        agreeLog.setOrgName(SecurityUtils.getUser().getOrgName());
        agreeLog.setCreateTime(LocalDateTime.now().toDate());
        agreeLog.setDrainId(agreeLog.getDrainId());
        agreeLog.setUserId(SecurityUtils.getUser().getId());
        agreeLog.setUserName(SecurityUtils.getUser().getRealname());
        agreeLog.setAggreeTime(LocalDateTime.now().toDate());
        if (this.agreeLogDao.insert(agreeLog) == 1) {
            return true;
        }
        return false;
    }

    /**
     * 修改数据
     *
     * @param agreeLog 实例对象
     * @return 实例对象
     */
    @Override
    public AgreeLog update(AgreeLog agreeLog) {
        this.agreeLogDao.update(agreeLog);
        return null;
    }

    /**
     * 通过主键删除数据
     *
     * @param uuid 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String uuid) {
        return this.agreeLogDao.deleteById(uuid) > 0;
    }
}