package jylink.cpds.service.impl;

import com.ada.jykjcloudx.sdk.api.entity.McMessageNormalDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.common.UserUtils;
import jylink.cpds.dao.*;
import jylink.cpds.domain.*;
import jylink.cpds.feign.ApprovingFeignService;
import jylink.cpds.service.IAcceptanceApproveService;
import jylink.cpds.serviceModel.JsonResponse;
import jylink.cpds.serviceModel.JsonResponseGeneric;
import jylink.cpds.serviceModel.Tuple;
import jylink.cpds.serviceModel.WorkFlowStatus;
import jylink.cpds.serviceModel.dto.AcceptanceApproveDto;
import jylink.cpds.serviceModel.dto.AcceptanceCheckDto;
import jylink.cpds.serviceModel.dto.ApprovalWorkflowDto;
import jylink.cpds.serviceModel.params.ApprovalReasonEditModel;
import jylink.cpds.serviceModel.params.ApprovalWorkflowParamsModel;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 验收主表Service实现类
 *
 * <AUTHOR>
 */
@Component
public class AcceptanceApproveService extends ServiceImpl<IAcceptanceApproveDao, AcceptanceApprove> implements IAcceptanceApproveService {
    /**
     * dozer帮助类
     */
    private final DozerUtils dozerUtils;

    /**
     * dozer对象
     */
    private final Mapper mapper;

    /**
     * 验收表dao
     */
    private final IAcceptanceCheckDao checkDao;

    private final IAcceptanceApproveDao dao;

    /**
     * 探水通知单dao
     */
    private final ISurveyNoticeDao noticeDao;

    /**
     * 台账dao
     */
    private final IDrainAccountDao accountDao;

    /**
     * 审批微服务feign
     */
    private final ApprovingFeignService feignService;
    /**
     * 班次dao服务
     */
    @Autowired
    private IClassNoManagerDao classNoManagerDao;
    /**
     * Redis操作类
     */
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    public AcceptanceApproveService(DozerUtils dozerUtils,IAcceptanceApproveDao dao, Mapper mapper, IAcceptanceCheckDao checkDao, ISurveyNoticeDao noticeDao, IDrainAccountDao accountDao, ApprovingFeignService feignService) {
        this.dozerUtils = dozerUtils;
        this.dao=dao;
        this.mapper = mapper;
        this.checkDao = checkDao;
        this.noticeDao = noticeDao;
        this.accountDao = accountDao;
        this.feignService = feignService;
    }

    /**
     * 根据台账Id查询数据
     *
     * @param drainId 台账Id
     * @return 查询结果
     */
    @Override
    @SuppressWarnings("unchecked")
    public List<AcceptanceApproveDto> getByDrainId(String drainId) {
        //查询主表数据
        LambdaQueryWrapper<AcceptanceApprove> wrapper = new QueryWrapper<AcceptanceApprove>().lambda()
                .eq(AcceptanceApprove::getDrainId, drainId)
                .orderByDesc(AcceptanceApprove::getCreateTime);
        List<AcceptanceApprove> list = baseMapper.selectList(wrapper);
        //转换对象
        List<AcceptanceApproveDto> dtos = dozerUtils.mapList(list, AcceptanceApproveDto.class);
        if (!dtos.isEmpty()) {
            //查询验收孔表数据并关联
            List<AcceptanceCheck> checks = checkDao.getByDrainId(drainId);
            dtos.forEach(approve -> {
                List<AcceptanceCheck> currents = checks.stream().filter(a -> !StringUtils.isEmpty(a.getApproveId()) && a.getApproveId().equals(approve.getId())).collect(Collectors.toList());
                approve.setAcceptanceChecks(dozerUtils.mapList(currents, AcceptanceCheckDto.class));
            });
        }

        return dtos;
    }

    /**
     * 添加数据
     *
     * @param isApproving   是否提交审批
     * @param acceptanceIds 验收孔表Id数组
     * @param model         DTO对象
     * @return 是否添加成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Tuple<Boolean, String> add(boolean isApproving, String[] acceptanceIds, AcceptanceApproveDto model) {
        boolean result=false;
        //检测Id是否对应
        Tuple<Boolean, String> match = acceptanceCheckMatch(model.getOrgCode(), model.getDrainId(), acceptanceIds, true);
        if (!match.getFirstItem()) {
            return match;
        }

        //设置编号
        DrainAccount accountModel = accountDao.getById(model.getDrainId());
        SurveyNotice noticeModel = noticeDao.getByPlanId(accountModel.getCheckPlanId());
        String number;
        int count = baseMapper.selectCount(new QueryWrapper<AcceptanceApprove>().lambda().eq(AcceptanceApprove::getDrainId, model.getDrainId()));
        long acceptanceCount = checkDao.getCount(model.getDrainId(), model.getOrgCode());
        if (acceptanceCount != acceptanceIds.length) {
            number = noticeModel.getNumber() + "-" + (count + 1);
        } else {
            number = noticeModel.getNumber();
        }

        //保存数据
        AcceptanceApprove entity = mapper.map(model, AcceptanceApprove.class);
        entity.setNumber(number);
        entity.setId(UUID.randomUUID().toString());
        entity.setCreateTime(LocalDateTime.now().toDate());
        entity.setDelFlag(false);
        //验收日期
        entity.setAcceptanceDate(LocalDateTime.now().toDate());
        //验收班次
        List<ClassNoManager> classNoManagers = classNoManagerDao.getByOrgCode(model.getOrgCode());
        Calendar calendar = Calendar.getInstance();
        int curHourse = calendar.get(Calendar.HOUR_OF_DAY);
        classNoManagers.forEach(a -> {
            if (a.getClassEndTime() == 0 && a.getClassEndTime() < a.getClassStartTime()) {
                a.setClassEndTime(24);
            }
        });
        classNoManagers.stream().filter(a -> a.getClassStartTime() <= curHourse && a.getClassEndTime() > curHourse).findFirst().ifPresent(r -> {
            entity.setAcceptanceClass(r.getClassNoName());
        });
        result=entity.insert();
        if(!result) {
            throw new IllegalArgumentException("数据插入失败！");
        }

        //审批微服务交互
        result=requestApprove(isApproving, entity.getId(), model.getFlows());
        if(!result) {
            throw new IllegalArgumentException("调用审批服务失败！");
        }

        //验收孔表关联数据Id
        result=baseMapper.setApproveId(entity.getId(), Arrays.asList(acceptanceIds));
        if(!result) {
            throw new IllegalArgumentException("更孔关联失败！");
        }
        return new Tuple<>(true, null);
    }

    private Tuple<Boolean, String> acceptanceCheckMatch(String orgCode, String drainId, String[] acceptanceIds, boolean isAdd) {
        List<AcceptanceCheck> checks = checkDao.getByDrainId(drainId);
        List<String> ids = Arrays.stream(acceptanceIds).collect(Collectors.toList());
        //检查checktime是否有null的
        boolean hasNull = checks.stream().anyMatch(a -> a.getCheckTime() == null && ids.contains(a.getId()));
        if (hasNull) {
            return new Tuple<>(false, "提交的数据未填写验收意见！");
        }

        //检查Id数组是否正确
        for (String acceptanceId : acceptanceIds) {
            boolean match = ids.stream().anyMatch(a -> a.equals(acceptanceId));
            if (!match) {
                return new Tuple<>(false, "孔Id参数错误！");
            }

            Tuple<Boolean, String> x = getBooleanStringTuple(isAdd, checks, acceptanceId);
            if (x != null) return x;
        }

        return new Tuple<>(true, null);
    }

    private Tuple<Boolean, String> getBooleanStringTuple(boolean isAdd, List<AcceptanceCheck> checks, String acceptanceId) {
        if (isAdd) {
            Optional<AcceptanceCheck> acceptanceCheckOptional = checks.stream().filter(a -> a.getId().equals(acceptanceId)).findFirst();
            if (acceptanceCheckOptional.isPresent()) {
                AcceptanceCheck checkModel = acceptanceCheckOptional.get();
                if (!StringUtils.isEmpty(checkModel.getApproveId())) {
                    return new Tuple<>(false, "存在已验收的孔！");
                }
            }
        }
        return null;
    }

    private boolean requestApprove(boolean isApproving, String id, List<List<ApprovalWorkflowParamsModel>> flows) {
        AcceptanceApprove entity = baseMapper.selectById(id);
        McMessageNormalDTO msg = new McMessageNormalDTO();
        msg.setTitle(entity.getWorkName()+"("+entity.getSurveyWaterPosition()+")的验收表数据需要审批");
        msg.setContent(entity.getWorkName()+"("+entity.getSurveyWaterPosition()+")的验收表数据需要审批");
        msg.setUrl("/wel/messageHandlingAcceptance?id=" + id);
        msg.setMobileUrl("/ts/checkAccpetDetail?id=" + id);
        if (isApproving) {
            //修改状态为审批中
            boolean result=baseMapper.updateApprovalStatus(WorkFlowStatus.Approving.getValue(),id);
            if(!result) {
                return false;
            }
            JsonResponse jsonResponse= feignService.saveApproving(AcceptanceApprove.MODULE_NAME, id, msg, flows);
            if(jsonResponse.getStatusCode()!=HttpStatus.OK.value()){
                throw new IllegalArgumentException(jsonResponse.getMessage());
            }
        } else {
            JsonResponse jsonResponse=  feignService.save(AcceptanceApprove.MODULE_NAME, id, msg, flows);
            if(jsonResponse.getStatusCode()!=HttpStatus.OK.value()){
                throw new IllegalArgumentException(jsonResponse.getMessage());
            }
        }
        return true;
    }

    /**
     * 修改数据
     *
     * @param isApproving   是否提交审批
     * @param acceptanceIds 验收孔表Id数组
     * @param model         DTO对象
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Tuple<Boolean, String> update(boolean isApproving, String[] acceptanceIds, AcceptanceApproveDto model) {
        boolean result=false;
        Tuple<Boolean, String> match = acceptanceCheckMatch(model.getOrgCode(), model.getDrainId(), acceptanceIds, false);
        if (!match.getFirstItem()) {
            return match;
        }

        AcceptanceApprove entity = baseMapper.selectById(model.getId());
        String number = entity.getNumber();
        mapper.map(model, entity);
        entity.setNumber(number);
        //验收日期
        entity.setAcceptanceDate(LocalDateTime.now().toDate());
        //验收班次
        List<ClassNoManager> classNoManagers = classNoManagerDao.getByOrgCode(model.getOrgCode());
        Calendar calendar = Calendar.getInstance();
        int curHourse = calendar.get(Calendar.HOUR_OF_DAY);
        classNoManagers.forEach(a -> {
            if (a.getClassEndTime() == 0 && a.getClassEndTime() < a.getClassStartTime()) {
                a.setClassEndTime(24);
            }
        });
        classNoManagers.stream().filter(a -> a.getClassStartTime() <= curHourse && a.getClassEndTime() > curHourse).findFirst().ifPresent(r -> {
            entity.setAcceptanceClass(r.getClassNoName());
        });
        int rows=baseMapper.updateById(entity);
        if(rows<=0) {
            throw new IllegalArgumentException("数据更新失败！");
        }
        //审批微服务交互
        result=requestApprove(isApproving, entity.getId(), model.getFlows());
        if(!result) {
            throw new IllegalArgumentException("调用审批服务失败！");
        }
        result=baseMapper.clearApproveId(entity.getId());
        if(!result) {
            throw new IllegalArgumentException("清除孔关联失败！");
        }
        result=baseMapper.setApproveId(model.getId(), Arrays.asList(acceptanceIds));
        if(!result) {
            throw new IllegalArgumentException("关联孔关联失败！");
        }
        return new Tuple<>(true, null);
    }

    /**
     * 根据Id判断数据是否存在
     *
     * @param id 数据Id
     * @return 查询结果
     */
    @Override
    public boolean anyById(String id) {
        LambdaQueryWrapper<AcceptanceApprove> wrapper = new QueryWrapper<AcceptanceApprove>().lambda()
                .eq(AcceptanceApprove::getId, id);
        return baseMapper.selectCount(wrapper) > 0;
    }

    /**
     * 根据Id查询数据
     *
     * @param id 数据Id
     * @return 查询结果
     */
    @Override
    public AcceptanceApproveDto getById(String id) {
        AcceptanceApprove entity = baseMapper.selectById(id);
        AcceptanceApproveDto dto = mapper.map(entity, AcceptanceApproveDto.class);
        JsonResponseGeneric<List<List<ApprovalWorkflowDto>>> generic = feignService.getByDataId(id);
        if (generic.getStatusCode()!= HttpStatus.OK.value()) {
            throw new  IllegalArgumentException(generic.getMessage());
        }else {
            List<List<ApprovalWorkflowDto>> lists = generic.getData();
            List<List<ApprovalWorkflowParamsModel>> flows = dozerUtils.list2ListList(lists, ApprovalWorkflowParamsModel.class);
            dto.setFlows(flows);
        }
        List<AcceptanceCheck> checks = checkDao.getByApproveId(id);
        dto.setAcceptanceChecks(dozerUtils.mapList(checks, AcceptanceCheckDto.class));
        return dto;
    }

    /**
     * 删除数据
     *
     * @param id 数据Id
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String id) {
        baseMapper.updateStatus(WorkFlowStatus.NoAcceptance.getValue(), id);
        baseMapper.clearApproveId(id);
        return baseMapper.deleteById(id) > 0;
    }

    /**
     * 审批通过
     *
     * @param model 参数模型
     * @return 审批状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approved(ApprovalReasonEditModel model) {
        JsonResponse jsonResponse = feignService.approved(model);
        if (jsonResponse.getStatusCode()==HttpStatus.OK.value()) {
            if("approved".equals(jsonResponse.getMessage())){
                extracted(model);
            }
            return true;
        } else{
            throw new IllegalArgumentException(jsonResponse.getMessage());
        }
    }

    private void extracted(ApprovalReasonEditModel model) {
        //修改对应的状态
        boolean result= baseMapper.updateStatus(WorkFlowStatus.Acceptance.getValue(), model.getId());
        boolean updateApprovalStatus = baseMapper.updateApprovalStatus(WorkFlowStatus.Approved.getValue(), model.getId());
        if(!result){
            throw new IllegalArgumentException("修改验收表状态失败");
        }
        if (!updateApprovalStatus) {
            throw new IllegalArgumentException("修改验收主表状态失败");
        }
        String drainId = baseMapper.selectById(model.getId()).getDrainId();
//                String orgCode = UserUtils.getRedisOrgCode(redisTemplate);
        List<AcceptanceCheck> checks = checkDao.getByDrainId(drainId);
        boolean hasNoCheck = checks.stream().anyMatch(a -> a.getStatus() == WorkFlowStatus.NoAcceptance.getValue());
        if (!hasNoCheck){
            result=baseMapper.updateDrainStatus(WorkFlowStatus.Acceptance.getValue(), drainId);
            if(!result) {
                throw new IllegalArgumentException("修改台账状态失败");
            }
        }
    }

    /**
     * 提交审批
     * @param id 业务数据id
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approving(String id) {
        //修改状态为审批中
        boolean result=baseMapper.updateApprovalStatus(WorkFlowStatus.Approving.getValue(),id);
        if(!result) {
            throw new IllegalArgumentException(TfsMessageConstants.STATUS_UPDATE_ERROR);
        }
        JsonResponse jsonResponse=feignService.approving(id);
        if(jsonResponse.getStatusCode()!=HttpStatus.OK.value()){
            throw new IllegalArgumentException(jsonResponse.getMessage());
        }
        return true;
    }

    /**
     * 拒绝
     * @param model 参数对象
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refused(ApprovalReasonEditModel model) {
        //修改状态为审批中
        boolean result=baseMapper.updateApprovalStatus(WorkFlowStatus.Refused.getValue(),model.getId());
        if(!result) {
            throw new IllegalArgumentException(TfsMessageConstants.STATUS_UPDATE_ERROR);
        }
        model.setStatus(WorkFlowStatus.Refused.toString());
        JsonResponse jsonResponse=feignService.refuseOrBack(model);
        if(jsonResponse.getStatusCode()!=HttpStatus.OK.value()){
            throw new IllegalArgumentException(jsonResponse.getMessage());
        }
        return true;
    }
    /**
     * 撤回
     * @param model 参数对象
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean revoke(ApprovalReasonEditModel model) {
        //修改状态为审批中
        boolean result=baseMapper.updateApprovalStatus(WorkFlowStatus.Revoke.getValue(),model.getId());
        if(!result) {
            throw new IllegalArgumentException(TfsMessageConstants.STATUS_UPDATE_ERROR);
        }
        model.setStatus(WorkFlowStatus.Revoke.toString());
        JsonResponse jsonResponse=feignService.refuseOrBack(model);
        if(jsonResponse.getStatusCode()!=HttpStatus.OK.value()){
            throw new IllegalArgumentException(jsonResponse.getMessage());
        }
        return true;
    }

}
