package jylink.cpds.service.impl;

import com.github.dozermapper.core.Mapper;
import com.github.pagehelper.page.PageMethod;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.dao.IEnforcementClassifyOneDao;
import jylink.cpds.domain.EnforcementClassifyOne;
import jylink.cpds.service.IEnforcementClassifyOneService;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.PagerParams;
import jylink.cpds.serviceModel.dto.EnforcementClassifyOneDto;
import jylink.cpds.serviceModel.params.EnforcementClassifyOneQueryModel;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

/**
 * (EnforcementClassifyOne)表服务实现类
 *
 * <AUTHOR>
 * @since 2020-10-12 16:31:41
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class EnforcementClassifyOneServiceImpl implements IEnforcementClassifyOneService {
    @Autowired
    private IEnforcementClassifyOneDao enforcementClassifyOneDao;
    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;
    /**
     * dozer 帮助类
     */
    @Autowired
    private DozerUtils mapperUtils;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public EnforcementClassifyOneDto queryById(String id,String orgCode) {
       EnforcementClassifyOne enforcementClassifyOne = enforcementClassifyOneDao.queryById(id,orgCode);
       return  mapper.map(enforcementClassifyOne, EnforcementClassifyOneDto.class);
    }
    /**
     * 通过ID确认数据是否存在
     *
     * @param id 主键
     * @return boolean
     */
     @Override
    public boolean anyById(String id,String orgCode){
        return queryById(id,orgCode) == null ? false : true;
    }
    /**
     * 数据查询
     * @return 
     */
     @Override
    public Pager<EnforcementClassifyOneDto> getByPager(PagerParams<EnforcementClassifyOneQueryModel> queryModel , String orgCode){
        PageMethod.startPage(queryModel.getCurrentPage(), queryModel.getPageSize());
        EnforcementClassifyOne enforcementClassifyOne =  mapper.map(queryModel.getObjParams(), EnforcementClassifyOne.class);
        enforcementClassifyOne.setOrgCode(orgCode);
        enforcementClassifyOne.setDelFlag(false);
        List<EnforcementClassifyOne> enforcementClassifyOneLists = enforcementClassifyOneDao.queryAll(enforcementClassifyOne);
        List<EnforcementClassifyOneDto> listDto = mapperUtils.mapList(enforcementClassifyOneLists, EnforcementClassifyOneDto.class);
        Pager<EnforcementClassifyOneDto> pager = new Pager<>(enforcementClassifyOneDao.queryAllCount(enforcementClassifyOne), listDto);
        return pager;
    }

    /**
     * 获取执法分类一级菜单数据信息
     * @return  查询结果
     */
    @Override
    public List<EnforcementClassifyOneDto> getList() {
        EnforcementClassifyOne one = new EnforcementClassifyOne();
        List<EnforcementClassifyOne> enforcementClassifyOnes = enforcementClassifyOneDao.queryAll(one);
        List<EnforcementClassifyOneDto> enforcementClassifyOneDtos = mapperUtils.mapList(enforcementClassifyOnes, EnforcementClassifyOneDto.class);
        return enforcementClassifyOneDtos;
    }

    /**
     * 新增数据
     *
     * @param enforcementClassifyOneDto 实例对象
     * @return 实例对象
     */
    @Override
    public boolean insert(EnforcementClassifyOneDto enforcementClassifyOneDto) {
       EnforcementClassifyOne enforcementClassifyOne =  mapper.map(enforcementClassifyOneDto, EnforcementClassifyOne.class);
       enforcementClassifyOne.setCreateTime(LocalDateTime.now().toDate());
       enforcementClassifyOne.setDelFlag(false);
       //判断id是否为空  id如果不为空则是从 文件上传接口调用过来的 , 不需要再使用uuid
       if(StringUtils.isBlank(enforcementClassifyOneDto.getId())){
          enforcementClassifyOne.setId( UUID.randomUUID().toString());
       }
       int res = enforcementClassifyOneDao.insert(enforcementClassifyOne);
       return res == 1;
    }

    /**
     * 修改数据
     *
     * @param enforcementClassifyOneDto 实例对象
     * @return 实例对象
     */
    @Override
    public boolean update(EnforcementClassifyOneDto enforcementClassifyOneDto) {
        EnforcementClassifyOne enforcementClassifyOne =  mapper.map(enforcementClassifyOneDto, EnforcementClassifyOne.class);
        int res = enforcementClassifyOneDao.update(enforcementClassifyOne);
        return res == 1;
    }

    /**
     * 通过主键删除数据
     *
     * @param uuid 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String uuid) {
        return this.enforcementClassifyOneDao.deleteById(uuid) > 0;
    }
}