package jylink.cpds.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.CpdsConstants;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.common.HttpClientUtils;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.dao.*;
import jylink.cpds.domain.*;
import jylink.cpds.feign.VideoFeignService;
import jylink.cpds.service.*;
import jylink.cpds.serviceModel.AI.*;
import jylink.cpds.serviceModel.analysis.AnalysisStatus;
import jylink.cpds.serviceModel.JsonResponseGeneric;
import jylink.cpds.serviceModel.dto.AnalysisWorkDto;
import jylink.cpds.serviceModel.dto.DrainAccountDto;
import jylink.cpds.serviceModel.dto.TunnelDesignDto;
import jylink.cpds.serviceModel.hikvision.HKChannels;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> AI下达任务服务实现类
 */
@Component
public class AnalysisWorkService implements IAnalysisWorkService {

    /**
     * 海康服务
     */
    @Autowired
    private IHikvisionService hikvisionService;

    /**
     * dao服务
     */
    @Autowired
    private IAnalysisWorkDao dao;

    /**
     * 探水设计服务
     */
    @Autowired
    @Qualifier("tunnelDesignService")
    private ITunnelDesignService designService;

    /**
     * 探水台账服务
     */
    @Autowired
    private IDrainAccountService accountService;

    /**
     * 字典表dao服务
     */
    @Autowired
    private IDictDao dictDao;

    /**
     * dozer帮助类
     */
    @Autowired
    private DozerUtils mapperUtils;

    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * 海康视频服务Fegin
     */
    @Autowired
    private VideoFeignService videoService;

    /**
     * 日志子表信息
     */
    @Autowired
    private ILogRecordDetailService logRecordDetailService;

    /**
     * LED配置dao服务
     */
    @Autowired
    private ILEDSettingDao lEDSettingDao;

    /**
     * led和摄像机配置dao服务
     */
    @Autowired
    private ITunnelLedCameraDao tunnelLedCameraDao;

    /**
     * 验收表dao服务
     */
    @Autowired
    private IAcceptanceCheckDao acceptanceCheckDao;

    /**
     * 记录下发任务记录
     *
     * @return 是否添加成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JsonResponseGeneric addAnalysis(String drainId, String orgCode, String userId,String logId) {
        JsonResponseGeneric jsonResponseGeneric = new JsonResponseGeneric(0,"","");
        // 查询台账和设计相关信息
        DrainAccountDto accountDto = accountService.getById(drainId);
        TunnelDesignDto designDto = designService.getById(accountDto.getTunnelId(), orgCode);
        // 获取孔号
        List<String> drainIds = new ArrayList<>();
        drainIds.add(accountDto.getId());
        List<AcceptanceCheck> details = acceptanceCheckDao.getByDrainIds(drainIds);
        List<String> holeNumbers = details.stream().map(AcceptanceCheck::getHoleNo).collect(Collectors.toList());
        // 创建下发任务参数对象
        AnalysisJob job = new AnalysisJob();
        job.setOrgCode(orgCode);
        job.setPoleLength(designDto.getPoleLength());
        job.setSurveyWaterMileage(String.valueOf(accountDto.getSurveyWaterMileage()));
        job.setWorkCode(designDto.getWorkCode());
        job.setHoleNumbers(holeNumbers);
        job.setUserId(userId);
        // 获取海康资源
        TunnelLedCamera setting = tunnelLedCameraDao.getByTunnelId(accountDto.getTunnelId());
        if (setting == null){
            jsonResponseGeneric.setStatusCode(-1);
            jsonResponseGeneric.setMessage("未完成工作面硬件绑定");
            return jsonResponseGeneric;
        }


        List<HKChannels> channels = hikvisionService.getResource(orgCode);
        Optional<HKChannels> optional = channels.stream()
                .filter(c -> c.getIndexCode().equals(setting.getIndexCode())).findFirst();
        if (!optional.isPresent()) {
            jsonResponseGeneric.setStatusCode(-1);
            jsonResponseGeneric.setMessage("未找到摄像头");
            return jsonResponseGeneric;
        }
        // 创建rtsp对象
        HKChannels channel = optional.get();
        Rtsp rtsp = new Rtsp();
        rtsp.setChannel(String.valueOf(channel.getChannel()));
        rtsp.setHkOrgCode(channel.getHkOrgCode());
        rtsp.setRtsp(channel.getRtspUrl());
        job.getRtspList().add(rtsp);
        // Led信息
        LEDSetting lEDSetting = lEDSettingDao.getById(orgCode, setting.getLedId());
        job.setLedIP(lEDSetting.getIp());
        // 发送post请求
         if (!releaseTask(job,logId)){
             jsonResponseGeneric.setStatusCode(-1);
             jsonResponseGeneric.setMessage("与AI交互失败");
             //添加日志子表信息
             logRecordDetailService.addDetail(logId, "与AI交互失败", "web", 1);
             return jsonResponseGeneric;
         }
        //添加日志子表信息
        logRecordDetailService.addDetail(logId, "与AI交互成功", "web", 0);
        // 将任务数据保存至数据库中
        List<AnalysisWork> analysisWorks = new ArrayList<>(1);
        AnalysisWork model = new AnalysisWork();
        model.setId(UUID.randomUUID().toString());
        model.setOrgCode(accountDto.getOrgCode());
        model.setOrgName(accountDto.getOrgName());
        model.setCameraName(channel.getName());
        model.setIndexCode(channel.getIndexCode());
        model.setDrainId(drainId);
        model.setLedId(setting.getLedId());
        model.setHkOrgCode(channel.getHkOrgCode());
        model.setChannel(channel.getChannel());
        model.setStatus(AnalysisStatus.SendAwait.getValue());
        model.setDelFlag(false);
        model.setCreateTime(LocalDateTime.now().toDate());
        Integer maxOrder = dao.getMaxOrder(drainId);
        model.setSortOrder(maxOrder == null ? 1 : (maxOrder + 1));
        model.setUserId(userId);
        analysisWorks.add(model);
        if (dao.batchAdd(analysisWorks)){
            jsonResponseGeneric.setStatusCode(200);
            jsonResponseGeneric.setMessage("");
            //添加日志子表信息
            logRecordDetailService.addDetail(logId, "实时任务基本信息添加成功", "web", 0);
            return jsonResponseGeneric;
        }else {
            jsonResponseGeneric.setStatusCode(-1);
            jsonResponseGeneric.setMessage("识别任务添加失败");
            //添加日志子表信息
            logRecordDetailService.addDetail(logId, "实时任务基本信息添加失败", "web", 1);
            return jsonResponseGeneric;
        }
    }

    /**
     * 下发识别任务
     *
     * @param job 实体参数对象
     * @return 是否下达成功
     */
    @Override
    public boolean releaseTask(AnalysisJob job,String logId) {
        // 发送post请求
        AiAnalysisJob aiAnalysisJob = new AiAnalysisJob();
        aiAnalysisJob.setRealtime(job);
        aiAnalysisJob.setHistory(new AiAnalysisTest());
        String content = JSON.toJSONString(aiAnalysisJob);
        Map<String, String> map = new HashMap<>();
        map.put(CpdsConstants.CONTENT_TYPE_KEY, CpdsConstants.APPLICATION_JSON);
        try {
            Dict dict = dictDao.getByGroupcode(CpdsConstants.AI_SERVICE_PATH_GROUP_CODE, job.getOrgCode()).get(0);
            String result = HttpClientUtils.sendPostDataByJson(dict.getDictValue() + "/job", content, "utf8", map);
            AnalysisResponse<Object> analysisResult = JSON.parseObject(result,
                    new TypeReference<AnalysisResponse<Object>>() {
                    });
            if (!"ok".equalsIgnoreCase(analysisResult.getMessage())){
                //添加日志操作子表
                logRecordDetailService.addDetail(logId, TfsMessageConstants.AI_FEEDBACK_RESULT +analysisResult.getMessage(), "web", 1);
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logRecordDetailService.addDetail(logId, TfsMessageConstants.AI_FEEDBACK_RESULT+e.getMessage(), "web", 1);
            return false;
        }
        return true;
    }

    /**
     * 下发历史识别任务
     *
     * @param job 实体参数对象
     * @return 是否下达成功
     */
    @Override
    public boolean releaseHistoryTask(AnalysisHistoryJob job,String logId) {
        // 发送post请求
        AiAnalysisHistoryJob aiAnalysisHistoryJob = new AiAnalysisHistoryJob();
        aiAnalysisHistoryJob.setRealtime(new AiAnalysisTest());
        aiAnalysisHistoryJob.setHistory(job);
        String content = JSON.toJSONString(aiAnalysisHistoryJob);

        Map<String, String> map = new HashMap<>();
        map.put(CpdsConstants.CONTENT_TYPE_KEY, CpdsConstants.APPLICATION_JSON);
        try {
            Dict dict = dictDao.getByGroupcode(CpdsConstants.AI_SERVICE_PATH_GROUP_CODE, job.getOrgCode()).get(0);
            String result = HttpClientUtils.sendPostDataByJson(dict.getDictValue() + "/job", content, "utf8", map);
            AnalysisResponse<Object> analysisResult = JSON.parseObject(result,
                    new TypeReference<AnalysisResponse<Object>>() {
                    });
            if (!"ok".equalsIgnoreCase(analysisResult.getMessage())){
                //添加日志操作子表
                logRecordDetailService.addDetail(logId, TfsMessageConstants.AI_FEEDBACK_RESULT+analysisResult.getMessage(), "web", 1);
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 下发识别任务 取消
     *
     * @param job 实体参数对象
     * @return 是否下达成功
     */
    @Override
    public boolean cancelReleaseTask(AnalysisCancelJob job,String logId) {
        // 发送post请求
        String content = JSON.toJSONString(job);
        Map<String, String> map = new HashMap<>();
        map.put(CpdsConstants.CONTENT_TYPE_KEY, CpdsConstants.APPLICATION_JSON);
        try {
            Dict dict = dictDao.getByGroupcode(CpdsConstants.AI_SERVICE_PATH_GROUP_CODE, job.getOrgCode()).get(0);
            String result = HttpClientUtils.sendPostDataByJson(dict.getDictValue() + "/ter_job", content, "utf8", map);
            AnalysisResponse<Object> analysisResult = JSON.parseObject(result,
                    new TypeReference<AnalysisResponse<Object>>() {
                    });
            if (!"ok".equalsIgnoreCase(analysisResult.getMessage())){
                //添加日志操作子表
                logRecordDetailService.addDetail(logId, TfsMessageConstants.AI_FEEDBACK_RESULT+analysisResult.getMessage(), "web", 1);
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    /**
     * 下发取消任务
     *
     * @param job 实体参数对象
     * @return 是否下达成功
     */
    @Override
    public boolean cancelHistoryReleaseTask(AnalysisHistoryCancelJob job,String logId) {
        // 发送post请求
        String content = JSON.toJSONString(job);
        Map<String, String> map = new HashMap<>();
        map.put(CpdsConstants.CONTENT_TYPE_KEY, CpdsConstants.APPLICATION_JSON);
        try {
            Dict dict = dictDao.getByGroupcode(CpdsConstants.AI_SERVICE_PATH_GROUP_CODE, job.getOrgCode()).get(0);
            String result = HttpClientUtils.sendPostDataByJson(dict.getDictValue() + "/ter_job", content, "utf8", map);
            AnalysisResponse<Object> analysisResult = JSON.parseObject(result,
                    new TypeReference<AnalysisResponse<Object>>() {
                    });
            if (!"ok".equalsIgnoreCase(analysisResult.getMessage())){
                //添加日志操作子表
                logRecordDetailService.addDetail(logId, TfsMessageConstants.AI_FEEDBACK_RESULT+analysisResult.getMessage(), "web", 1);
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    /**
     * 更新数据状态
     *
     * @param id     任务id
     * @param status 任务状态
     * @return
     */
    @Override
    public boolean updateStatus(String id, int status) {
        return dao.updateStatus(id, status);
    }

    /**
     * 获取工作面下正在识别的任务
     *
     * @param drainId 探水台账Id
     * @return 查询结果
     */
    @Override
    public Integer getAnalysingByDrainId(String drainId, int status) {
        return dao.getAnalysingByDrainId(drainId, status);
    }

    /**
     * 根据Id获取数据
     *
     * @param id 数据Id
     * @return 实体对象
     */
    @Override
    public AnalysisWorkDto getById(String id) {
        AnalysisWorkDto dto = mapper.map(dao.getById(id), AnalysisWorkDto.class);
        return dto;
    }

    /**
     * 根据台账Id查询数据
     *
     * @param drainId 台账Id
     * @return 查询结果
     */
    @Override
    public List<AnalysisWorkDto> getByDrainId(String drainId) {
        List<AnalysisWork> analysisWorks = dao.getByDrainId(drainId);
        return mapperUtils.mapList(analysisWorks, AnalysisWorkDto.class);
    }

    /**
     * 根据台账Id查询数据 且完成实时识别
     *
     * @param drainId 台账Id
     * @param status  状态
     * @return 查询结果
     */
    @Override
    public List<AnalysisWorkDto> getByDrainIdAndStatus(String drainId, int status) {
        List<AnalysisWork> analysisWorks = dao.getByDrainIdAndStatus(drainId, status);
        return mapperUtils.mapList(analysisWorks, AnalysisWorkDto.class);
    }

    /**
     * 根据状态查询数据
     *
     * @param status  状态
     * @param orgCode 机构编码
     * @return 查询结果
     */
    @Override
    public List<AnalysisWorkDto> getAllAnalysing(int status, String orgCode) {
        List<AnalysisWork> analysisWorks = dao.getAllAnalysing(status, orgCode);
        return mapperUtils.mapList(analysisWorks, AnalysisWorkDto.class);
    }

    /**
     * 根据台账Id和通道号查询数据
     *
     * @param drainId 台账Id
     * @param channel 通道号
     * @return 查询结果
     */
    @Override
    public AnalysisWorkDto getByDrainIdAndChannel(String drainId, int channel) {
        AnalysisWork work = dao.getByDrainIdAndChannel(drainId, channel);
        return mapper.map(work, AnalysisWorkDto.class);
    }

    /**
     * 判断数据是否存在
     *
     * @param id 数据Id
     * @return 查询结果
     */
    @Override
    public boolean anyById(String id) {
        return dao.anyById(id);
    }

    /**
     * 获取监控点编号
     *
     * @param channelNumber 通道号
     * @param orgCode       机构编码
     * @return 查询结果
     */
    @Override
    public String getIndexcode(String channelNumber, String orgCode) {
        return dao.getIndexcode(channelNumber, orgCode);
    }

    /**
     * 查询是否有正常结束的
     *
     * @param drainId 台账Id
     * @param status  状态（正常结束）
     * @return 查询结果
     */
    @Override
    public boolean anyNormalFinish(String drainId, int status) {
        return dao.anyNormalFinish(drainId, status);
    }
}
