package jylink.cpds.service.impl;

import com.ada.jykjcloudx.sdk.api.entity.McMessageNormalDTO;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.CpdsConstants;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.dao.*;
import jylink.cpds.domain.*;
import jylink.cpds.feign.ApprovingFeignService;
import jylink.cpds.service.IApprovalWorkflowService;
import jylink.cpds.service.IHoleDetailService;
import jylink.cpds.service.ITransferDetectionService;
import jylink.cpds.serviceModel.JsonResponse;
import jylink.cpds.serviceModel.JsonResponseGeneric;
import jylink.cpds.serviceModel.WorkFlowStatus;
import jylink.cpds.serviceModel.dto.*;
import jylink.cpds.serviceModel.params.ApprovalReasonEditModel;
import jylink.cpds.serviceModel.params.ApprovalWorkflowParamsModel;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 探放水安全确认移交表 设计实现类
 */
@Component
public class TransferDetectionService implements ITransferDetectionService {
    /**
     * dozer Mapper对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * dao对象
     */
    @Autowired
    private ITransferDetectionDao dao;

    /**
     * 审批主表服务
     */
    @Autowired
    private IAcceptanceApproveDao acceptanceApproveDao;

    /**
     * 探放水钻孔验收表dao服务
     */
    @Autowired
    private IAcceptanceCheckDao acceptanceCheckDao;

    /**
     * 探水台账dao对象
     */
    @Autowired
    private IDrainAccountDao drainAccountDao;

    /**
     * 审批流程service
     */
    @Autowired
    private IApprovalWorkflowService approvalWorkflowService;
    /**
     * 探水设计dao对象
     */
    @Autowired
    private ITunnelDesignDao tunnelDesignDao;

    /**
     * 探水通知单
     */
    @Autowired
    private ISurveyNoticeDao surveyNoticeDao;
    /**
     * 探水计划dao服务
     */
    @Autowired
    private ICheckPlanDao checkPlanDao;

    /**
     * 作业记录id
     */
    @Autowired
    private IHoleDetailDao holeDetailDao;
    /**
     * 班次管理dao对象
     */
    @Autowired
    private IClassNoManagerDao classNoManagerDao;
    /**
     * dozer 帮助类
     */
    @Autowired
    private DozerUtils mapperUtils;
    @Autowired
    private IHoleDetailService holeDetailService;
    /**
     * feign服务
     */
    @Autowired
    private ApprovingFeignService feign;


    @Autowired
    DataSourceTransactionManager dataSourceTransactionManager;
    @Autowired
    TransactionDefinition transactionDefinition;

    /**
     * 根据id查询
     *
     * @param id uuid
     * @return dto模型集合
     */
    @Override
    public TransferDetectionDto getById(String id, String orgCode) {
        TransferDetection model = dao.getById(id, orgCode);
        TransferDetectionDto transferDetectionDto = mapper.map(model, TransferDetectionDto.class);
        JsonResponseGeneric<List<List<ApprovalWorkflowDto>>> generic = feign.getByDataId(id);
        if (generic.getStatusCode()!= HttpStatus.OK.value()) {
            //失败返回错误消息
            throw new  IllegalArgumentException(generic.getMessage());
        }else {
            transferDetectionDto.setFlows(generic.getData());
        }
        // 处理时间段
//        List<String> prospectingDate = splitString(model.getProspectingDate());
//        if (prospectingDate.size() > 1) {
//            transferDetectionDto.setProspectingDateStart(prospectingDate.get(0));
//            transferDetectionDto.setProspectingDateEnd(prospectingDate.get(1));
//        }

        return transferDetectionDto;
    }

    /**
     * 根据台账id查询
     *
     * @param drainId 台账id
     * @param orgCode 机构编码
     * @return 对象实例
     */
    @Override
    public TransferDetectionDto getByDrainId(String drainId, String orgCode) {
        TransferDetection model = new TransferDetection();
        TransferDetectionDto transferDetectionDto = new TransferDetectionDto();
        List<List<ApprovalWorkflowDto>> flows = new ArrayList<>();
        List<AcceptanceCheck> dateList = acceptanceCheckDao.getByDrainId(drainId);
        SimpleDateFormat format = new SimpleDateFormat(CpdsConstants.DATE_FORMAT);
        if (dao.anyByDrainId(drainId, orgCode)){
            model = dao.getByDrainId(drainId, orgCode);
            if (model == null) {
                return null;
            }
            transferDetectionDto = mapper.map(model, TransferDetectionDto.class);
            JsonResponseGeneric<List<List<ApprovalWorkflowDto>>> generic = feign.getByDataId(transferDetectionDto.getId());
            if (generic.getStatusCode()!= HttpStatus.OK.value()) {
                //失败返回错误消息
                throw new  IllegalArgumentException(generic.getMessage());
            }else {
                transferDetectionDto.setFlows(generic.getData());
            }
        }else{
            CheckPlan checkPlan = checkPlanDao.getById(drainAccountDao.getById(drainId).getCheckPlanId());
            List<HoleDetailDto> holeDetailDtos = holeDetailService.getByDrainId(drainId, orgCode);
            SurveyNotice surveyNotice = surveyNoticeDao.getByPlanId(checkPlan.getId());
            model.setNumber(surveyNotice==null?"":surveyNotice.getNumber());
            HoleDetailDto holeDetailDto= new HoleDetailDto();
            HoleDetailDto detailDto = new HoleDetailDto();
            if (holeDetailDtos.stream().filter(h->h.getStartDrillTime()!=null).count()>0) {
                holeDetailDto = holeDetailDtos.stream().filter(h->h.getStartDrillTime()!=null).sorted(Comparator.comparing(HoleDetailDto::getStartDrillTime)).findFirst().get();
            }
            if (holeDetailDtos.stream().filter(h->h.getEndDrillTime()!=null).count()>0) {
                detailDto = holeDetailDtos.stream().filter(h->h.getEndDrillTime()!=null).sorted(Comparator.comparing(HoleDetailDto::getEndDrillTime).reversed()).findFirst().get();
            }
            SimpleDateFormat sdf = new SimpleDateFormat(CpdsConstants.DATE_FORMAT_DAY);
            String startClass = holeDetailDto.getReportClassNumValue()==null||holeDetailDto.getReportClassNumValue().isEmpty()?"":holeDetailDto.getReportClassNumValue();
            String endClass = detailDto.getReportClassNumValue()==null||detailDto.getReportClassNumValue().isEmpty()?"":detailDto.getReportClassNumValue();
            String startTime=holeDetailDto.getStartDrillTime()==null?"":sdf.format(holeDetailDto.getStartDrillTime());
            String endTime=detailDto.getEndDrillTime()==null?"":sdf.format(detailDto.getEndDrillTime());
            String prospectingDate = startTime+startClass+"-"+endTime+endClass;
            model.setWorkName(checkPlan.getWorkName());
            model.setCheckPosition(checkPlan.getCheckPosition());
            model.setProspectingDate(prospectingDate);
            transferDetectionDto = mapper.map(model, TransferDetectionDto.class);
            transferDetectionDto.setDrainId(drainId);
            transferDetectionDto.setFlows(flows);
            transferDetectionDto.setOverDistance(30);
            String date = format.format(acceptanceApproveDao.getMaxApproveDate(drainId, orgCode));
            transferDetectionDto.setAcceptanceDate(date);
            transferDetectionDto.setProspectingDateStart(holeDetailDto.getStartDrillTime());
            transferDetectionDto.setProspectingClassStart(holeDetailDto.getReportClassNumValue());
            transferDetectionDto.setProspectingDateEnd(detailDto.getEndDrillTime());
            transferDetectionDto.setProspectingClassEnd(detailDto.getReportClassNumValue());
        }
        List<String> ids = dateList.stream().map(AcceptanceCheck::getHoleDetailId).distinct().collect(Collectors.toList());
        List<HoleDetail> holeDetails = holeDetailDao.getByIds(ids);
        // 钻孔施工情况
        List<DrillingDescriptionDto> drillingDescription=new ArrayList<>();
        List<AddTransferListDto> addTransferListDto = new ArrayList<>(dateList.size());
        for (AcceptanceCheck acceptanceCheck : dateList) {
            AddTransferListDto addDto = mapper.map(acceptanceCheck, AddTransferListDto.class,
                    "acceptanceCheckTransferMap");
            addDto.setAcceptanceId(acceptanceCheck.getId());
            addDto.setApproveId(acceptanceCheck.getApproveId());
            addDto.setHoleDetailId(acceptanceCheck.getHoleDetailId());
            addTransferListDto.add(addDto);
            //获取钻探情况数据
            Optional<HoleDetail> first = holeDetails.stream().filter(h -> h.getId().equals(acceptanceCheck.getHoleDetailId())).findFirst();
            if (first.isPresent()){
                HoleDetail holeDetail = first.get();
                DrillingDescriptionDto drillingDescriptionDto = new DrillingDescriptionDto();
                String isNormol;
                if (holeDetail.getHoleStatus()!=null&&holeDetail.getHoleStatus()==0) {
                    isNormol="正常";
                } else if(holeDetail.getHoleStatus()!=null&&holeDetail.getHoleStatus()==1) {
                    isNormol="异常";
                } else {
                    isNormol="";
                }
                String holeCondition = holeDetail.getHoleCondition()==null||holeDetail.getHoleCondition().isEmpty()?"":holeDetail.getHoleCondition();
                String remark = acceptanceCheck.getRemark()==null||acceptanceCheck.getRemark().isEmpty()?"":acceptanceCheck.getRemark();
                drillingDescriptionDto.setHoleNo(acceptanceCheck.getHoleNo());
                drillingDescriptionDto.setIsNormol(isNormol);
                drillingDescriptionDto.setHoleCondition(holeCondition);
                drillingDescriptionDto.setRemark(remark);
                drillingDescription.add(drillingDescriptionDto);
            }
        }
        transferDetectionDto.setDrillingDescription(drillingDescription);
        transferDetectionDto.setAddTransferListDto(addTransferListDto);
        return transferDetectionDto;
    }

    /**
     * 根据台账id查询 添加所需要的信息
     *
     * @param drainId 台账id
     * @param orgCode 机构编码
     * @return 对象实例
     */
    @Override
    public AddTransferDetectionDto getAddMessage(String drainId, String orgCode) {
        AddTransferDetectionDto addTransferDetectionDto = new AddTransferDetectionDto();
        List<AcceptanceCheck> dateList = acceptanceCheckDao.getByDrainId(drainId);
        if (dateList == null || dateList.isEmpty()) {
            return addTransferDetectionDto;
        }

        List<String> addClassName = new ArrayList<>();
        List<AddTransferListDto> addTransferListDto = new ArrayList<>();
        dateList.forEach(date -> {
            AddTransferListDto addTransferDto = new AddTransferListDto();
            addTransferDto.setClassNum(date.getHoleClassNumValue());
            addTransferDto.setHoleAzimuth(date.getHoleAzimuth());
            addTransferDto.setHoleDistance(date.getHoleDistance());
            addTransferDto.setHoleNo(date.getHoleNo());
            addTransferDto.setHoleObliquity(date.getHoleObliquity());
            addTransferDto.setHoleStartTime(date.getHoleStartTime());
            addTransferListDto.add(addTransferDto);
            // 班次信息
            addClassName.add(date.getHoleClassNumValue());
        });
        List<String> addClassNameList = addClassName.stream().distinct().collect(Collectors.toList());
        String className = "";
        if (addClassNameList.size() > 1) {
            for (int i = 0; i < addClassNameList.size() - 1; i++) {
                className = addClassNameList.get(i) + "、";
            }
            className = className + addClassNameList.get(addClassNameList.size() - 1);
        } else if (addClassNameList.size() == 1) {
            className = addClassNameList.get(0);
        }
        addTransferDetectionDto.setClassName(className);
        addTransferDetectionDto.setWorkName(dateList.get(0).getWorkName());
        addTransferDetectionDto.setCheckPosition(dateList.get(0).getSurveyWaterMileage());
        addTransferDetectionDto.setPlanHoleNoId(dateList.get(0).getPlanHoleNoId());
        addTransferDetectionDto.setAddTransferListDto(addTransferListDto);

        DrainAccount drainAccount = drainAccountDao.getById(drainId);
        CheckPlan checkPlan = checkPlanDao.getById(drainAccount.getCheckPlanId());

        addTransferDetectionDto.setWorkOrgName(checkPlan.getWorkOrgName());
        TunnelDesign tunnelDesign = tunnelDesignDao.getById(drainAccount.getTunnelId());
        double distance = 0;
        distance = tunnelDesign.getTunnelDistance() - drainAccount.getSurveyWaterMileage();
        if (distance < 0) {
            distance = 0;
        }
        addTransferDetectionDto.setSurplusDistance(Integer.parseInt(new java.text.DecimalFormat("0").format(distance)));
        //List<DrainAccount> drainAccounts = drainAccountDao.getByWorkName(orgCode, drainAccount.getWorkName());
        double drivedDistanceMax = tunnelDesign.getTunnelDistance();
        /*
         * if (drainAccounts.size()>1){ drivedDistanceMax =
         * tunnelDesign.getTunnelDistance()-drainAccounts.get(1).getSurveyWaterMileage()
         * ; if (drivedDistanceMax<0){ drivedDistanceMax=0; } }else { drivedDistanceMax=
         * tunnelDesign.getTunnelDistance(); }
         */
        addTransferDetectionDto
                .setDrivedDistanceMax(Integer.parseInt(new java.text.DecimalFormat("0").format(drivedDistanceMax)));

        return addTransferDetectionDto;
    }

    /**
     * 根据机构编码查询
     *
     * @param orgCode 机构编码
     * @return dto模型集合
     */
    @Override
    public List<TransferDetectionDto> getByOrgCode(String orgCode) {
        List<TransferDetection> dataList = dao.getByOrgCode(orgCode);
        List<TransferDetectionDto> transferDetectionDtoList = mapperUtils.mapList(dataList, TransferDetectionDto.class);
//        for (int i = 0; i < transferDetectionDtoList.size(); i++) {
//            for (int j = 0; j < dataList.size(); j++) {
//                if (dataList.get(j).getId().equals(transferDetectionDtoList.get(i).getId())) {
//                    // 处理时间段
//                    List<String> prospectingDate = splitString(dataList.get(j).getProspectingDate());
//                    if (prospectingDate.size() > 0) {
//                        transferDetectionDtoList.get(i).setProspectingDateStart(prospectingDate.get(0));
//                        transferDetectionDtoList.get(i).setProspectingDateEnd(prospectingDate.get(1));
//                    }
//                    continue;
//                }
//            }
//        }
        return transferDetectionDtoList;
    }


    /**
     * 根据工作面名称和探水时间查询
     *
     * @param workName        工作面名称
     * @param surveyWaterDate 探水时间
     * @param orgCode         根据机构编码
     * @return 对象实例
     */
    @Override
    public List<TransferDetectionDto> getByWorkAndDate(String workName, String surveyWaterDate, String orgCode) {
        List<TransferDetection> dataList = dao.getByWorkAndDate(workName, surveyWaterDate, orgCode);
        List<TransferDetectionDto> dtoList = new ArrayList<TransferDetectionDto>();
        mapper.map(dataList, dtoList);
        return dtoList;
    }

    /**
     * 添加数据
     *
     * @param instance 对象实例
     * @return 是否添加成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(TransferDetectionDto instance,List<List<ApprovalWorkflowParamsModel>> flows) {
        if (instance.getId() == null || instance.getId().isEmpty()) {
            instance.setId(UUID.randomUUID().toString());
        }
        DrainAccount drainAccount = drainAccountDao.getById(instance.getDrainId());
        CheckPlan checkPlan = checkPlanDao.getById(drainAccount.getCheckPlanId());
        TransferDetection model = mapper.map(instance, TransferDetection.class);
        // 探放水日期处理
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(CpdsConstants.DATE_FORMAT_DAY);
        String prospectingDate = simpleDateFormat.format(instance.getProspectingDateStart()) +instance.getProspectingClassStart()+ "-" + simpleDateFormat.format(instance.getProspectingDateEnd())+instance.getProspectingClassEnd();
        model.setWorkName(drainAccount.getWorkName());
        model.setCheckPosition(checkPlan.getCheckPosition());
        model.setProspectingDate(prospectingDate);
        model.setCreateTime(LocalDateTime.now().toDate());
        model.setDelFlag(false);
        model.setIsViewed(false);
        model.setStatus(WorkFlowStatus.Approving.getValue());
        // 根据key值去寻找对应班次名称
//        if (model.getDrivedClassNumKey() != null) {
//            String drivedClassNum = classNoManagerDao.getClassName(model.getOrgCode(), model.getDrivedClassNumKey())
//                    .getClassNoName();
//            model.setDrivedClassNum(drivedClassNum);
//        }
//        if (model.getSurplusClassNumKey() != null) {
//            String surplusClassNum = classNoManagerDao.getClassName(model.getOrgCode(), model.getSurplusClassNumKey())
//                    .getClassNoName();
//            model.setSurplusClassNum(surplusClassNum);
//        }
//        boolean addSublist = drainAccountDao.updateSublist(model.getDrainId(), false, false, false, true, false, false);
        boolean addModel=dao.add(model);
        if(!addModel) {
            return false;
        }
        McMessageNormalDTO mcMessageNormalDTO = new McMessageNormalDTO();
        String title = model.getWorkName() + "(" + model.getCheckPosition() + ")" + "的探放水安全确认移交表需要审批";
        mcMessageNormalDTO.setTitle(title);
        mcMessageNormalDTO.setContent(title);
        mcMessageNormalDTO.setUrl("/wel/messageHandlingTransferDetection?id=" + model.getDrainId());
        mcMessageNormalDTO.setMobileUrl("/ts/drillingAnalysisDetail?id=" + model.getDrainId());

        if (!checkFlowUserId(flows,instance.getCheckId(),instance.getChiefEngineerId())) {
            throw new IllegalArgumentException("审批流程中必须有总工程师及防治水副总!");
        }
        JsonResponse jsonResponse=feign.saveApproving(TransferDetection.MODULE_NAME, model.getId(), mcMessageNormalDTO, flows);
        if(jsonResponse.getStatusCode()!=HttpStatus.OK.value()){
            throw new IllegalArgumentException(jsonResponse.getMessage());
        }
        return true;
    }

    /**
     * 检查安全确认移交表中的审批人员是否有 总工程师师及防治水副总
     * <AUTHOR>
     * @date 2020/4/16 15:21
     * @param flows 审批流程
     * @param checkId 防治水副总id
     * @param chiefEngineerId 总工程师id
     * @return  boolean
     */
    private boolean checkFlowUserId(List<List<ApprovalWorkflowParamsModel>> flows,String checkId,String chiefEngineerId) {
        boolean checkFlag = false;
        boolean chiefEngineerFlag = false;
        List<ApprovalWorkflowParamsModel> list = new ArrayList<>();
        for (List<ApprovalWorkflowParamsModel> flow : flows) {
            for (ApprovalWorkflowParamsModel approvalWorkflowParamsModel : flow) {
                if (StringUtils.equals(approvalWorkflowParamsModel.getUserId(),checkId)){
                    checkFlag = true;
                }
                if (StringUtils.equals(approvalWorkflowParamsModel.getUserId(),chiefEngineerId)){
                    chiefEngineerFlag = true;
                }

            }
        }
        return checkFlag&&chiefEngineerFlag;
    }

    /**
     * 添加验收信息
     *
     * @param instance  移交表实体
     * @return  是否添加成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addMessage(TransferDetectionDto instance,String drainId) {
        if (instance.getId() == null || instance.getId().isEmpty()) {
            instance.setId(UUID.randomUUID().toString());
        }
        TransferDetection model = mapper.map(instance, TransferDetection.class);
        DrainAccount drainAccount = drainAccountDao.getById(instance.getDrainId());
        TunnelDesign tunnelDesign = tunnelDesignDao.getById(drainAccount.getTunnelId() );
        CheckPlan checkPlan = checkPlanDao.getById(drainAccount.getCheckPlanId() );
        model.setWorkName(drainAccount.getWorkName());
        if (tunnelDesign.getTunnelAzimuth()==null) {
            model.setGalleryHoleAzimuth("");
        }
        model.setGalleryHoleAzimuth(tunnelDesign.getTunnelAzimuth().toString());
        model.setTransferDate(LocalDateTime.now().toDate());
        Date startTime = drainAccount.getSurveyWaterDate();
        Date endTime = drainAccount.getSurveyWaterDate();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(CpdsConstants.DATE_FORMAT);
        String start = simpleDateFormat.format(startTime);
        String end = simpleDateFormat.format(endTime);
        model.setProspectingDate(start+"/"+end);
        model.setClassNum(checkPlan.getClassNoName());
        model.setOverDistance(0.0);
        model.setCreateTime(LocalDateTime.now().toDate());
        model.setDelFlag(false);
        model.setIsViewed(false);
    //    model.setStatus(WorkFlowStatus.NoUploaded.getValue());
        boolean addSublist = updateAccount(instance.getDrainId());
        boolean update = drainAccountDao.updateSublist(instance.getDrainId(),false,false
        ,true,true,false,false);
        boolean updateDrainStatus=drainAccountDao.updateDrainStatus(instance.getDrainId());
        boolean updateStatus=holeDetailService.updateStatus(drainId);
        return addSublist && dao.addMessage(model) && update&&updateStatus&&updateDrainStatus;
    }

    /**
     * 修改数据
     *
     * @param instance 对象实例
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(TransferDetectionDto instance,List<List<ApprovalWorkflowParamsModel>> flows) {

        TransferDetection model = mapper.map(instance, TransferDetection.class);

        // 探放水日期处理

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(CpdsConstants.DATE_FORMAT_DAY);
        String prospectingDate = simpleDateFormat.format(instance.getProspectingDateStart()) +instance.getProspectingClassStart()+ "-" + simpleDateFormat.format(instance.getProspectingDateEnd())+instance.getProspectingClassEnd();
        model.setProspectingDate(prospectingDate);
        // 根据key值去寻找对应班次名称
      /*  if (model.getDrivedClassNumKey() != null) {
            String drivedClassNum = classNoManagerDao.getClassName(model.getOrgCode(), model.getDrivedClassNumKey())
                    .getClassNoName();
            model.setDrivedClassNum(drivedClassNum);
        }
        if (model.getSurplusClassNumKey() != null) {
            String surplusClassNum = classNoManagerDao.getClassName(model.getOrgCode(), model.getSurplusClassNumKey())
                    .getClassNoName();
            model.setSurplusClassNum(surplusClassNum);
        }*/
        model.setStatus(WorkFlowStatus.Approving.getValue());
        boolean updateModel=dao.update(model);
        if(!updateModel) {
            return false;
        }
        McMessageNormalDTO mcMessageNormalDTO = new McMessageNormalDTO();
        String title = model.getWorkName() + "(" + model.getCheckPosition() + ")" + "的探放水安全确认移交表需要审批";
        mcMessageNormalDTO.setTitle(title);
        mcMessageNormalDTO.setContent(title);
        mcMessageNormalDTO.setUrl("/wel/messageHandlingTransferDetection?id=" + model.getDrainId());
        mcMessageNormalDTO.setMobileUrl("/ts/drillingAnalysisDetail?id=" + model.getDrainId());

        if (!checkFlowUserId(flows,instance.getCheckId(),instance.getChiefEngineerId())) {
            throw new IllegalArgumentException("审批流程中必须有总工程师及防治水副总!");
        }
        JsonResponse jsonResponse=feign.saveApproving(TransferDetection.MODULE_NAME, model.getId(), mcMessageNormalDTO, flows);
        if(jsonResponse.getStatusCode()!=HttpStatus.OK.value()){
            throw new IllegalArgumentException(jsonResponse.getMessage());
        }
        return true;
    }

    /**
     * 更新验收信息
     * @param instance 对象实例
     * @return  是否更新成功
     */
    @Override
    public boolean updateMessage(TransferDetectionDto instance) {
        TransferDetection transferDetection = dao.getByDrainId(instance.getDrainId() , instance.getOrgCode());
        mapper.map(instance , transferDetection);
        return dao.updateMessage(transferDetection);
    }

    /**
     * 根据台账id修改数据
     *
     * @param instance 对象实例
     * @return 是否修改成功
     */
    @Override
    public boolean updateDrainId(TransferDetectionDto instance) {
        TransferDetection model = mapper.map(instance, TransferDetection.class);
        // 探放水日期处理
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(CpdsConstants.DATE_FORMAT_DAY);
        String prospectingDate = simpleDateFormat.format(instance.getProspectingDateStart()) +instance.getProspectingClassEnd()+ "-" + simpleDateFormat.format(instance.getProspectingDateEnd())+instance.getProspectingClassEnd();
        model.setProspectingDate(prospectingDate);
        // 根据key值去寻找对应班次名称
        if (model.getDrivedClassNumKey() != null) {
            String drivedClassNum = classNoManagerDao.getClassName(model.getOrgCode(), model.getDrivedClassNumKey())
                    .getClassNoName();
            model.setDrivedClassNum(drivedClassNum);
        }
        if (model.getSurplusClassNumKey() != null) {
            String surplusClassNum = classNoManagerDao.getClassName(model.getOrgCode(), model.getSurplusClassNumKey())
                    .getClassNoName();
            model.setSurplusClassNum(surplusClassNum);
        }
        return dao.updateDrainId(model);
    }

    /**
     * 删除数据
     *
     * @param id      数据Id
     * @param orgCode 机构编码
     * @return 是否删除成功
     */
    @Override
    public boolean delete(String id, String orgCode) {
        return dao.delete(id, orgCode);
    }

    /**
     * 根据台账删除数据
     *
     * @param drainId 数据drainId
     * @param orgCode 机构编码
     * @return 是否删除成功
     */
    @Override
    public boolean deleteDrainId(String drainId, String orgCode) {
        return dao.deleteDrainId(drainId, orgCode);
    }

    /**
     * 上报
     *
     * @param id         数据id
     * @param orgCode    机构编码
     * @param uploadTime 上报时间
     * @return 是否上报成功
     */
    @Override
    public boolean upload(String id, String orgCode, Date uploadTime) {
        TransferDetection intstance = new TransferDetection();
        intstance.setId(id);
        intstance.setOrgCode(orgCode);
        Date date = LocalDateTime.now().toDate();
        intstance.setUploadTime(date);
        return dao.upload(intstance);
    }

    /**
     * 监管单位已经查看
     *
     * @param id      数据Id
     * @param orgCode 机构编码
     * @return 是否更改为已查看状态
     */
    @Override
    public boolean check(String id, String orgCode) {
        return dao.check(id, orgCode);
    }

    /**
     * 判断数据是否存在
     *
     * @param id      数据Id
     * @param orgCode 机构编码
     * @return 数据是否存在
     */
    @Override
    public boolean anyById(String id, String orgCode) {
        return dao.anyById(id, orgCode);
    }

    /**
     * 判断台账id数据是否存在
     *
     * @param drainId 数据machineId
     * @param orgCode 机构编码
     * @return 数据是否存在
     */
    @Override
    public boolean anyByDrainId(String drainId, String orgCode) {
        return dao.anyByDrainId(drainId, orgCode);
    }

    /**
     * 根据设计Id获取数据
     *
     * @param orgCode  机构编码
     * @param tunnelId 探水设计Id
     * @return 查询结果
     */
    @Override
    public List<TransferDetectionDto> getByTunnelId(String orgCode, String tunnelId) {
        List<TransferDetection> list = dao.getByTunnelId(orgCode, tunnelId);
        return mapperUtils.mapList(list, TransferDetectionDto.class);
    }

    /**
     * 更新审批状态
     * @param id  主键id
     * @param orgCode  机构编码
     * @return
     */
    @Override
    public boolean updateStatus(String id, String orgCode,Integer status) {
        return dao.updateStatus(id, orgCode,status);
    }

    /**
     * 审批
     * @param model 参数对象
     * @param orgCode 机构编码
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approved(ApprovalReasonEditModel model,String orgCode) {
        JsonResponse jsonResponse = feign.approved(model);
        if (jsonResponse.getStatusCode()== HttpStatus.OK.value()){
            if ("approved".equals(jsonResponse.getMessage())) {
                //修改对应的状态
                boolean result= dao.updateStatus(model.getId(),orgCode,WorkFlowStatus.Approved.getValue());
                if(!result) {
                    throw new IllegalArgumentException(TfsMessageConstants.STATUS_UPDATE_ERROR);
                }
            }
              return true;
        } else{
            throw new IllegalArgumentException(jsonResponse.getMessage());
        }
    }

    /**
     * 提交审批
     * @param id 数据Id
     * @param orgCode 机构编码
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approving(String id,String orgCode) {
        //修改状态为审批中
        boolean result=dao.updateStatus(id,orgCode,WorkFlowStatus.Approving.getValue());
        if(!result) {
            throw new IllegalArgumentException(TfsMessageConstants.STATUS_UPDATE_ERROR);
        }
        JsonResponse jsonResponse=feign.approving(id);
        if(jsonResponse.getStatusCode()!=HttpStatus.OK.value()){
            throw new IllegalArgumentException(jsonResponse.getMessage());
        }
        return true;
    }

    /**
     * 审批拒绝
     * @param model 参数对象
     * @param orgCode 机构编码
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refused(ApprovalReasonEditModel model,String orgCode) {
        //修改状态为审批中
        boolean result=dao.updateStatus(model.getId(),orgCode,WorkFlowStatus.Refused.getValue());
        if(!result) {
            throw new IllegalArgumentException(TfsMessageConstants.STATUS_UPDATE_ERROR);
        }
        model.setStatus(WorkFlowStatus.Refused.toString());
        JsonResponse jsonResponse=feign.refuseOrBack(model);
        if(jsonResponse.getStatusCode()!=HttpStatus.OK.value()){
            throw new IllegalArgumentException(jsonResponse.getMessage());
        }
        return true;
    }

    /**
     * 撤回
     * @param model 参数对象
     * @param orgCode 机构编码
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean revoke(ApprovalReasonEditModel model,String orgCode) {
        //修改状态为审批中
        boolean result=dao.updateStatus(model.getId(),orgCode,WorkFlowStatus.Revoke.getValue());
        if(!result) {
            throw new IllegalArgumentException(TfsMessageConstants.STATUS_UPDATE_ERROR);
        }
        model.setStatus(WorkFlowStatus.Revoke.toString());
        JsonResponse jsonResponse=feign.refuseOrBack(model);
        if(jsonResponse.getStatusCode()!=HttpStatus.OK.value()){
            throw new IllegalArgumentException(jsonResponse.getMessage());
        }
        return true;
    }

    public List<String> splitString(String splitString) {
        List<String> splitStringList = new ArrayList<>();
        // 处理时间段
        int first = splitString.indexOf("-");
        if (first > 0) {
            String startData = splitString.substring(0, first);
            String endData = splitString.substring(first + 1);
            splitStringList.add(startData);
            splitStringList.add(endData);
        }
        return splitStringList;
    }
    private boolean updateAccount(String id) {
        return drainAccountDao.updateSublist(id, false, false, true, false, false, false);
    }
}
