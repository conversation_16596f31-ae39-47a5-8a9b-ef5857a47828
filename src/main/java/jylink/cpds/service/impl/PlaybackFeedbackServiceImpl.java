package jylink.cpds.service.impl;

import com.github.dozermapper.core.Mapper;
import com.github.pagehelper.page.PageMethod;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.dao.IHistoryVideoDao;
import jylink.cpds.dao.IPlaybackFeedbackDao;
import jylink.cpds.dao.IVideoListDao;
import jylink.cpds.domain.FullVideoList;
import jylink.cpds.domain.HistoryVideo;
import jylink.cpds.domain.PlaybackFeedback;
import jylink.cpds.domain.VideoList;
import jylink.cpds.service.IPlaybackFeedbackService;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.PagerParams;
import jylink.cpds.serviceModel.dto.PlaybackFeedbackDto;
import jylink.cpds.serviceModel.enumeration.PlaybackFeedbackTypeEnum;
import jylink.cpds.serviceModel.params.PlaybackFeedbackQueryModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 视频播放反馈表(PlaybackFeedback)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-24 18:35:21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PlaybackFeedbackServiceImpl implements IPlaybackFeedbackService {

    @Autowired
    private IVideoListDao videoListDao;

    @Autowired
    private IHistoryVideoDao historyVideoDao;

    @Autowired
    private IPlaybackFeedbackDao playbackFeedbackDao;

    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * dozer 帮助类
     */
    @Autowired
    private DozerUtils mapperUtils;

    /**
     * 通过ID查询单条数据
     *
     * @param id      主键
     * @param orgCode 机构编码
     * @return 实例对象
     */
    @Override
    public PlaybackFeedbackDto queryById(String id, String orgCode) {
        PlaybackFeedback playbackFeedback = playbackFeedbackDao.queryById(id, orgCode);
        if (playbackFeedback == null) {
            return null;
        }
        return mapper.map(playbackFeedback, PlaybackFeedbackDto.class);
    }

    /**
     * 通过ID确认数据是否存在
     *
     * @param id 主键
     * @return boolean
     */
    @Override
    public boolean anyById(String id, String orgCode) {
        return queryById(id, orgCode) != null;
    }

    /**
     * 数据查询
     *
     * @return
     */
    @Override
    public Pager<PlaybackFeedbackDto> getByPager(PagerParams<PlaybackFeedbackQueryModel> queryModel, String orgCode) {
        PageMethod.startPage(queryModel.getCurrentPage(), queryModel.getPageSize());
        PlaybackFeedback playbackFeedback = mapper.map(queryModel.getObjParams(), PlaybackFeedback.class);
        playbackFeedback.setOrgCode(orgCode);
        playbackFeedback.setDelFlag(false);
        List<PlaybackFeedback> playbackFeedbackLists = playbackFeedbackDao.queryAll(playbackFeedback);
        List<PlaybackFeedbackDto> listDto = mapperUtils.mapList(playbackFeedbackLists, PlaybackFeedbackDto.class);
        Pager<PlaybackFeedbackDto> pager = new Pager<>(playbackFeedbackDao.queryAllCount(playbackFeedback), listDto);
        return pager;
    }

    /**
     * 新增数据
     *
     * @param playbackFeedbackDto 实例对象
     * @return 实例对象
     */
    @Override
    public boolean insert(PlaybackFeedbackDto playbackFeedbackDto) {
        PlaybackFeedback playbackFeedback = mapper.map(playbackFeedbackDto, PlaybackFeedback.class);
        playbackFeedback.setCreateTime(new Date());
        playbackFeedback.setDelFlag(false);
        PlaybackFeedbackTypeEnum item = PlaybackFeedbackTypeEnum.getItem(playbackFeedback.getAbnormalType());
        if (Objects.isNull(item)) {
            throw new IllegalArgumentException("异常类型不存在");
        }
        playbackFeedback.setAbnormalTypeName(item.getValue());
        if (playbackFeedback.getVideoType()==0) {
            HistoryVideo historyVideo = historyVideoDao.getByHoleDetailId(playbackFeedback.getHoleDetailId());
            if (Objects.isNull(historyVideo)) {
                throw new IllegalArgumentException("视频不存在");
            }
            if (StringUtils.isNotBlank(historyVideo.getCsDrillFullVideo())) {
                playbackFeedback.setDataId(historyVideo.getId());
                playbackFeedback.setVideoNumber(1);
                playbackFeedback.setVideoUrl(historyVideo.getCsDrillFullVideo());
            } else {
                List<FullVideoList> fullVideoLists = historyVideoDao.selFullVideoByVideoId(historyVideo.getId());
                Optional<FullVideoList> first = fullVideoLists.stream().filter(f -> f.getId().equalsIgnoreCase(playbackFeedback.getDataId())).findFirst();
                if (!first.isPresent()) {
                    throw new IllegalArgumentException("视频不存在");
                }
                FullVideoList fullVideoList = first.get();
                playbackFeedback.setDataId(fullVideoList.getId());
                playbackFeedback.setVideoNumber(fullVideoList.getSort());
                playbackFeedback.setVideoUrl(fullVideoList.getVideoUrl());
            }
        } else {
            VideoList videoList = videoListDao.getById(playbackFeedback.getDataId());
            if (Objects.isNull(videoList)) {
                throw new IllegalArgumentException("杆视频数据不存在");
            }
            playbackFeedback.setHoleDetailId(videoList.getHoleDetailId());
            playbackFeedback.setVideoNumber(videoList.getSortOrder());
            playbackFeedback.setVideoUrl(videoList.getVideoUrl());
        }
        //判断id是否为空  id如果不为空则是从 文件上传接口调用过来的 , 不需要再使用uuid
        if (StringUtils.isBlank(playbackFeedbackDto.getId())) {
            playbackFeedback.setId(UUID.randomUUID().toString());
        }
        int res = playbackFeedbackDao.insert(playbackFeedback);
        return res == 1;
    }

    /**
     * 修改数据
     *
     * @param playbackFeedbackDto 实例对象
     * @return 实例对象
     */
    @Override
    public boolean update(PlaybackFeedbackDto playbackFeedbackDto) {
        PlaybackFeedback playbackFeedback = mapper.map(playbackFeedbackDto, PlaybackFeedback.class);
        int res = playbackFeedbackDao.update(playbackFeedback);
        return res == 1;
    }

    /**
     * 通过主键删除数据
     *
     * @param uuid 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.playbackFeedbackDao.deleteById(id) > 0;
    }
}
