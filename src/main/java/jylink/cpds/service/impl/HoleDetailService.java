package jylink.cpds.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.CpdsConstants;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.common.HttpClientUtils;
import jylink.cpds.config.MinioConfig;
import jylink.cpds.config.ObsProConfig;
import jylink.cpds.dao.*;
import jylink.cpds.domain.*;
import jylink.cpds.service.IAcceptanceCheckService;
import jylink.cpds.service.IHoleDetailService;
import jylink.cpds.serviceModel.AI.AIAnalysisResult;
import jylink.cpds.serviceModel.AI.AIAppendHoleParamsModel;
import jylink.cpds.serviceModel.AI.AnalysisResponse;
import jylink.cpds.serviceModel.AI.Drill;
import jylink.cpds.serviceModel.AbnormalType;
import jylink.cpds.serviceModel.HoleDetailStatus;
import jylink.cpds.serviceModel.MimeType;
import jylink.cpds.serviceModel.WorkFlowStatus;
import jylink.cpds.serviceModel.dto.*;
import jylink.cpds.serviceModel.gis.GisDrillPathDto;
import jylink.cpds.serviceModel.gis.GisHoleMessageDto;
import jylink.cpds.serviceModel.params.HoleDetailAcceptanceCheckModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class HoleDetailService implements IHoleDetailService {

    /**
     * obs配置服务
     */
    @Autowired
    private ObsProConfig obsProConfig;

    /**
     * 轨迹dao 服务
     */
    @Autowired
    private IDrillPathDao drillPathDao;

    /**
     * gis服务
     */
    @Autowired
    private IGISDao gisDao;

    /**
     * 杆汇报服务
     */
    @Autowired
    private IHoleDetailsReportDao holeDetailsReportDao;

    /**
     * dao服务
     */
    @Autowired
    private IHoleDetailDao dao;

    /**
     * 交接班 dao 服务
     */
    @Autowired
    private IChangeClassDao changeClassDao;

    /**
     * 工作量 dao 服务
     */
    @Autowired
    private IPersonWorkloadDao personWorkloadDao;

    /**
     * 交接班人员 dao 服务
     */
    @Autowired
    private IDrainAccountOperatorDao operatorDao;

    /**
     * 探水台账dao对象
     */
    @Autowired
    private IDrainAccountDao drainAccountDao;

    /**
     * 验收dao对象
     */
    @Autowired
    private IAcceptanceCheckDao acceptanceCheckDao;

    /**
     * 验收service对象
     */
    @Autowired
    private IAcceptanceCheckService acceptanceCheckService;

    /**
     * 计划子表dao对象
     */
    @Autowired
    private ICheckPlanDetailDao checkPlanDetailDao;

    /**
     * 移交表dao对象
     */
    @Autowired
    private ITransferDetectionDao transferDetectionDao;
    /**
     * dozer帮助类
     */
    @Autowired
    private DozerUtils mapperUtils;

    /**
     * dozer Mapper对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * 历史视频dao
     */
    @Autowired
    private IHistoryVideoDao historyVideoDao;

    /**
     * 小视频dao
     */
    @Autowired
    private IVideoListDao videoListDao;

    /**
     * 探水设计dao
     */
    @Autowired
    private ITunnelDesignDao tunnelDesignDao;

    /**
     * 字典表dao服务
     */
    @Autowired
    private IDictDao dictDao;

    @Autowired
    private IAnalysisHistoryWorkDao analysisHistoryWorkDao;
    /**
     * 班次dao服务
     */
    @Autowired
    private IClassNoManagerDao classNoManagerDao;

    /**
     * 分析dao服务
     */
    @Autowired
    private IAnalysisWorkDao analysisWorkDao;
    /**
     * Spring Boot 环境变量
     */
    @Autowired
    private Environment env;

    @Autowired
    private MinioConfig minioConfig;

    /**
     * 添加数据
     *
     * @param model 视频分析结果实体
     * @return 是否添加成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(HoleDetail model) {
        boolean addResult = false;
        if (aIAppendHole(model)) {
            HoleDetail holeDetailList = dao.getByHoleNo(model.getDrainId(), model.getHoleNo(), null);
            if (holeDetailList == null) {
                //没有该孔信息，插入数据
                model.setId(UUID.randomUUID().toString());
                model.setCreateTime(LocalDateTime.now().toDate());
                addResult = dao.add(model);
            } else {
                //有孔信息，更新所有孔信息
                addResult = dao.updateHole(model);
            }
            model.setHoleDistance(0.0);
            model.setStatus(WorkFlowStatus.NoAcceptance.getValue());
//            model.setId(UUID.randomUUID().toString());
            model.setCreateTime(LocalDateTime.now().toDate());
            AcceptanceCheck accModel = mapper.map(model, AcceptanceCheck.class);
            accModel.setId(UUID.randomUUID().toString());
            accModel.setUserId(model.getUsrId());
            accModel.setHoleDetailId(model.getId());
            addResult = acceptanceCheckService.addHoleDetail(accModel);
        }

        return addResult;
    }

    @Override
    public Double getTotalTaskPushDistance(List<String> ids) {
        return dao.getTotalTaskPushDistance(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addHoleDetail(HoleDetail holeDetail) {
        //获取最大的副孔号
        String viceHoleNo = holeDetail.getViceHoleNo();
        Integer newViceHoleNumber;
        if (viceHoleNo == null || viceHoleNo.isEmpty()) {
            newViceHoleNumber = 1;
        } else {
            newViceHoleNumber = Integer.valueOf(viceHoleNo) + 1;
        }
        //获取新的副孔号
        String newViceHoleNo = newViceHoleNumber.toString();
        HoleDetail detail = new HoleDetail();
        detail.setId(UUID.randomUUID().toString());
        detail.setOrgCode(holeDetail.getOrgCode());
        detail.setOrgName(holeDetail.getOrgName());
        detail.setDrainId(holeDetail.getDrainId());
        detail.setWorkCode(holeDetail.getWorkCode());
        detail.setWorkName(holeDetail.getWorkName());
        detail.setSurveyWaterMileage(holeDetail.getSurveyWaterMileage());
        detail.setHoleNo(holeDetail.getHoleNo());
        detail.setViceHoleNo(newViceHoleNo);
        detail.setHoleDistance(holeDetail.getHoleDistance());
        detail.setHoleObliquity(holeDetail.getHoleObliquity());
        detail.setHoleAzimuth(holeDetail.getHoleAzimuth());
        detail.setCasingLength(holeDetail.getCasingLength());
        detail.setHoleDiam(holeDetail.getHoleDiam());
        detail.setCasingDiam(holeDetail.getCasingDiam());
        detail.setStatus(HoleDetailStatus.NOT_STARTED.getValue());
        detail.setCreateTime(LocalDateTime.now().toDate());
        detail.setDelFlag(false);
        //修改验收表孔关联id
        AcceptanceCheck acceptanceCheck = acceptanceCheckDao.getByDrainIdAndHoleNo(holeDetail.getDrainId(), holeDetail.getHoleNo(), holeDetail.getOrgCode());
        acceptanceCheck.setHoleDetailId(detail.getId());
        return dao.add(detail) && acceptanceCheckDao.update(acceptanceCheck);
    }

    @Override
    public List<HoleDetailAcceptanceDto> getAcceptance(String drainId, String orgCode) {
        List<AcceptanceCheck> acceptanceChecks = acceptanceCheckDao.getByDrainId(drainId);
        List<String> collect = acceptanceChecks.stream().map(AcceptanceCheck::getHoleDetailId).collect(Collectors.toList());
        if (collect == null || collect.size() <= 0) {
            return new ArrayList<>();
        }
        List<HoleDetail> holeDetails = dao.getByIds(collect);
        List<HoleDetailAcceptanceDto> holeDetailAcceptanceDtos = new ArrayList<>();
        acceptanceChecks.forEach(acceptanceCheck -> {
            HoleDetailAcceptanceDto holeDetailAcceptanceDto = new HoleDetailAcceptanceDto();
            mapper.map(acceptanceCheck, holeDetailAcceptanceDto);
            holeDetails.stream().filter(h -> h.getId().equalsIgnoreCase(acceptanceCheck.getHoleDetailId())).findFirst().ifPresent(holeDetail -> {
                HoleDetailDto holeDetailDto = mapper.map(holeDetail, HoleDetailDto.class);
                holeDetailDto.setHoleStatus(acceptanceCheck.getDrillCondition());
                holeDetailAcceptanceDto.setDrillCondition(acceptanceCheck.getDrillCondition());
                holeDetailAcceptanceDto.setHoleDetailDto(holeDetailDto);
            });
            holeDetailAcceptanceDtos.add(holeDetailAcceptanceDto);
        });
        return holeDetailAcceptanceDtos;
    }

    public boolean aIAppendHole(HoleDetail holeDetail) {
        //发送post请求
        AIAppendHoleParamsModel aiModel = new AIAppendHoleParamsModel();
        aiModel.setHoleNo(holeDetail.getHoleNo());
        aiModel.setOrgCode(holeDetail.getOrgCode());
        aiModel.setSurveyWaterMileage(holeDetail.getSurveyWaterMileage());
        aiModel.setWorkCode(holeDetail.getWorkCode());
        String content = JSON.toJSONString(aiModel);
        Map<String, String> map = new HashMap<>();
        map.put(CpdsConstants.CONTENT_TYPE_KEY, CpdsConstants.APPLICATION_JSON);
        try {
            Dict dict = dictDao.getByGroupcode(CpdsConstants.AI_SERVICE_PATH_GROUP_CODE, holeDetail.getOrgCode()).get(0);
            String result = HttpClientUtils.sendPostDataByJson(dict.getDictValue() + "/appendHoleNumber", content, "utf8", map);
            AnalysisResponse<Object> analysisResult = JSON.parseObject(result, new TypeReference<AnalysisResponse<Object>>() {
            });
            if (!analysisResult.getMessage().equalsIgnoreCase("ok")) {
                log.warn("HoleDetailService--AIAppendHole--analysisResult获取信息不为ok");
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.warn("HoleDetailService--AIAppendHole方法执行失败！！");
            return false;
        }
        return true;
    }

    /**
     * 批量添加
     *
     * @param list 数据集合
     * @return 批量添加是否成功
     */
    @Override
    public boolean batchAdd(List<HoleDetail> list) {
        return dao.batchAdd(list);
    }

    /**
     * 查询数据是否存在
     *
     * @param orgCode 机构编码
     * @param id      数据Id
     * @return 查询结果
     */
    @Override
    public boolean anyById( String id) {
        return dao.anyById( id);
    }

    /**
     * 更改状态为作废
     *
     * @param id     数据Id
     * @param status 状态
     * @return 是否更改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancellation(String id, int status) {
        return dao.updateStatus(id, status);
    }

    /**
     * 更改状态为正常
     *
     * @param id
     * @param status
     * @return
     */
    @Override
    public boolean normal(String id, int status) {
        return dao.updateStatus(id, status);
    }

    /**
     * 根据工作面编码和探水里程查询数据信息
     *
     * @param orgCode            机构编码
     * @param workCode           工作面编码
     * @param surveyWaterMileage 探水里程
     * @return 查询结果列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<HoleDetailAcceptanceDto> getByWorkCodeAndSurveyWaterMileage(String orgCode, String workCode, String surveyWaterMileage) {
        //获取验收表的孔列表
        List<AcceptanceCheck> acceptanceChecks = acceptanceCheckDao.getCodeAndMileageAndStatus(orgCode, workCode, surveyWaterMileage);
        //对验收表的孔列表根据孔号排序
        List<AcceptanceCheck> checks = acceptanceChecks.stream().sorted(Comparator.comparing(AcceptanceCheck::getHoleNo)).collect(Collectors.toList());
        //取得孔列表对应的作业孔IDS
        List<String> holeDetialIds = checks.stream().map(AcceptanceCheck::getHoleDetailId).collect(Collectors.toList());
        //取得孔列表对应的最终作业列表
        List<HoleDetailAcceptanceDto> holeDetailAcceptanceList = new ArrayList<>();
        if (holeDetialIds.size() == 0) {
            return holeDetailAcceptanceList;
        }
        List<HoleDetail> holeDetails = dao.getByIds(holeDetialIds);
        checks.forEach(acceptanceCheck -> {
            HoleDetailAcceptanceDto holeDetailAcceptanceDto = new HoleDetailAcceptanceDto();
            mapper.map(acceptanceCheck, holeDetailAcceptanceDto);
            holeDetails.stream().filter(h -> h.getId().equals(acceptanceCheck.getHoleDetailId())).findFirst().ifPresent(detail -> {
                HoleDetailDto hoelDetailDto = mapper.map(detail, HoleDetailDto.class);
                holeDetailAcceptanceDto.setHoleDetailDto(hoelDetailDto);
            });
            holeDetailAcceptanceList.add(holeDetailAcceptanceDto);
        });
        return holeDetailAcceptanceList;
    }


    /**
     * @param acceptanceModel 孔验收实体
     * @return 是否验收成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean holeAcceptanceCheck(HoleDetailAcceptanceCheckModel acceptanceModel) {
        HoleDetail holeDetail = dao.getById(acceptanceModel.getHoleDetailId());
        if (holeDetail == null) {
            log.warn("HoleDetailService--holeAcceptanceCheck查询holeDetail表数据为空！！");
            return false;
        }
        DrainAccount drainAccount = drainAccountDao.getById(holeDetail.getDrainId());
        if (drainAccount == null) {
            log.warn("HoleDetailService--holeAcceptanceCheck查询drainAccount表数据为空！！");
            return false;
        }
        List<ClassNoManager> classNoManagers = classNoManagerDao.getByOrgCode(acceptanceModel.getOrgCode());
        AcceptanceCheck acceptanceCheck = new AcceptanceCheck();
        acceptanceCheck.setId(acceptanceModel.getAcceptanceId());
        acceptanceCheck.setHoleDistance(acceptanceModel.getConclusionHoleDistance());
        acceptanceCheck.setRemark(acceptanceModel.getConfirmOpinion());
        acceptanceCheck.setAnalysisHoleDistance(holeDetail.getAnalysisHoleDistance() == null ? 0 : holeDetail.getAnalysisHoleDistance());
        acceptanceCheck.setUserId(acceptanceModel.getUserId());
        acceptanceCheck.setUserName(acceptanceModel.getUserName());
        acceptanceCheck.setCheckTime(LocalDateTime.now().toDate());
        acceptanceCheck.setHoleDetailId(acceptanceModel.getHoleDetailId());
        acceptanceCheck.setOrgCode(acceptanceModel.getOrgCode());
        acceptanceCheck.setHoleAzimuth(holeDetail.getReportHoleAzimuth() == null ? holeDetail.getHoleAzimuth() : holeDetail.getReportHoleAzimuth());
        acceptanceCheck.setHoleObliquity(holeDetail.getReportHoleObliquity() == null ? holeDetail.getHoleObliquity() : holeDetail.getReportHoleObliquity());
        acceptanceCheck.setHoleStartTime(holeDetail.getStartDrillTime());
        acceptanceCheck.setHoleDiam(holeDetail.getHoleDiam());
        acceptanceCheck.setCasingDiam(holeDetail.getCasingDiam());
        acceptanceCheck.setCasingLength(holeDetail.getCasingLength());
        acceptanceCheck.setHoleClassNumKey(holeDetail.getReportClassNumKey());
        acceptanceCheck.setHoleClassNumValue(holeDetail.getReportClassNumValue());
        acceptanceCheck.setPrincipalName(holeDetail.getReportUserName());
        acceptanceCheck.setHoleOrgName(holeDetail.getWorkOrgName());
        acceptanceCheck.setHeight(holeDetail.getHeight() == null ? 0 : holeDetail.getHeight());
        acceptanceCheck.setHoleCondition(holeDetail.getHoleCondition());
        Calendar calendar = Calendar.getInstance();
        int curHourse = calendar.get(Calendar.HOUR_OF_DAY);
        classNoManagers.forEach(a -> {
            if (a.getClassEndTime() == 0 && a.getClassEndTime() < a.getClassStartTime()) {
                a.setClassEndTime(24);
            }
        });
        classNoManagers.stream().filter(a -> a.getClassStartTime() <= curHourse && a.getClassEndTime() > curHourse).findFirst().ifPresent(r -> {
            acceptanceCheck.setCheckClassNumKey(r.getClassKey());
            acceptanceCheck.setCheckClassNumValue(r.getClassNoName());
        });
        //施钻地点
        acceptanceCheck.setDrillHolePlace(drainAccount.getWorkName() + ":" + drainAccount.getSurveyWaterMileage() + "米处");

        return dao.holeAcceptanceCheck(acceptanceCheck);
    }

    @Override
    public HoleDetail getById(String id) {
        return dao.getById(id);
    }

    @Override
    public List<HoleDetailDto> getByDrainId(String drainId, String orgCode) {
        List<HoleDetail> holeDetail = dao.getByDrainId(drainId, orgCode);
        return mapperUtils.mapList(holeDetail, HoleDetailDto.class);
    }

    @Override
    public boolean updateConclusionHoleDistanceById(String id, Double conclusionHoleDistance) {
        return dao.updateConclusionHoleDistanceById(id, conclusionHoleDistance);
    }

    @Override
    public HoleDetail getByHoleNo(String drainId, String holeNo, String viceHoleNo) {
        return dao.getByHoleNo(drainId, holeNo, viceHoleNo);
    }

    /**
     * 根据验收id和钻孔详情id查询数据信息
     *
     * @param acceptanceId 验收id
     * @return 查询结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AcceptanceHoleDto getDetails(String acceptanceId) {
        //初如化返回对象
        AcceptanceHoleDto acceptanceHoleDto = new AcceptanceHoleDto();
        //验收表信息
        AcceptanceCheck acceptanceCheck = acceptanceCheckDao.getById(acceptanceId);
        //作业表信息
        HoleDetail holeDetail = dao.getById(acceptanceCheck.getHoleDetailId());
        if (holeDetail == null) {
            return acceptanceHoleDto;
        }
        DrainAccount account = drainAccountDao.getById(holeDetail.getDrainId());
        TunnelDesign tunnelDesign = tunnelDesignDao.getById(account.getTunnelId());
        acceptanceHoleDto.setWorkFaceId(tunnelDesign.getWorkFaceId());
        acceptanceHoleDto.setPosition(account.getSurveyWaterMileage());
        acceptanceHoleDto.setReportCasingLength(holeDetail.getReportCasingLength());
        acceptanceHoleDto.setUserId(acceptanceCheck.getUserId());
        acceptanceHoleDto.setApprovalId(acceptanceCheck.getApproveId());
        acceptanceHoleDto.setUserName(acceptanceCheck.getUserName());
        acceptanceHoleDto.setCheckTime(acceptanceCheck.getCheckTime());
        acceptanceHoleDto.setHoleDistance(acceptanceCheck.getHoleDistance());
        acceptanceHoleDto.setRemark(acceptanceCheck.getRemark());
        acceptanceHoleDto.setReportHoleAzimuth(holeDetail.getReportHoleAzimuth());
        acceptanceHoleDto.setReportHoleObliquity(holeDetail.getReportHoleObliquity());
        acceptanceHoleDto.setReportHoleDistance(holeDetail.getReportHoleDistance());
        acceptanceHoleDto.setHoleCondition(holeDetail.getHoleCondition());
        acceptanceHoleDto.setStartDrillTime(holeDetail.getStartDrillTime());
        acceptanceHoleDto.setEndDrillTime(holeDetail.getEndDrillTime());
        acceptanceHoleDto.setReportClassNumKey(holeDetail.getReportClassNumKey());
        acceptanceHoleDto.setReportClassNumValue(holeDetail.getReportClassNumValue());
        acceptanceHoleDto.setReportTime(holeDetail.getReportTime());
        acceptanceHoleDto.setReportUserId(holeDetail.getReportUserId());
        acceptanceHoleDto.setReportUserName(holeDetail.getReportUserName());
        acceptanceHoleDto.setWorkOrgId(holeDetail.getWorkOrgId());
        acceptanceHoleDto.setWorkOrgName(holeDetail.getWorkOrgName());
        acceptanceHoleDto.setDrillCondition(acceptanceCheck.getDrillCondition());
        //获取岩性分布的数据信息
        List<RockCharacterDto> dtos = getRockCharacter(holeDetail.getId(), holeDetail.getOrgCode(), acceptanceCheck);
        acceptanceHoleDto.setDtos(dtos);
        String abNormal = dtos.stream().filter(r -> r.getAbnormalType() != null).map(RockCharacterDto::getName).collect(Collectors.joining(","));
        //获取异常位置
        List<BigDecimal> collect = dtos.stream().filter(r -> r.getAbnormalType() != null).map(RockCharacterDto::getFrom).collect(Collectors.toList());
        String position = "";
        for (BigDecimal bigDecimal : collect) {
            if (collect != null && collect.size() > 0 && bigDecimal == collect.get(collect.size() - 1)) {
                position = position + bigDecimal + "m";
            } else {
                position = position + bigDecimal + "m;";
            }
        }
        acceptanceHoleDto.setAbnormalLocation(position);
        acceptanceHoleDto.setAbnormalType(abNormal);
        acceptanceHoleDto.setAnalysisHoleDistance(holeDetail.getAnalysisHoleDistance() == null ? 0 : holeDetail.getAnalysisHoleDistance());
        acceptanceHoleDto.setHeight(holeDetail.getHeight() == null ? 0 : holeDetail.getHeight());
        Integer holeState = holeDetail.getHoleStatus();
        acceptanceHoleDto.setHoleStatus(holeState == null ? "" : (holeState == 0 ? "正常" : "异常"));
        if (account.getCoalAngle() != null) {
            acceptanceHoleDto.setCoalAngle(account.getCoalAngle());
        }
        acceptanceHoleDto.setPlanHoleDistance(holeDetail.getHoleDistance());
        acceptanceHoleDto.setHoleAzimuth(holeDetail.getHoleAzimuth());
        acceptanceHoleDto.setHoleObliquity(holeDetail.getHoleObliquity());
        acceptanceHoleDto.setCasingDiam(holeDetail.getCasingDiam());
        acceptanceHoleDto.setCasingLength(holeDetail.getCasingLength());
        acceptanceHoleDto.setHoleDiam(holeDetail.getHoleDiam());
        String baseUrl = env.getProperty("bosUrl");
        if (minioConfig.isOpenFlag()) {
            baseUrl = minioConfig.getEndPoint() + "/" + minioConfig.getBucketName();
        }
        if (obsProConfig.isCloud() && obsProConfig.isSaveObs()) {
            acceptanceHoleDto.setHoleObliquityUrl((StringUtils.isBlank(holeDetail.getHoleObliquityUrl()) ? "" : baseUrl) + holeDetail.getHoleObliquityUrl());
            acceptanceHoleDto.setHoleAzimuthUrl((StringUtils.isBlank(holeDetail.getHoleAzimuthUrl()) ? "" : baseUrl) + holeDetail.getHoleAzimuthUrl());
        } else {
            acceptanceHoleDto.setHoleObliquityUrl(holeDetail.getHoleObliquityUrl());
            acceptanceHoleDto.setHoleAzimuthUrl(holeDetail.getHoleAzimuthUrl());
        }
        acceptanceHoleDto.setMimeType(MimeType.MP4);
        //获取交接班列表
        List<ChangeClassMessageDto> list = new ArrayList<>();
        List<PersonWorkload> personWorkloads = personWorkloadDao.getByHoleDetailId(holeDetail.getId(), holeDetail.getOrgCode());
        if (personWorkloads == null || personWorkloads.size() <= 0) {
            acceptanceHoleDto.setOperatorPerson(list);
        } else {
            //获取交接班人员信息
            List<String> ids = personWorkloads.stream().map(PersonWorkload::getChangeClassId).collect(Collectors.toList());
            List<ChangeClass> classes = changeClassDao.getByIds(ids);
            List<DrainAccountOperator> accountOperators = operatorDao.getByChangeClassIds(ids);
            classes.forEach(changeClass -> {
                String monitorName = accountOperators.stream().filter(o -> o.getChangeClassId().equalsIgnoreCase(changeClass.getId())).filter(o -> o.getPersonType() != null && o.getPersonType() == 0).map(DrainAccountOperator::getConstructionLeadingName).collect(Collectors.joining(","));
                String drillerName = accountOperators.stream().filter(o -> o.getChangeClassId().equalsIgnoreCase(changeClass.getId())).filter(o -> o.getPersonType() != null && o.getPersonType() == 1).map(DrainAccountOperator::getConstructionLeadingName).collect(Collectors.joining(","));
                ChangeClassMessageDto changeClassMessageDto = new ChangeClassMessageDto();
                changeClassMessageDto.setMonitorName(monitorName);
                changeClassMessageDto.setDrillerName(drillerName);
                changeClassMessageDto.setClassName(changeClass.getClassName());
                changeClassMessageDto.setWorkOrgName(changeClass.getWorkOrgName());
                list.add(changeClassMessageDto);
            });
            acceptanceHoleDto.setOperatorPerson(list);
        }
        return acceptanceHoleDto;
    }

    @Override
    public boolean updateHoleDetails(HoleDetail holeDetail, List<HoleDetail> holeDetails) {
        return dao.updateHoleDetails(holeDetail, holeDetails);
    }

    /**
     * 自动识别结果
     *
     * @param acceptanceCheckId 验收孔ID
     * @return 查询结果
     */
    @Override
    public List<HoleDetailAcceptanceVideoDto> getAutoAnalysisList(String acceptanceCheckId) {
        List<HoleDetail> holeDetails = dao.getAutoAnalysisList(acceptanceCheckId);
        AcceptanceCheck acceptanceCheck = acceptanceCheckDao.getById(acceptanceCheckId);
        if (holeDetails != null && holeDetails.size() > 0) {
            List<RockCharacterDto> rockCharacterDtos = gisDao.getPoleAbnormal(holeDetails.get(0).getDrainId());
            List<String> ids = holeDetails.stream().map(HoleDetail::getId).collect(Collectors.toList());
            List<VideoList> videoLists = videoListDao.getByHoleDetailIds(ids);
            List<PersonWorkload> personWorkloads = personWorkloadDao.getByHoleDetailIds(ids);
            List<DrainAccountOperator> operatorList = null;
            List<ChangeClass> changeClasses = null;
            if (personWorkloads.size() > 0) {
                List<String> changeClassIds = personWorkloads.stream().map(PersonWorkload::getChangeClassId).collect(Collectors.toList());
                operatorList = operatorDao.getByChangeClassIds(changeClassIds);
                changeClasses = changeClassDao.getByIds(changeClassIds);
            }
            List<HoleDetailAcceptanceVideoDto> holeDetailAcceptanceVideoDtos = mapperUtils.mapList(holeDetails, HoleDetailAcceptanceVideoDto.class);
            List<ChangeClass> finalChangeClasses = changeClasses;
            List<DrainAccountOperator> finalOperatorList = operatorList;
            String host = Objects.equals(env.getProperty("isCloud"), "true") ? env.getProperty("bosUrl") : "";
            if (minioConfig.isOpenFlag()) {
                host = minioConfig.getEndPoint() + "/" + minioConfig.getBucketName();
            }
            for (HoleDetailAcceptanceVideoDto detail : holeDetailAcceptanceVideoDtos) {
                List<RockCharacterDto> characterDtos = rockCharacterDtos.stream().filter(rockCharacterDto -> rockCharacterDto.getHoleDetailId().equalsIgnoreCase(detail.getId())).collect(Collectors.toList());
                detail.setHoleAzimuthUrl(host + detail.getHoleAzimuthUrl());
                //获取岩性分布
                List<VideoList> lists = videoLists.stream().filter(v -> v.getHoleDetailId().equalsIgnoreCase(detail.getId())).collect(Collectors.toList());
                String abNormal = characterDtos.stream().filter(r -> r.getAbnormalType() != null).map(RockCharacterDto::getName).collect(Collectors.joining(","));
                //获取异常位置
                List<BigDecimal> collect = characterDtos.stream().filter(r -> r.getAbnormalType() != null).map(RockCharacterDto::getFrom).collect(Collectors.toList());
                String position = "";
                for (BigDecimal bigDecimal : collect) {
                    if (collect != null && collect.size() > 0 && bigDecimal.equals(collect.get(collect.size() - 1))) {
                        position = position + bigDecimal + "m";
                    } else {
                        position = position + bigDecimal + "m;";
                    }
                }
                detail.setAbnormalLocation(position);
                detail.setAbnormalType(abNormal);
                for (RockCharacterDto characterDto : characterDtos) {//获取结束时间
                    videoLists.stream().filter(videoList -> videoList.getSortOrder() == characterDto.getLastPoleNo()).findFirst().ifPresent(videoList -> {
                        characterDto.setEndTime(videoList.getEndTime());
                    });
                    Optional<VideoList> first = videoLists.stream().filter(v -> v.getSortOrder() == characterDto.getPoleNo()).findFirst();
                    if (first.isPresent()) {
                        VideoList videoList = first.get();
                        characterDto.setStartTime(videoList.getStartTime());
                        characterDto.setVideoUrl(host + videoList.getVideoUrl());
                    }
                }
                if (acceptanceCheck.getHoleDistance() > 0) {
                    List<RockCharacterDto> list = characterDtos.stream().filter(r -> r.getFrom().compareTo(BigDecimal.valueOf(acceptanceCheck.getHoleDistance())) < 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
                    Optional<RockCharacterDto> first = characterDtos.stream().filter(h -> h.getAbnormalType() != null).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).findFirst();
                    if (!list.isEmpty()) {
                        if (first.isPresent() && first.get().getTo().compareTo(BigDecimal.valueOf(acceptanceCheck.getHoleDistance())) > 0) {
                            List<RockCharacterDto> rockCharacterDtoList = characterDtos.stream().filter(r -> r.getFrom().compareTo(new BigDecimal(first.get().getFrom().toString())) <= 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
                            rockCharacterDtoList.sort(Comparator.comparing(RockCharacterDto::getTo));
                            detail.setRockCharacterDtos(rockCharacterDtoList);
                        } else {
                            list.get(0).setTo(BigDecimal.valueOf(acceptanceCheck.getHoleDistance()));
                            list.sort(Comparator.comparing(RockCharacterDto::getTo));
                            detail.setRockCharacterDtos(list);
                        }
                    }
                } else if (detail.getReportHoleDistance() != null) {
                    List<RockCharacterDto> list = characterDtos.stream().filter(r -> r.getFrom().compareTo(new BigDecimal(detail.getReportHoleDistance().toString())) < 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
                    Optional<RockCharacterDto> first = characterDtos.stream().filter(h -> h.getAbnormalType() != null).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).findFirst();
                    if (!list.isEmpty()) {
                        if (first.isPresent() && first.get().getTo().compareTo(new BigDecimal(detail.getReportHoleDistance().toString())) > 0) {
                            List<RockCharacterDto> rockCharacterDtoList = characterDtos.stream().filter(r -> r.getFrom().compareTo(new BigDecimal(first.get().getFrom().toString())) <= 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
                            rockCharacterDtoList.sort(Comparator.comparing(RockCharacterDto::getTo));
                            detail.setRockCharacterDtos(rockCharacterDtoList);
                        } else {
                            list.get(0).setTo(new BigDecimal(detail.getReportHoleDistance().toString()));
                            list.sort(Comparator.comparing(RockCharacterDto::getTo));
                            detail.setRockCharacterDtos(list);
                        }
                    }
                }
                detail.setHoleObliquityUrl(host + detail.getHoleObliquityUrl());
                detail.setHoleNo(detail.getHoleNo() + (StringUtils.isBlank(detail.getViceHoleNo()) ? "" : ("-" + detail.getViceHoleNo())));
                detail.setStartAnalysisTime(detail.getStartDrillTime());
                detail.setEndAnalysisTime(detail.getEndDrillTime());
                detail.setNotes(detail.getStatus() == HoleDetailStatus.NORMAL.getValue() ? HoleDetailStatus.NORMAL.getInterpretation() : (detail.getStatus() == HoleDetailStatus.CANCELLATION.getValue() ? HoleDetailStatus.CANCELLATION.getInterpretation() : ""));
                String isNormal;
                if (detail.getWorkStatus() != null && detail.getWorkStatus() == 3) {
                    isNormal = "正常";
                } else if (detail.getWorkStatus() != null && detail.getWorkStatus() == 4) {
                    isNormal = "作废";
                } else {
                    isNormal = " ";
                }
                detail.setIsNormal(isNormal);
                //获取交接班列表
                List<ChangeClassMessageDto> operatorPerson = new ArrayList<>();
                if (personWorkloads == null || personWorkloads.isEmpty()) {
                    detail.setOperatorPerson(operatorPerson);
                } else {
                    //获取交接班人员信息
                    List<PersonWorkload> workloads = personWorkloads.stream().filter(p -> p.getHoleDetailId().equalsIgnoreCase(detail.getId())).collect(Collectors.toList());
                    workloads.forEach(w -> {
                        if (finalChangeClasses != null && finalOperatorList != null) {
                            Optional<ChangeClass> first = finalChangeClasses.stream().filter(f -> f.getId().equalsIgnoreCase(w.getChangeClassId())).findFirst();
                            if (first.isPresent()) {
                                ChangeClass changeClass = first.get();
                                String monitorName = finalOperatorList.stream().filter(o -> changeClass.getId().equalsIgnoreCase(o.getChangeClassId())).filter(o -> o.getPersonType() != null && 0 == o.getPersonType()).map(DrainAccountOperator::getConstructionLeadingName).collect(Collectors.joining(","));
                                String drillerName = finalOperatorList.stream().filter(o -> o.getChangeClassId().equalsIgnoreCase(changeClass.getId())).filter(o -> o.getPersonType() != null && 1 == o.getPersonType()).map(DrainAccountOperator::getConstructionLeadingName).collect(Collectors.joining(","));
                                ChangeClassMessageDto list = new ChangeClassMessageDto();
                                list.setMonitorName(monitorName);
                                list.setDrillerName(drillerName);
                                list.setClassName(changeClass.getClassName());
                                list.setWorkOrgName(changeClass.getWorkOrgName());
                                operatorPerson.add(list);
                            }
                        }
                    });
                    detail.setOperatorPerson(operatorPerson);
                }
            }
            return holeDetailAcceptanceVideoDtos;
        }
        return new ArrayList<>();
    }

    /**
     * 人工辅助识别结果
     *
     * @param acceptanceCheckId 验收孔ID
     * @return 查询结果
     */
    @Override
    public List<HoleDetailAcceptanceVideoDto> getArtificialAnalysisList(String acceptanceCheckId) {
        List<HoleDetail> holeDetails = dao.getArtificialAnalysisList(acceptanceCheckId);
        List<HoleDetailAcceptanceVideoDto> holeDetailAcceptanceVideoDtos = mapperUtils.mapList(holeDetails, HoleDetailAcceptanceVideoDto.class);
        if (holeDetailAcceptanceVideoDtos.size() > 0) {
            List<String> ids = holeDetailAcceptanceVideoDtos.stream().map(HoleDetailAcceptanceVideoDto::getId).collect(Collectors.toList());
            List<AnalysisHistoryWork> analysisHistoryWorks = analysisHistoryWorkDao.getByHoleDetailIds(ids);
            holeDetailAcceptanceVideoDtos.forEach(detail -> {
                detail.setHoleNo(detail.getHoleNo() + (StringUtils.isBlank(detail.getViceHoleNo()) ? "" : ("-" + detail.getViceHoleNo())));
                detail.setStartAnalysisTime(detail.getStartDrillTime());
                detail.setEndAnalysisTime(detail.getEndDrillTime());
                //找到对应的历史识别任务取作业时间
                analysisHistoryWorks.stream().filter(p -> p.getHoleDetailId().equals(detail.getId())).findFirst().ifPresent(analysisHistoryWork -> {
                    detail.setStartDrillTime(analysisHistoryWork.getStartTime());
                    detail.setEndDrillTime(analysisHistoryWork.getEndTime());
                });
                detail.setNotes(detail.getStatus() == HoleDetailStatus.NORMAL.getValue() ? HoleDetailStatus.NORMAL.getInterpretation() : (detail.getStatus() == HoleDetailStatus.CANCELLATION.getValue() ? HoleDetailStatus.CANCELLATION.getInterpretation() : ""));
                if (detail.getWorkStatus() != null && detail.getWorkStatus() == 3) {
                    detail.setIsNormal("正常");
                } else if (detail.getWorkStatus() != null && detail.getWorkStatus() == 4) {
                    detail.setIsNormal("作废");
                } else {
                    detail.setIsNormal("");
                }
            });
        }
        return holeDetailAcceptanceVideoDtos;
    }

    /**
     * 获取分析结果
     *
     * @param orgCode            机构编码
     * @param workCode           工作面编码
     * @param surveyWaterMileage 探水里程
     * @param holeNo             孔号
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setAnalysisResult(String orgCode, String workCode, Double surveyWaterMileage, String holeNo, String viceHoleNo) {
        // 查询台账数据
        Optional<DrainAccount> accountOptional = drainAccountDao.getByCodeAndMileage(orgCode, workCode, surveyWaterMileage).stream().findFirst();
        if (!accountOptional.isPresent()) {
            log.warn(MessageFormat.format("HoleDetailService--setAnalysisResult查询台账数据失败！！参数为：orgCode：{0},workCode:{1},surveyWaterMileage:{2}", orgCode, workCode, surveyWaterMileage));
            return false;
        }

        DrainAccount account = accountOptional.get();

        // 获取识别结果
        AIAnalysisResult analysisResult = acceptanceCheckService.getAIAnalysis(orgCode, workCode, surveyWaterMileage, holeNo, viceHoleNo);
        if (analysisResult == null) {
            log.warn(MessageFormat.format("HoleDetailService--setAnalysisResult获取识别结果失败！！参数为：orgCode：{0},workCode:{1},surveyWaterMileage:{2},holeNo:{3},viceHoleNo:{4}", orgCode, workCode, surveyWaterMileage, holeNo, viceHoleNo));
            return false;
        }


        // 计算并更新识别孔深
        double poleLength = tunnelDesignDao.getById(account.getTunnelId()).getPoleLength();
        analysisResult.getDrillings().forEach(drilling -> {
            String viceHoleNumber = StringUtils.isEmpty(drilling.getCodeAttach()) ? "" : drilling.getCodeAttach();
            HoleDetail holeDetail = dao.getByHoleNo(account.getId(), holeNo, viceHoleNumber);
            //先删除两表，在插入，直接删除
            videoListDao.deleteByHoleDetailId(holeDetail.getId());
            historyVideoDao.deleteByHoleDetailId(holeDetail.getId());

            //更新识别孔深
            double analysisDistance = poleLength * drilling.getDrills().size();
            BigDecimal bg = BigDecimal.valueOf(analysisDistance).setScale(2, RoundingMode.UP);
            dao.updateAnalysisResult(bg.doubleValue(), account.getId(), holeNo, viceHoleNo);

            //保存视频数据
            HistoryVideo historyVideo = new HistoryVideo();
            historyVideo.setId(UUID.randomUUID().toString());
            historyVideo.setHoleDetailId(holeDetail.getId());
            historyVideo.setCsDrillFullVideo(drilling.getFullVideoUrl());
            historyVideo.setOrgCode(orgCode);
            historyVideo.setOrgName(account.getOrgName());
            historyVideo.setDelFlag(false);
            historyVideo.setCreateTime(LocalDateTime.now().toDate());
            historyVideoDao.add(historyVideo);

            List<VideoList> videoLists = getVideoList(drilling.getDrills(), holeDetail.getId(), orgCode, account.getOrgName());
            if (!videoLists.isEmpty()) {
                videoListDao.batchAdd(videoLists);
            }
        });

        return true;
    }

    @Override
    public boolean addAnalysis(String orgCode, String workCode, Double surveyWaterMileage, String holeNo, String viceHoleNo) {
        Optional<DrainAccount> accountOptional = drainAccountDao.getByCodeAndMileage(orgCode, workCode, surveyWaterMileage).stream().findFirst();
        if (!accountOptional.isPresent()) {
            log.warn("HoleDetailService--addAnalysis查询台账数据失败！！参数为：orgCode：" + orgCode + ",workCode:" + workCode
                    + ",surveyWaterMileage:" + surveyWaterMileage);
            return false;
        }
        DrainAccount account = accountOptional.get();
        String viceHole = StringUtils.isEmpty(viceHoleNo) ? "" : viceHoleNo;
        HoleDetail holeDetail = dao.getByHoleNo(account.getId(), holeNo, viceHole);
        if (holeDetail == null) {
            holeDetail = dao.getByHoleNo(account.getId(), holeNo, "");
            if (holeDetail != null) {
                HoleDetail detail = new HoleDetail();
                detail.setId(UUID.randomUUID().toString());
                detail.setCreateTime(LocalDateTime.now().toDate());
                detail.setViceHoleNo(viceHole);
                detail.setDelFlag(false);
                detail.setOrgCode(holeDetail.getOrgCode());
                detail.setOrgName(holeDetail.getOrgName());
                detail.setDrainId(holeDetail.getDrainId());
                detail.setWorkCode(holeDetail.getWorkCode());
                detail.setWorkName(holeDetail.getWorkName());
                detail.setSurveyWaterMileage(surveyWaterMileage);
                detail.setHoleNo(holeNo);
                detail.setHoleDistance(holeDetail.getHoleDistance());
                detail.setHoleAzimuth(holeDetail.getHoleAzimuth());
                detail.setHoleObliquity(holeDetail.getHoleObliquity());
                detail.setStatus(HoleDetailStatus.NOT_STARTED.getValue());
                dao.add(detail);
            } else {
                HoleDetail detail = new HoleDetail();
                detail.setId(UUID.randomUUID().toString());
                detail.setCreateTime(LocalDateTime.now().toDate());
                detail.setViceHoleNo(viceHole);
                detail.setDelFlag(false);
                detail.setOrgCode(orgCode);
                detail.setOrgName(account.getOrgName());
                detail.setWorkCode(account.getWorkCode());
                detail.setWorkName(account.getWorkName());
                detail.setDrainId(account.getId());
                detail.setSurveyWaterMileage(surveyWaterMileage);
                detail.setHoleNo(holeNo);
                detail.setStatus(HoleDetailStatus.NOT_STARTED.getValue());
                dao.add(detail);
            }
        }
        return true;
    }

    /**
     * 根据分析参数查询数据
     *
     * @param orgCode            机构编码
     * @param workCode           工作面编码
     * @param surveyWaterMileage 探水里程
     * @param holeNo             孔号
     * @return 查询结果
     */
    @Override
    public HoleDetailDto getByAnalysisParams(String orgCode, String workCode, Double surveyWaterMileage, String holeNo, String holeNoAttach) {
        List<HoleDetail> holeDetails = dao.getByWorkCodeAndSurveyWaterMileage(orgCode, workCode, surveyWaterMileage, holeNo, holeNoAttach);
        return holeDetails.size() > 0 ? mapper.map(holeDetails.get(0), HoleDetailDto.class) : null;
    }

    /**
     * 更新开始时间
     *
     * @param orgCode            机构编码
     * @param workCode           工作面编码
     * @param surveyWaterMileage 探水里程
     * @param holeNo             孔号
     * @param viceHoleNo         副孔号
     * @param date               时间
     * @return 是否更新成功
     */
    @Override
    public boolean updateStartTime(String orgCode, String workCode, Double surveyWaterMileage, String holeNo, String viceHoleNo, Date date) {
        return dao.updateStartTime(orgCode, workCode, surveyWaterMileage, holeNo, StringUtils.isEmpty(viceHoleNo) ? null : viceHoleNo, date);
    }

    /**
     * 更新结束时间
     *
     * @param orgCode            机构编码
     * @param workCode           工作面编码
     * @param surveyWaterMileage 探水里程
     * @param holeNo             孔号
     * @param viceHoleNo         副孔号
     * @param date               时间
     * @return 是否更新成功
     */
    @Override
    public boolean updateEndTime(String orgCode, String workCode, Double surveyWaterMileage, String holeNo, String viceHoleNo, Date date) {
        return dao.updateEndTime(orgCode, workCode, surveyWaterMileage, holeNo, StringUtils.isEmpty(viceHoleNo) ? null : viceHoleNo, date);
    }

    /**
     * 根据主键id删除数据信息
     *
     * @param id      主键id
     * @param orgCode 机构编码
     * @return 删除结果
     */
    @Override
    public boolean deleteById(String id, String orgCode) {
        return dao.deleteById(id, orgCode);
    }

    /**
     * 根据台账id更新数据状态
     *
     * @param drainId 台账id
     * @return 更新结果
     */
    @Override
    public boolean updateStatus(String drainId) {
        return acceptanceCheckDao.updateStatus(drainId);
    }

    @Override
    public AnalysisWorkDto getStatus(String drainId) {
        List<AnalysisWork> analysisWorks = analysisWorkDao.getByDrainId(drainId);
        if (analysisWorks.size() <= 0) {
            return null;
        } else {
            Optional<AnalysisWork> analysisWorkOptional = analysisWorks.stream().sorted(Comparator.comparing(AnalysisWork::getCreateTime).reversed()).findFirst();
            if (analysisWorkOptional.isPresent()){
                AnalysisWork analysisWork = analysisWorkOptional.get();
                return mapper.map(analysisWork, AnalysisWorkDto.class);
            }
           return null;
        }
    }

    @Override
    public List<HoleDetailDto> getByDrainIdAndHoleNo(String orgCode, String drainId, String holeNo) {
        List<HoleDetail> holeDetails = dao.getByDrainIdAndHoleNo(orgCode, drainId, holeNo);
        return mapperUtils.mapList(holeDetails, HoleDetailDto.class);
    }

    /**
     * 根据Id更新识别中的分析孔深
     *
     * @param id                   主键Id
     * @param analysisHoleDistance 分析孔深
     * @return
     */
    @Override
    public boolean updateAnalysisHoleDistance(String id, double analysisHoleDistance) {
        return dao.updateAnalysisHoleDistance(id, analysisHoleDistance);
    }

    @Override
    public List<HoleDetail> getByDrainIdAndStatus(String orgCode, String drainId,
                                                  HoleDetailStatus status) {
        return dao.getByDrainIdAndStatus(orgCode, drainId, HoleDetailStatus.IN_PROGRESS.getValue());
    }

    /**
     * 根据台账id查询条形图数据
     *
     * @param drainId 台账id
     * @param orgCode 机构编码
     * @return
     */
    @Override
    public List<HoleNoAndPoleDto> getPoleAndTimes(String drainId, String orgCode) {
        List<AcceptanceCheck> acceptanceChecks = acceptanceCheckDao.getByDrainId(drainId);
        DrainAccount drainAccount = drainAccountDao.getById(drainId);
        double poleLength = tunnelDesignDao.getById(drainAccount.getTunnelId()).getPoleLength();
        List<String> holeDetailIds = acceptanceChecks.stream().map(AcceptanceCheck::getHoleDetailId).collect(Collectors.toList());
        List<VideoList> videoLists = videoListDao.getByHoleDetailIds(holeDetailIds);
        List<HoleNoAndPoleDto> holeNoAndPoleDtos = new ArrayList<>();
        List<HoleDetail> holeDetails = dao.getByIds(holeDetailIds);
        acceptanceChecks.forEach(acceptanceCheck -> {
            HoleNoAndPoleDto holeNoAndPoleDto = new HoleNoAndPoleDto();
            holeNoAndPoleDto.setAcceptanceId(acceptanceCheck.getId());
            holeNoAndPoleDto.setRemark(acceptanceCheck.getRemark());
            holeNoAndPoleDto.setConclusionHoleDistance(acceptanceCheck.getHoleDistance());
            HoleDetail holeDetail = holeDetails.stream().filter(h -> h.getId().equalsIgnoreCase(acceptanceCheck.getHoleDetailId())).findFirst().get();
            holeNoAndPoleDto.setHoleNo(acceptanceCheck.getHoleNo());
            List<VideoList> lists = videoLists.stream().filter(v -> v.getHoleDetailId().equals(holeDetail.getId())).collect(Collectors.toList());
            PoleDto poleDto = new PoleDto();
            List<Integer> times = new ArrayList<>();
            Map<Integer, Object> map = new HashMap<>();
            log.info(acceptanceCheck.getHoleNo());
            log.info(holeDetail.getViceHoleNo());
            //获取杆的开始时间和结束时间，杆长
            lists.forEach(videoList -> {
                BigDecimal bigDecimal = BigDecimal.valueOf(videoList.getSortOrder() * poleLength);
                double poleLengths = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                map.put(videoList.getSortOrder(), poleLengths);
                long time = 0;
                if (videoList.getEndTime() != null && videoList.getStartTime() != null) {
                    time = videoList.getEndTime().getTime() - videoList.getStartTime().getTime();
                }
                times.add(Integer.parseInt(String.valueOf(time / 1000)));
            });
            poleDto.setSortOrders(map);
            poleDto.setTimes(times);
            holeNoAndPoleDto.setPoleDto(poleDto);
            holeNoAndPoleDto.setHoleDistance(holeDetail.getHoleDistance());
            holeNoAndPoleDto.setAnalysisHoleDistance(holeDetail.getAnalysisHoleDistance());
            holeNoAndPoleDto.setReportHoleDistance(holeDetail.getReportHoleDistance());
            holeNoAndPoleDto.setHoleCondition(holeDetail.getHoleCondition());
            List<String> systemAnalysisList = new ArrayList<>(times.size());
            //系统分析
            List<Integer> sortTimes = new ArrayList<>();
            sortTimes.addAll(times);
            if (sortTimes.size() >= 5) {
                Integer devTime = 0;  //平均时间
                Integer addTime = 0;
                Collections.sort(sortTimes);
                for (int i = 1; i < sortTimes.size() - 1; i++) {
                    Integer intTime = sortTimes.get(i);
                    addTime += intTime;
                }
                devTime = addTime / (sortTimes.size() - 2);
                holeNoAndPoleDto.setDevTime(devTime);

                //获取钻探时间异常范围
                String groupCode = "abnormal_range_drill_time";
                List<Dict> byGroupcode = dictDao.getByGroupcode(groupCode, orgCode);
                //设置水平角偏差，以及垂直角偏差
                int min = 0;
                int max = 0;
                if (byGroupcode.size() > 0) {
                    for (Dict dict : byGroupcode) {
                        if ("max".equals(dict.getDictKey())) {
                            max = Integer.parseInt(dict.getDictValue());
                        } else if ("min".equals(dict.getDictKey())) {
                            min = Integer.parseInt(dict.getDictValue());
                        }
                    }
                } else {
                    max = 100;
                    min = 0;
                }

                Double maxTime = devTime + devTime * max * 0.01;
                Double minTime = devTime * min * 0.01;
                holeNoAndPoleDto.setMaxTime(Integer.valueOf(maxTime.intValue()));
                holeNoAndPoleDto.setMinTime(Integer.valueOf(minTime.intValue()));

                for (int i = 0; i < times.size(); i++) {
                    if (devTime != 0) {
                        Integer intTime = times.get(i);
                        BigDecimal bigDecimal = BigDecimal.valueOf((double) intTime / (double) devTime * 100);
                        double analysisTime = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        if (analysisTime > max + 100 || analysisTime < min) {
                            String systemAnalysis = i + 1 + "号钻杆的钻进时间为平均时间" + devTime + "秒的" + analysisTime + "%,疑似异常，请核实！";
                            systemAnalysisList.add(systemAnalysis);
                        }
                    }
                }
            } else if ((sortTimes.size() > 0 && sortTimes.size() < 5)) {
                Integer devTime = 0;  //平均时间
                Integer addTime = 0;
                Collections.sort(sortTimes);
                for (int i = 0; i < sortTimes.size(); i++) {
                    Integer intTime = sortTimes.get(i);
                    addTime += intTime;
                }
                devTime = addTime / (sortTimes.size());
                holeNoAndPoleDto.setDevTime(devTime);
                //获取钻探时间异常范围
                String groupCode = "abnormal_range_drill_time";
                List<Dict> byGroupcode = dictDao.getByGroupcode(groupCode, orgCode);
                //设置水平角偏差，以及垂直角偏差
                int min = 0;
                int max = 0;
                if (byGroupcode.size() > 0) {
                    for (Dict dict : byGroupcode) {
                        if ("max".equals(dict.getDictKey())) {
                            max = Integer.parseInt(dict.getDictValue());
                        } else if ("min".equals(dict.getDictKey())) {
                            min = Integer.parseInt(dict.getDictValue());
                        }
                    }
                } else {
                    max = 100;
                    min = 0;
                }

                Double maxTime = devTime + devTime * max * 0.01;
                Double minTime = devTime * min * 0.01;
                holeNoAndPoleDto.setMaxTime(Integer.valueOf(maxTime.intValue()));
                holeNoAndPoleDto.setMinTime(Integer.valueOf(minTime.intValue()));
                for (int i = 0; i < times.size(); i++) {
                    if (devTime != 0) {
                        Integer intTime = times.get(i);
                        BigDecimal bigDecimal = BigDecimal.valueOf((double) intTime / (double) devTime * 100);
                        double analysisTime = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        if (analysisTime > max + 100 || analysisTime < min) {
                            String systemAnalysis = i + 1 + "号钻杆的钻进时间为平均时间" + devTime + "秒的" + analysisTime + "%,疑似异常，请核实！";
                            systemAnalysisList.add(systemAnalysis);
                        }
                    }
                }
            }
            holeNoAndPoleDto.setSystemAnalysis(systemAnalysisList);
            if (holeDetail.getHoleStatus() == null) {
                holeNoAndPoleDto.setHoleStatus("");
            } else {
                if (holeDetail.getHoleStatus() == 0) {
                    holeNoAndPoleDto.setHoleStatus("正常");
                } else if (holeDetail.getHoleStatus() == 1) {
                    holeNoAndPoleDto.setHoleStatus("异常");
                } else {
                    holeNoAndPoleDto.setHoleStatus("");
                }
            }
            holeNoAndPoleDtos.add(holeNoAndPoleDto);
        });
        return holeNoAndPoleDtos;
    }

    @Override
    public HoleNoAndPoleDto getHolePoleAndTimes(String holeDetailId) {
        HoleNoAndPoleDto holeNoAndPoleDto = new HoleNoAndPoleDto();
        List<VideoList> videoLists = videoListDao.getByHoleDetailId(holeDetailId);
        List<Integer> times = new ArrayList<>();
        Map<Integer, Object> map = new HashMap<>();
        HoleDetail holeDetail = dao.getById(holeDetailId);
        if (Objects.isNull(holeDetail)) {
            return holeNoAndPoleDto;
        }
        DrainAccount drainAccount = drainAccountDao.getById(holeDetail.getDrainId());
        double poleLength = tunnelDesignDao.getById(drainAccount.getTunnelId()).getPoleLength();
        PoleDto poleDto = new PoleDto();
        //获取杆的开始时间和结束时间，杆长
        videoLists.forEach(videoList -> {
            BigDecimal bigDecimal = BigDecimal.valueOf(videoList.getSortOrder() * poleLength);
            double poleLengths = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            map.put(videoList.getSortOrder(), poleLengths);
            long time = 0;
            if (videoList.getEndTime() != null && videoList.getStartTime() != null) {
                time = videoList.getEndTime().getTime() - videoList.getStartTime().getTime();
            }
            times.add(Integer.parseInt(String.valueOf(time / 1000)));
        });
        poleDto.setSortOrders(map);
        poleDto.setTimes(times);
        holeNoAndPoleDto.setPoleDto(poleDto);
        List<Integer> sortTimes = new ArrayList<>();
        sortTimes.addAll(times);
        if (sortTimes.size() >= 5) {
            Integer devTime = 0;  //平均时间
            Integer addTime = 0;
            Collections.sort(sortTimes);
            for (int i = 1; i < sortTimes.size() - 1; i++) {
                Integer intTime = sortTimes.get(i);
                addTime += intTime;
            }
            devTime = addTime / (sortTimes.size() - 2);
            holeNoAndPoleDto.setDevTime(devTime);

            //获取钻探时间异常范围
            String groupCode = "abnormal_range_drill_time";
            List<Dict> byGroupcode = dictDao.getByGroupcode(groupCode, holeDetail.getOrgCode());
            //设置水平角偏差，以及垂直角偏差
            int min = 0;
            int max = 0;
            if (byGroupcode.size() > 0) {
                for (Dict dict : byGroupcode) {
                    if ("max".equals(dict.getDictKey())) {
                        max = Integer.parseInt(dict.getDictValue());
                    } else if ("min".equals(dict.getDictKey())) {
                        min = Integer.parseInt(dict.getDictValue());
                    }
                }
            } else {
                max = 100;
                min = 0;
            }

            Double maxTime = devTime + devTime * max * 0.01;
            Double minTime = devTime * min * 0.01;
            holeNoAndPoleDto.setMaxTime(maxTime.intValue());
            holeNoAndPoleDto.setMinTime(minTime.intValue());
        } else if ((sortTimes.size() > 0 && sortTimes.size() < 5)) {
            Integer devTime = 0;  //平均时间
            Integer addTime = 0;
            Collections.sort(sortTimes);
            for (int i = 0; i < sortTimes.size(); i++) {
                Integer intTime = sortTimes.get(i);
                addTime += intTime;
            }
            devTime = addTime / (sortTimes.size());
            holeNoAndPoleDto.setDevTime(devTime);
            //获取钻探时间异常范围
            String groupCode = "abnormal_range_drill_time";
            List<Dict> byGroupcode = dictDao.getByGroupcode(groupCode, holeDetail.getOrgCode());
            //设置水平角偏差，以及垂直角偏差
            int min = 0;
            int max = 0;
            if (byGroupcode.size() > 0) {
                for (Dict dict : byGroupcode) {
                    if ("max".equals(dict.getDictKey())) {
                        max = Integer.parseInt(dict.getDictValue());
                    } else if ("min".equals(dict.getDictKey())) {
                        min = Integer.parseInt(dict.getDictValue());
                    }
                }
            } else {
                max = 100;
                min = 0;
            }

            Double maxTime = devTime + devTime * max * 0.01;
            Double minTime = devTime * min * 0.01;
            holeNoAndPoleDto.setMaxTime(maxTime.intValue());
            holeNoAndPoleDto.setMinTime(minTime.intValue());
        }
        return holeNoAndPoleDto;
    }

    @Override
    public boolean updateStatus(String id, int status) {
        return dao.updateStatus(id, status);
    }

    @Override
    public CoalRockSegmentationDto getAbnormal(String acceptanceId, String orgCode) {
        AcceptanceCheck acceptanceCheck = acceptanceCheckDao.getById(acceptanceId);
        if (Objects.isNull(acceptanceCheck)) {
            return new CoalRockSegmentationDto();
        }
        CoalRockSegmentationDto coalRockSegmentationDto = dao.getAbNormal(acceptanceId);
        //获取岩性分类数据
        List<RockCharacterDto> characterDtos = holeDetailsReportDao.getRockCharacter(acceptanceCheck.getOrgCode(), coalRockSegmentationDto.getHoleDetailId());
        List<AbNormalLimitDto> limitDtos = new ArrayList<>();
        //判断设计孔深同杆汇报的最大孔深取小的作为最后的结束点
        List<RockCharacterDto> dtos = characterDtos.stream().filter(r -> r.getFrom().compareTo(new BigDecimal(coalRockSegmentationDto.getHoleDistance().toString())) < 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
        if (dtos.size() > 0) {
            dtos.get(0).setTo(new BigDecimal(coalRockSegmentationDto.getHoleDistance().toString()));
            dtos.sort(Comparator.comparing(RockCharacterDto::getTo));
            dtos.forEach(characterDto -> {
                AbNormalLimitDto dto = new AbNormalLimitDto();
                dto.setFrom(characterDto.getFrom());
                dto.setTo(characterDto.getTo());
                String title = StringUtils.isEmpty(characterDto.getReportUserName()) ? "" : (characterDto.getReportUserName() + "在" + characterDto.getReportTime() + "汇报：");
                //异常类型为空，显示见煤
                if (characterDto.getAbnormalType() == null) {
                    dto.setType(6);
                    dto.setTitle(title + characterDto.getFrom() + "-" + characterDto.getTo() + "米处见煤");
                    //异常类型为见顶底板或者建裂隙，异常类型显示见岩，标题显示各自的异常类型
                } else if (characterDto.getAbnormalType() == AbnormalType.SEE_ROOF_AND_FLOOR.getValue() || characterDto.getAbnormalType() == AbnormalType.FISSURE.getValue()) {
                    dto.setTitle(title + characterDto.getFrom() + "-" + characterDto.getTo() + "米处" + characterDto.getName());
                    dto.setType(AbnormalType.ROCK.getValue());
                } else {
                    dto.setType(characterDto.getAbnormalType());
                    dto.setTitle(title + characterDto.getFrom() + "-" + characterDto.getTo() + "米处" + characterDto.getName());
                }
                limitDtos.add(dto);
            });
            coalRockSegmentationDto.setAbNormalLimitDtos(limitDtos);
        }
        return coalRockSegmentationDto;
    }

    @Override
    public GisHoleMessageDto getTrack(String acceptanceId) {
        AcceptanceCheck acceptanceCheck = acceptanceCheckDao.getById(acceptanceId);
        HoleDetail holeDetail = dao.getById(acceptanceCheck.getHoleDetailId());
        List<DrillPath> drillPaths = drillPathDao.getByAcceptanceId(acceptanceCheck.getHoleDetailId(), holeDetail.getOrgCode());
        List<GisDrillPathDto> gisDrillPathDtos = mapperUtils.mapList(drillPaths, GisDrillPathDto.class);
        List<RockCharacterDto> rockCharacter = holeDetailsReportDao.getRockCharacter(holeDetail.getOrgCode(), acceptanceCheck.getHoleDetailId());
        List<HoleDetailsReport> holeDetailsReports = holeDetailsReportDao.getByHoleDetailId(acceptanceCheck.getHoleDetailId());
        GisHoleMessageDto gisHoleMessageDto = new GisHoleMessageDto();
        gisHoleMessageDto.setId(acceptanceId);
        gisHoleMessageDto.setHoleNo(acceptanceCheck.getHoleNo());
        gisHoleMessageDto.setHoleDetailId(acceptanceCheck.getHoleDetailId());
        gisHoleMessageDto.setAzimuth(acceptanceCheck.getHoleAzimuth());
        gisHoleMessageDto.setObliquity(acceptanceCheck.getHoleObliquity());
        gisHoleMessageDto.setHoleDistance(holeDetail.getHoleDistance());
        if (drillPaths != null && drillPaths.size() > 0) {
            BigDecimal deep = BigDecimal.valueOf(drillPaths.get(drillPaths.size() - 1).getDeep());
            List<RockCharacterDto> dtos = rockCharacter.stream().filter(r -> r.getFrom().compareTo(deep) <= 0).collect(Collectors.toList());
            if (dtos != null && dtos.size() > 0 && dtos.get(dtos.size() - 1).getTo().compareTo(deep) > 0) {
                    dtos.get(dtos.size() - 1).setTo(deep);
            }
            gisDrillPathDtos.forEach(dto -> {
                dtos.stream().filter(p -> dto.getSortOrder() >= p.getPoleNo() && dto.getSortOrder() <= p.getLastPoleNo()).findFirst().ifPresent(rockCharacterDto -> {
                    String title = StringUtils.isEmpty(rockCharacterDto.getReportUserName()) ? "" : (rockCharacterDto.getReportUserName() + "在" + rockCharacterDto.getReportTime() + "汇报：");
                    String position = rockCharacterDto.getFrom() + "-" + rockCharacterDto.getTo();
                    if (rockCharacterDto.getFrom().compareTo(rockCharacterDto.getTo()) == 0) {
                        position = rockCharacterDto.getFrom().toString();
                    }
                    if (rockCharacterDto.getAbnormalType() == null) {
                        dto.setTitle(title + position + "米处见煤");
                        //异常类型为见顶底板或者建裂隙，异常类型显示见岩，标题显示各自的异常类型
                    } else if (rockCharacterDto.getAbnormalType() == AbnormalType.SEE_ROOF_AND_FLOOR.getValue() || rockCharacterDto.getAbnormalType() == AbnormalType.FISSURE.getValue()) {
                        dto.setTitle(title + position + "米处" + rockCharacterDto.getName());
                    } else {
                        dto.setTitle(title + position + "米处" + rockCharacterDto.getName());
                    }
                });
                holeDetailsReports.stream().filter(holeDetailsReport -> holeDetailsReport.getHoleDetailId().equalsIgnoreCase(dto.getAcceptanceId())).filter(h -> h.getPoleNo().equals(dto.getSortOrder())).findFirst().ifPresent(holeDetailsReport -> {
                    if (holeDetailsReport.getAbnormalType() == null) {
                        dto.setType(AbnormalType.FISSURE.getValue());
                    } else if (holeDetailsReport.getAbnormalType() == AbnormalType.FISSURE.getValue() || holeDetailsReport.getAbnormalType() == AbnormalType.SEE_ROOF_AND_FLOOR.getValue()) {
                        dto.setType(AbnormalType.ROCK.getValue());
                    } else {
                        dto.setType(holeDetailsReport.getAbnormalType());
                    }
                });
                if (dto.getType() == null) {
                    dto.setType(AbnormalType.FISSURE.getValue());
                }
            });
            gisHoleMessageDto.setDrillPathDtos(gisDrillPathDtos);
        }
        return gisHoleMessageDto;
    }

    @Override
    public HoleDetailDto getAnalysisHole(String drainId, String orgCode) {
        HoleDetail holeDetail = dao.getAnalysisHole(drainId, orgCode);
        if (holeDetail == null) {
            return null;
        }
        return mapper.map(holeDetail, HoleDetailDto.class);
    }

    /**
     * 获取岩性划分数据
     *
     * @param holeDetailId 钻孔作业id
     * @param orgCode      机构编码
     * @return 查询结果
     */
    @Override
    public List<RockCharacterDto> getRockCharacter(String holeDetailId, String orgCode, Double checkHoleDistance) {
        List<RockCharacterDto> rockCharacterDtos = holeDetailsReportDao.getRockCharacter(orgCode, holeDetailId);
        List<VideoList> videoLists = videoListDao.getByHoleDetailId(holeDetailId);
        String host = obsProConfig.isSaveObs() && obsProConfig.isCloud() ? obsProConfig.getBosUrl() : "";
        rockCharacterDtos.forEach(list -> {
            videoLists.stream().filter(videoList -> videoList.getSortOrder() == list.getPoleNo()).findFirst().ifPresent(videoList -> {
                list.setVideoUrl(host + videoList.getVideoUrl());
                list.setStartTime(videoList.getStartTime());
            });
            videoLists.stream().filter(videoList -> videoList.getSortOrder() == list.getLastPoleNo()).findFirst().ifPresent(videoList -> {
                list.setEndTime(videoList.getEndTime());
            });
        });
        HoleDetail holeDetail = dao.getById(holeDetailId);
        //判断是否已经完成汇报，已完成，杆汇报的数据由汇报孔深决定
        if (holeDetail != null && checkHoleDistance > 0) {
            List<RockCharacterDto> lists = rockCharacterDtos.stream().filter(r -> r.getFrom().compareTo(BigDecimal.valueOf(checkHoleDistance)) < 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
            Optional<RockCharacterDto> first = rockCharacterDtos.stream().filter(h -> h.getAbnormalType() != null).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).findFirst();
            if (!lists.isEmpty()) {
                if (first.isPresent() && first.get().getTo().compareTo(BigDecimal.valueOf(checkHoleDistance)) > 0) {
                    List<RockCharacterDto> rockCharacterDtoList = rockCharacterDtos.stream().filter(r -> r.getFrom().compareTo(first.get().getFrom()) <= 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
                    rockCharacterDtoList.sort(Comparator.comparing(RockCharacterDto::getTo));
                    return rockCharacterDtoList;
                }
                lists.get(0).setTo(BigDecimal.valueOf(checkHoleDistance));
                lists.sort(Comparator.comparing(RockCharacterDto::getTo));
                return lists;
            }
        } else if (holeDetail != null && holeDetail.getReportHoleDistance() != null) {
            List<RockCharacterDto> list = rockCharacterDtos.stream().filter(r -> r.getFrom().compareTo(BigDecimal.valueOf(holeDetail.getReportHoleDistance())) < 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
            Optional<RockCharacterDto> first = rockCharacterDtos.stream().filter(h -> h.getAbnormalType() != null).max(Comparator.comparing(RockCharacterDto::getTo));
            if (!list.isEmpty()) {
                if (first.isPresent() && first.get().getTo().compareTo(BigDecimal.valueOf(holeDetail.getReportHoleDistance())) > 0) {
                    List<RockCharacterDto> rockCharacterDtoList = rockCharacterDtos.stream().filter(r -> r.getFrom().compareTo(first.get().getFrom()) <= 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
                    rockCharacterDtoList.sort(Comparator.comparing(RockCharacterDto::getTo));
                    return rockCharacterDtoList;
                }
                list.get(0).setTo(BigDecimal.valueOf(holeDetail.getReportHoleDistance()));
                list.sort(Comparator.comparing(RockCharacterDto::getTo));
                return list;
            }
        } else {
            return new ArrayList<>();
        }
        return new ArrayList<>();
    }

    /**
     * 获取小视频列表对象集合
     *
     * @param drills       AI返回结果
     * @param holeDetailId 孔表数据Id
     * @param orgCode      机构编码
     * @param orgName      机构名称
     * @return 创建的对象
     */
    private List<VideoList> getVideoList(List<Drill> drills, String holeDetailId, String orgCode, String orgName) {
        List<VideoList> videoLists = new ArrayList<>();
        drills.forEach(drill -> {
            VideoList videoList = new VideoList();
            videoList.setId(UUID.randomUUID().toString());
            videoList.setHoleDetailId(holeDetailId);
            videoList.setVideoUrl(drill.getVideoUrl());
            videoList.setSortOrder(drill.getSerialNumber());
            videoList.setStartTime(drill.getStartTime());
            videoList.setEndTime(drill.getEndTime());
            videoList.setCreateTime(LocalDateTime.now().toDate());
            videoList.setNumberOfPerson(drill.getNumberOfPerson());
            videoList.setOrgCode(orgCode);
            videoList.setOrgName(orgName);
            videoList.setDelFlag(false);
            videoList.setCreateTime(LocalDateTime.now().toDate());
            videoLists.add(videoList);
        });
        return videoLists;
    }

    /**
     * 获取岩性划分数据
     *
     * @param holeDetailId 钻孔作业id
     * @param orgCode      机构编码
     * @return 查询结果
     */
    private List<RockCharacterDto> getRockCharacter(String holeDetailId, String orgCode, AcceptanceCheck acceptanceCheck) {
        List<RockCharacterDto> rockCharacterDtos = holeDetailsReportDao.getRockCharacter(orgCode, holeDetailId);
        List<VideoList> videoLists = videoListDao.getByHoleDetailId(holeDetailId);
        String host = Objects.equals(env.getProperty("isCloud"), "true") ? env.getProperty("bosUrl") : "";
        if (minioConfig.isOpenFlag()) {
            host = minioConfig.getEndPoint() + "/" + minioConfig.getBucketName();
        }
        for (RockCharacterDto rockCharacterDto : rockCharacterDtos) {
            Optional<VideoList> first = videoLists.stream().filter(videoList -> videoList.getSortOrder() == rockCharacterDto.getPoleNo()).findFirst();
            if (first.isPresent()) {
                VideoList videoList = first.get();
                rockCharacterDto.setVideoUrl(host + videoList.getVideoUrl());
                rockCharacterDto.setStartTime(videoList.getStartTime());
            }
            videoLists.stream().filter(videoList -> videoList.getSortOrder() == rockCharacterDto.getLastPoleNo()).findFirst().ifPresent(videoList -> {
                rockCharacterDto.setEndTime(videoList.getEndTime());
            });
        }
        HoleDetail holeDetail = dao.getById(holeDetailId);
        //判断是否已经完成汇报，已完成，杆汇报的数据由汇报孔深决定
        if (holeDetail != null && acceptanceCheck.getHoleDistance() > 0) {
            List<RockCharacterDto> lists = rockCharacterDtos.stream().filter(r -> r.getFrom().compareTo(BigDecimal.valueOf(acceptanceCheck.getHoleDistance())) < 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
            Optional<RockCharacterDto> first = rockCharacterDtos.stream().filter(h -> h.getAbnormalType() != null).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).findFirst();
            if (lists != null && lists.size() > 0) {
                if (first.isPresent() && first.get().getTo().compareTo(BigDecimal.valueOf(acceptanceCheck.getHoleDistance())) > 0) {
                    List<RockCharacterDto> rockCharacterDtoList = rockCharacterDtos.stream().filter(r -> r.getFrom().compareTo(new BigDecimal(first.get().getFrom().toString())) <= 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
                    rockCharacterDtoList.sort(Comparator.comparing(RockCharacterDto::getTo));
                    return rockCharacterDtoList;
                }
                lists.get(0).setTo(BigDecimal.valueOf(acceptanceCheck.getHoleDistance()));
                lists.sort(Comparator.comparing(RockCharacterDto::getTo));
                return lists;
            }
        } else if (holeDetail != null && holeDetail.getReportHoleDistance() != null) {
            List<RockCharacterDto> list = rockCharacterDtos.stream().filter(r -> r.getFrom().compareTo(new BigDecimal(holeDetail.getReportHoleDistance().toString())) < 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
            Optional<RockCharacterDto> first = rockCharacterDtos.stream().filter(h -> h.getAbnormalType() != null).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).findFirst();
            if (list != null && list.size() > 0) {
                if (first.isPresent() && first.get().getTo().compareTo(new BigDecimal(holeDetail.getReportHoleDistance().toString())) > 0) {
                    List<RockCharacterDto> rockCharacterDtoList = rockCharacterDtos.stream().filter(r -> r.getFrom().compareTo(new BigDecimal(first.get().getFrom().toString())) <= 0).sorted(Comparator.comparing(RockCharacterDto::getTo).reversed()).collect(Collectors.toList());
                    rockCharacterDtoList.sort(Comparator.comparing(RockCharacterDto::getTo));
                    return rockCharacterDtoList;
                }
                list.get(0).setTo(new BigDecimal(holeDetail.getReportHoleDistance().toString()));
                list.sort(Comparator.comparing(RockCharacterDto::getTo));
                return list;
            }
        } else {
            return new ArrayList<>();
        }
        return new ArrayList<>();
    }

}
