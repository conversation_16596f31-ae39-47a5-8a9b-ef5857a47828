package jylink.cpds.service.impl;

import com.github.dozermapper.core.Mapper;
import com.github.pagehelper.PageHelper;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.dao.IWorkFacePlanDao;
import jylink.cpds.domain.WorkFacePlan;
import jylink.cpds.service.IWorkFacePlanService;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.PagerParams;
import jylink.cpds.serviceModel.dto.WorkFacePlanDto;
import jylink.cpds.serviceModel.params.WorkFacePlanQueryModel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 工作面计划表(WorkFacePlan)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-18 19:03:59
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkFacePlanServiceImpl implements IWorkFacePlanService {

    @Autowired
    private IWorkFacePlanDao workFacePlanDao;

    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * dozer 帮助类
     */
    @Autowired
    private DozerUtils mapperUtils;

    /**
     * 通过ID查询单条数据
     *
     * @param id      主键
     * @param orgCode 机构编码
     * @return 实例对象
     */
    @Override
    public WorkFacePlanDto queryById(String id, String orgCode) {
        WorkFacePlan workFacePlan = workFacePlanDao.queryById(id, orgCode);
        if (workFacePlan == null) {
            return null;
        }
        return mapper.map(workFacePlan, WorkFacePlanDto.class);
    }

    /**
     * 通过ID确认数据是否存在
     *
     * @param id 主键
     * @return boolean
     */
    @Override
    public boolean anyById(String id, String orgCode) {
        return queryById(id, orgCode) != null;
    }

    /**
     * 数据查询
     *
     * @return
     */
    @Override
    public Pager<WorkFacePlanDto> getByPager(PagerParams<WorkFacePlanQueryModel> queryModel, String orgCode) {
        PageHelper.startPage(queryModel.getCurrentPage(), queryModel.getPageSize());
        WorkFacePlan workFacePlan = mapper.map(queryModel.getObjParams(), WorkFacePlan.class);
        workFacePlan.setOrgCode(orgCode);
        workFacePlan.setDelFlag(false);
        List<WorkFacePlan> workFacePlanLists = workFacePlanDao.queryAll(workFacePlan);
        List<WorkFacePlanDto> listDto = mapperUtils.mapList(workFacePlanLists, WorkFacePlanDto.class);
        Pager<WorkFacePlanDto> pager = new Pager<>(workFacePlanDao.queryAllCount(workFacePlan), listDto);
        return pager;
    }

    /**
     * 新增数据
     *
     * @param workFacePlanDto 实例对象
     * @return 实例对象
     */
    @Override
    public boolean insert(WorkFacePlanDto workFacePlanDto) {
        WorkFacePlan workFacePlan = mapper.map(workFacePlanDto, WorkFacePlan.class);
        workFacePlan.setCreateTime(new Date());
        workFacePlan.setDelFlag(false);
        //判断id是否为空  id如果不为空则是从 文件上传接口调用过来的 , 不需要再使用uuid
        if (StringUtils.isBlank(workFacePlanDto.getId())) {
            workFacePlan.setId(UUID.randomUUID().toString());
        }
        int res = workFacePlanDao.insert(workFacePlan);
        return res == 1;
    }

    /**
     * 修改数据
     *
     * @param workFacePlanDto 实例对象
     * @return 实例对象
     */
    @Override
    public boolean update(WorkFacePlanDto workFacePlanDto) {
        WorkFacePlan workFacePlan = mapper.map(workFacePlanDto, WorkFacePlan.class);
        int res = workFacePlanDao.update(workFacePlan);
        return res == 1;
    }

    /**
     * 通过主键删除数据
     *
     * @param uuid 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String id) {
        return this.workFacePlanDao.deleteById(id) > 0;
    }
}
