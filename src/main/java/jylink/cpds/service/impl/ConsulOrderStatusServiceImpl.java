package jylink.cpds.service.impl;

import com.github.dozermapper.core.Mapper;
import com.github.pagehelper.page.PageMethod;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.dao.IConsulOrderStatusDao;
import jylink.cpds.domain.ConsulOrderStatus;
import jylink.cpds.service.IConsulOrderStatusService;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.PagerParams;
import jylink.cpds.serviceModel.dto.ConsulOrderStatusDto;
import jylink.cpds.serviceModel.params.ConsulOrderStatusQueryModel;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

/**
 * 订单对应的状态变更记录(ConsulOrderStatus)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-02-17 11:36:02
 */
@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ConsulOrderStatusServiceImpl implements IConsulOrderStatusService {
    private final IConsulOrderStatusDao consulOrderStatusDao;
    /**
     * dozer 对象
     */
    private final Mapper mapper;
    /**
     * dozer 帮助类
     */
    private final DozerUtils mapperUtils;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ConsulOrderStatusDto queryById(String id, String orgCode) {
        ConsulOrderStatus consulOrderStatus = consulOrderStatusDao.queryById(id, orgCode);
        return mapper.map(consulOrderStatus, ConsulOrderStatusDto.class);
    }

    /**
     * 通过ID确认数据是否存在
     *
     * @param id 主键
     * @return boolean
     */
    @Override
    public boolean anyById(String id, String orgCode) {
        return consulOrderStatusDao.queryById(id, orgCode) != null;
    }

    /**
     * 数据查询
     *
     * @return
     */
    @Override
    public Pager<ConsulOrderStatusDto> getByPager(PagerParams<ConsulOrderStatusQueryModel> queryModel, String orgCode) {
        PageMethod.startPage(queryModel.getCurrentPage(), queryModel.getPageSize());
        ConsulOrderStatus consulOrderStatus = mapper.map(queryModel.getObjParams(), ConsulOrderStatus.class);
        consulOrderStatus.setOrgCode(orgCode);
        consulOrderStatus.setDelFlag(false);
        List<ConsulOrderStatus> consulOrderStatusLists = consulOrderStatusDao.queryAll(consulOrderStatus);
        List<ConsulOrderStatusDto> listDto = mapperUtils.mapList(consulOrderStatusLists, ConsulOrderStatusDto.class);
        Pager<ConsulOrderStatusDto> pager = new Pager<>(consulOrderStatusDao.queryAllCount(consulOrderStatus), listDto);
        return pager;
    }

    /**
     * 新增数据
     *
     * @param consulOrderStatusDto 实例对象
     * @return 实例对象
     */
    @Override
    public boolean insert(ConsulOrderStatusDto consulOrderStatusDto) {
        ConsulOrderStatus consulOrderStatus = mapper.map(consulOrderStatusDto, ConsulOrderStatus.class);
        consulOrderStatus.setCreateTime(LocalDateTime.now().toDate());
        consulOrderStatus.setDelFlag(false);
        //判断id是否为空  id如果不为空则是从 文件上传接口调用过来的 , 不需要再使用uuid
        if (StringUtils.isBlank(consulOrderStatusDto.getId())) {
            consulOrderStatus.setId(UUID.randomUUID().toString());
        }
        int res = consulOrderStatusDao.insert(consulOrderStatus);
        return res == 1;
    }

    /**
     * 修改数据
     *
     * @param consulOrderStatusDto 实例对象
     * @return 实例对象
     */
    @Override
    public boolean update(ConsulOrderStatusDto consulOrderStatusDto) {
        ConsulOrderStatus consulOrderStatus = mapper.map(consulOrderStatusDto, ConsulOrderStatus.class);
        int res = consulOrderStatusDao.update(consulOrderStatus);
        return res == 1;
    }

    /**
     * 通过主键删除数据
     *
     * @param uuid 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(String uuid) {
        return this.consulOrderStatusDao.deleteById(uuid) > 0;
    }
}