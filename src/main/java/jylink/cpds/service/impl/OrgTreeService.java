package jylink.cpds.service.impl;

import com.ada.jykjcloudx.sdk.api.dto.OrgLogicDTO;
import com.ada.jykjcloudx.sdk.api.dto.UserDTO;
import com.ada.jykjcloudx.sdk.api.dto.UserInfo;
import com.ada.jykjcloudx.sdk.api.entity.OrgTree;
import com.ada.jykjcloudx.sdk.api.entity.SysOrgLogicType;
import com.ada.jykjcloudx.sdk.api.service.OrgLogicService;
import com.ada.jykjcloudx.sdk.core.util.R;
import com.ada.jykjcloudx.sdk.security.util.SecurityUtils;
import jylink.cpds.InitQuartzMessageContext;
import jylink.cpds.common.OrgTreeUtils;
import jylink.cpds.dao.IMineInfoDao;
import jylink.cpds.domain.CoalInfoScreen;
import jylink.cpds.domain.MineInfo;
import jylink.cpds.domain.OrgLogicTree;
import jylink.cpds.feign.SecurtyUtilsService;
import jylink.cpds.service.IBasicService;
import jylink.cpds.service.IOrgTreeService;
import jylink.cpds.serviceModel.BasicOrgTree;
import jylink.cpds.serviceModel.OrgGradeEnum;
import jylink.cpds.serviceModel.TreeNodeTypeEnum;
import jylink.cpds.serviceModel.params.LogicTreeTypeModel;
import jylink.cpds.serviceModel.params.TreeTypeModel;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 逻辑树接口实现类
 * @date 2020/3/5 11:56
 * @version: V1.0
 */
@Service
@Transactional
public class OrgTreeService implements IOrgTreeService {
    /**
     * 逻辑树
     */
    @Autowired
    private OrgLogicService orgLogicService;
    /**
     * 根据逻辑树类型及id查询
     *
     * @param treeType
     * @param id
     * @return java.util.List<jylink.cpds.domain.OrgLogicTree>
     * <AUTHOR>
     * @date 2020/3/5 14:27
     */
    @Override
    public List<OrgLogicTree> getListById(String treeType, String orgId) {
        String treeName = "";
        String logicId = getLogicId(orgId, treeType);
        R<List<SysOrgLogicType>> logicTypes = orgLogicService.getList();
        for (SysOrgLogicType datum : logicTypes.getData()) {
            if (StringUtils.equals(treeType,datum.getCode())){
                treeName = datum.getName();
                break;
            }
        }
        R<List<OrgTree>> tree = orgLogicService.getTree(treeType, logicId);
        return OrgTreeUtils.orgTreeToOrgLogicTree(tree.getData(), treeType, treeName,true);
    }

    @Override
    public List<OrgLogicTree> getOrgTreeByOrgCodeAndTreeType(String treeType, String orgCode,String orgId) {
        String treeName = "";
        if (StringUtils.isBlank(orgId)) {
            orgId = SecurityUtils.getUser().getOrgId();
        }
        String logicId = getLogicId(orgId, treeType);
        R<List<SysOrgLogicType>> logicTypes = orgLogicService.getList();
        for (SysOrgLogicType datum : logicTypes.getData()) {
            if (StringUtils.equals(treeType,datum.getCode())){
                treeName = datum.getName();
                break;
            }
        }
        R<List<OrgTree>> tree = orgLogicService.getTree(treeType, logicId);
        return OrgTreeUtils.orgTreeToOrgLogicTree(tree.getData(), treeType, treeName,true);
    }

    /**
     * 检查当前用户的树级别
     * 1. 煤矿级别 不存在树
     * 2. 别的级别检查是否 存在逻辑树
     *
     * @param orgCode
     * @param userId
     * @return jylink.cpds.serviceModel.params.TreeTypeModel
     * <AUTHOR>
     * @date 2022/6/28 17:05
     */
    @Override
    public TreeTypeModel checkTree(String orgCode, String userId) {
        TreeTypeModel treeTypeModel = new TreeTypeModel();
        SecurtyUtilsService securtyUtilsService = InitQuartzMessageContext.getApplicationContext().getBean(SecurtyUtilsService.class);
        UserInfo userInfo = securtyUtilsService.getUserInfo();
        if (Objects.isNull(userInfo)) {
            // 当前用户信息 为空
            //矿
            treeTypeModel.setTreeFlag(false);
            treeTypeModel.setTreeType(null);
            treeTypeModel.setLogicTreeTypeList(null);
            return treeTypeModel;
        }
        UserDTO userDTO = userInfo.getUserDTO();
        if (Objects.isNull(userDTO)) {
            //矿
            treeTypeModel.setTreeFlag(false);
            treeTypeModel.setTreeType(null);
            treeTypeModel.setLogicTreeTypeList(null);
            return treeTypeModel;
        }
        if (!StringUtils.equals(userDTO.getGrade(), OrgGradeEnum.COAL.getLevel()) && !StringUtils.equals(userDTO.getGrade(), OrgGradeEnum.OTHER.getLevel()) && !StringUtils.equals(userDTO.getGrade(), null)) {
            //存在树
            treeTypeModel.setTreeFlag(true);
        } else {
            //矿
            treeTypeModel.setTreeFlag(false);
            treeTypeModel.setTreeType(null);
            treeTypeModel.setLogicTreeTypeList(null);
            return treeTypeModel;
        }
        List<LogicTreeTypeModel> logicTreeType = getLogicTreeType(userDTO.getOrgId());
        if (logicTreeType.isEmpty()) {
            //物理树
            treeTypeModel.setTreeType("0");
            treeTypeModel.setLogicTreeTypeList(null);
            return treeTypeModel;
        }
        List<String> treeTypeList = logicTreeType.stream().map(LogicTreeTypeModel::getId).distinct().collect(Collectors.toList());
        if (treeTypeList.isEmpty()) {
            //物理树
            treeTypeModel.setTreeType("0");
            treeTypeModel.setLogicTreeTypeList(null);
            return treeTypeModel;
        }
        //逻辑树
        treeTypeModel.setTreeType("1");
        treeTypeModel.setLogicTreeTypeList(logicTreeType);
        return treeTypeModel;
    }

    /**
     * 通过机构id  获取当前机构所属的所有逻辑树
     *
     * @param orgId 机构id
     * @return java.util.List<jylink.cpds.serviceModel.params.LogicTreeTypeModel>
     * <AUTHOR>
     * @date 2022/6/28 17:25
     */
    private List<LogicTreeTypeModel> getLogicTreeType(String orgId) {
        List<LogicTreeTypeModel> list = new ArrayList<>();
        R<List<OrgLogicDTO>> orgLogicDtos = orgLogicService.getListByOrgId(orgId, null);
        for (OrgLogicDTO orgLogicDtosDatum : orgLogicDtos.getData()) {
            LogicTreeTypeModel logicTreeTypeModel = new LogicTreeTypeModel();
            logicTreeTypeModel.setId(orgLogicDtosDatum.getLogicCode());
            logicTreeTypeModel.setName(orgLogicDtosDatum.getLogicCodeName());
            list.add(logicTreeTypeModel);
        }
        return list;
    }
    /**
     *  通过机构id  和 逻辑树code 返回逻辑机构id
     * <AUTHOR>
     * @date 2022/6/29 11:04
     * @param orgId 机构id
     * @param treeCode 逻辑树code
     * @return java.lang.String
     */
    private String getLogicId (String orgId,String treeCode){
        R<List<OrgLogicDTO>> orgLogicDtos = orgLogicService.getListByOrgId(orgId, treeCode);
        List<OrgLogicDTO> data = orgLogicDtos.getData();
        if (Objects.isNull(data) || data.isEmpty()){
            return null;
        }
        return data.get(0).getId();
    }
}
