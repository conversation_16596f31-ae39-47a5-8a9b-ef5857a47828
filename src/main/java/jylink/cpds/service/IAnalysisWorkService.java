package jylink.cpds.service;

import jylink.cpds.serviceModel.AI.AnalysisCancelJob;
import jylink.cpds.serviceModel.AI.AnalysisHistoryCancelJob;
import jylink.cpds.serviceModel.AI.AnalysisHistoryJob;
import jylink.cpds.serviceModel.AI.AnalysisJob;
import jylink.cpds.serviceModel.JsonResponseGeneric;
import jylink.cpds.serviceModel.dto.AnalysisWorkDto;

import java.util.List;

/**
 * AI下达任务服务接口
 * 
 * <AUTHOR>
 */
public interface IAnalysisWorkService {
    /**
     * 记录下发任务记录
     * 
     * @param drainId 台账Id
     * @param orgCode 机构编码
     * @param userId  用户Id
     * @return 是否下发成功
     */
    JsonResponseGeneric addAnalysis(String drainId, String orgCode, String userId,String logId);

    /**
     * 根据Id获取数据
     *
     * @param id 数据Id
     * @return 实体对象
     */
    AnalysisWorkDto getById(String id);

    /**
     * 根据台账Id查询数据
     *
     * @param drainId 台账Id
     * @return 查询结果
     */
    List<AnalysisWorkDto> getByDrainId(String drainId);

    /**
     * 根据台账Id查询数据 且完成实时识别
     *
     * @param drainId 台账Id
     * @param status  状态
     * @return 查询结果
     */
    List<AnalysisWorkDto> getByDrainIdAndStatus(String drainId, int status);

    /**
     * 根据状态查询数据
     *
     * @param status  状态
     * @param orgCode 机构编码
     * @return 查询结果
     */
    List<AnalysisWorkDto> getAllAnalysing(int status, String orgCode);

    /**
     * 根据台账Id和通道号查询数据
     *
     * @param drainId 台账Id
     * @param channel 通道号
     * @return 查询结果
     */
    AnalysisWorkDto getByDrainIdAndChannel(String drainId, int channel);

    /**
     * 判断数据是否存在
     *
     * @param id 数据Id
     * @return 查询结果
     */
    boolean anyById(String id);

    /**
     * 下发识别任务
     *
     * @param job 实体参数对象
     * @return 是否下达成功
     */
    boolean releaseTask(AnalysisJob job,String logId);

    /**
     * 下历史发识别任务
     *
     * @param job 实体参数对象
     * @return 是否下达成功
     */
    boolean releaseHistoryTask(AnalysisHistoryJob job,String logId);

    /**
     * 下发取消任务
     *
     * @param job 实体参数对象
     * @return 是否下达成功
     */
    boolean cancelReleaseTask(AnalysisCancelJob job,String logId);

    /**
     * 下发取消任务
     *
     * @param job 实体参数对象
     * @return 是否下达成功
     */
    boolean cancelHistoryReleaseTask(AnalysisHistoryCancelJob job,String logId);

    /**
     * 更新数据状态
     *
     * @param id     任务id
     * @param status 任务状态
     * @return 更新结果
     */
    boolean updateStatus(String id, int status);

    /**
     * 获取工作面下正在识别的任务
     *
     * @param drainId 探水台账Id
     * @param status  任务状态
     * @return 查询结果
     */
    Integer getAnalysingByDrainId(String drainId, int status);

    /**
     * 获取监控点编号
     *
     * @param channelNumber 通道号
     * @param orgCode       机构编码
     * @return 查询结果
     */
    String getIndexcode(String channelNumber, String orgCode);

    /**
     * 查询是否有正常结束的
     *
     * @param drainId 台账Id
     * @param status  状态（正常结束）
     * @return 查询结果
     */
    boolean anyNormalFinish(String drainId, int status);
}
