package jylink.cpds.service;

import jylink.cpds.domain.ExcavationReport;
import jylink.cpds.serviceModel.JsonResponseGeneric;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.dto.ExcavationReportDto;
import jylink.cpds.serviceModel.params.ApprovalReasonEditModel;
import jylink.cpds.serviceModel.params.ApprovalWorkflowParamsModel;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 掘进汇报表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
public interface IExcavationReportService {
    /**
     * 添加数据
     *
     * @param instance 实体对象集合
     * @return 是否添加成功
     */
    boolean add(ExcavationReport instance, List<List<ApprovalWorkflowParamsModel>> flows) throws Exception;

    /**
     * 根据设计ID查询数据
     *
     * @param tunnelId
     * @param orgCode
     * @return
     */
    List<ExcavationReport> getByTunnelId(String tunnelId, String orgCode);

    /**
     * 根据设计ID查询数据和汇报状态查询数据
     *
     * @param tunnelId
     * @param orgCode
     * @return
     */
    List<ExcavationReport> getByTunnelIdAndIsCorrect(String tunnelId, String orgCode,int isCorrect);

    /**
     * 根据ID查询数据
     *
     * @param id
     * @param orgCode
     * @return
     */
    ExcavationReportDto getById(String id, String orgCode);


    /**
     * 查询数据
     *
     * @param tunnelId
     * @param endDate
     * @return
     */
    Pager<ExcavationReportDto> getData(String tunnelId, String startDate, String endDate, int currentPage, int pageSize);

    /**
     * 删除数据
     *
     * @param id
     * @param orgCode
     * @return
     */
    boolean delete(String id, String orgCode);

    /**
     * 修改数据
     * @param excavationReport
     * @return
     */
    boolean update(ExcavationReport excavationReport,List<List<ApprovalWorkflowParamsModel>> flows);

    /**
     * 根据设计ID查询数据
     *
     * @param tunnelId
     * @param orgCode
     * @return
     */
    List<ExcavationReport> getByClassAndTunnelIdAndWorkDate(String tunnelId, String orgCode, String workClassNumKey, Date workDate, String isCorrect) throws ParseException;


    /**
     * 根据设计ID查询数据
     *
     * @param tunnelId
     * @param orgCode
     * @return
     */
    List<ExcavationReport> getByTunnelIdAndWorkDate(String tunnelId, String orgCode, Date workDate, String isCorrect) throws ParseException;

    /**
     * 查询数据是否存在
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  是否存在
     */
    boolean anyById(String id, String orgCode);

    /**
     * 审批通过
     * @param approvalReasonEditModel  审批模型
     * @param excavationReport  掘进汇报模型
     * @return
     */
    JsonResponseGeneric approval(ApprovalReasonEditModel approvalReasonEditModel, ExcavationReport excavationReport);

    /**
     * 根据设计ID查询数据
     * @param tunnelId  设计id
     * @param orgCode  机构编码
     * @return  查询结果
     */
    ExcavationReportDto getPosition(String tunnelId, String orgCode);

    /**
     * 修改审批状态
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  是否成功
     */
    boolean updateStatus(String id,String orgCode,Integer status);

    /**
     * 拒绝
     * @param model
     * @return
     */
    boolean refuse(ApprovalReasonEditModel model);

    /**
     * 撤回
     * @param model
     * @return
     */
    boolean reVoke(ApprovalReasonEditModel model);

    /**
     * 提交审批
     * @param id  主键id
     * @return
     */
    boolean approving(String id,String orgCode);

    /**
     * 获取探水后的掘进距离
     * @param planId  计划id
     * @return  查询结果
     */
    Pager<ExcavationReportDto> getExploreInfo(String planId,int currentPage,int pageSize);

}
