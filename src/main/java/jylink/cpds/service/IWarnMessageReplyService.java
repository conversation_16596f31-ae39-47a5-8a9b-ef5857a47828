package jylink.cpds.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jylink.cpds.domain.WarnMessageReply;
import jylink.cpds.serviceModel.dto.WarnMessageReplyDto;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
public interface IWarnMessageReplyService extends IService<WarnMessageReply> {

    boolean insert(WarnMessageReplyDto warnMessageReplyDto, String orgCode);

    List<WarnMessageReplyDto> getByWarnId(String warnId);

    /**
     * 一键督办
     * @param drainIds  台账列表
     */
    void oneClickSupervision(List<String> drainIds,Integer type);

}
