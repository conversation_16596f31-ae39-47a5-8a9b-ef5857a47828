package jylink.cpds.service;

import jylink.cpds.serviceModel.BaseItem;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface INewManageAppService {

    /**
     * 获取联网信息
     * @return  查询结果
     */
    InternetMineInfoDto getInternetMineInfo();

    /**
     * 根据类型查询物探信息
     * @param type  类型
     * @return  查询结果
     */
    GeophysicalDrillInfoDto getGeophysicalDrillInfo(Integer type);

    /**
     * 获取探放水任务关注
     * @return
     */
    ExplorationTaskInfoDto getExplorationTaskInfo();

    /**
     * 获取异常煤矿列表
     * @param handleType
     * @param messageTypes
     * @param durationType
     * @param startDate
     * @param endDate
     * @return
     */
    WarnAbnormalOrgListDto getAbnormalByOrg(boolean handleType , List<Integer> messageTypes , Integer durationType,String workName , String startDate , String endDate, String orgName,int currentPage , int pageSize);

    /**
     *
     * @param orgName
     * @param currentPage
     * @param pageSize
     * @return
     */
    RealWaterDetectionDto getRealWaterDetection(String orgName , int currentPage , int pageSize);

    RealWaterDetectionDto getNoAnalysisList(String orgName , Integer orderType , Integer day , int currentPage , int pageSize);

    /**
     *
     * @param orgName
     * @param type
     * @param analysisType
     * @param result
     * @param currentPage
     * @param pageSize
     * @return
     */
    RealWaterDetectionDto getDrillPlanList(String orgName ,Integer type , Integer analysisType, String tunnelId ,String orgCode,Boolean result , int currentPage , int pageSize);

    /**
     * 获取杆汇报异常个数
     * @param orgName
     * @param type
     * @param startDate
     * @param endDate
     * @return
     */
    List<BaseItem> getPoleAbnormalCount(String orgName ,Integer type , String startDate , String endDate);

    /**
     *
     * @param orgName
     * @param type
     * @param abnormalType
     * @param startDate
     * @param endDate
     * @param currentPage
     * @param pageSize
     * @return
     */
    Pager<PoleAbnormalInfoDto> getPoleAbnormalList(String orgName , Integer type , Integer abnormalType , String startDate , String endDate , String orgCode , String tunnelId, int currentPage , int pageSize);

    /**
     * 根据机构编码查询数据信息
     * @param orgCode  机构编码
     * @return
     */
    RealWaterDetectionDto getMineDrillInfo(String orgCode);

    /**
     * 获取煤层信息
     * @param orgCode
     * @return
     */
    List<CoalLayerWorkDto> getCoalLayerWorkInfo( String orgCode );

    /**
     * 获取同步信息
     * @param orgCode  机构编码
     * @return   查询结果
     */
    MineInfoDto getSymmetricInfo( String orgCode );

    /**
     *
     *
     * @param orgCode
     * @return
     */
    List<LastGeophysicalDto> getLastGeophysicalInfo( String orgCode );

    /**
     *
     * @param orgCode
     * @return
     */
    LastDrillInfoDto getLastDrillInfo(String orgCode);

    /**
     *
     * @param orgCode
     * @return
     */
    WarnOrgStatisticsDto getWarnByOrgCode(String orgCode);

    /**
     * 获取app首页数据总览
     * @param year  年份
     * @param month  月份
     * @param orgCode  机构编码
     * @return  查询结果
     */
    APPDataOverviewDto getDataOverview(Integer year , Integer month , String orgCode);

    /**
     * 获取探水总览
     * @param year  年份
     * @param month  月份
     * @param orgCode  机构编码
     * @return  查询结果
     */
    List<AppDrillOverviewDto> getDrillOverview(Integer year , Integer month , String orgCode);

    /**
     * 根据月份获取进尺总览
     * @param year  年份
     * @param month  月份
     * @param orgCode  机构编码
     * @return
     */
    List<AppTunnelOverviewDto> getTunnelOverview(Integer year , Integer month , String orgCode);

}
