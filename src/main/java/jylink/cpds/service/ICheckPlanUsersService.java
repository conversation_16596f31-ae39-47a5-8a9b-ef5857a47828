package jylink.cpds.service;


import jylink.cpds.domain.CheckPlanUsers;
import jylink.cpds.serviceModel.dto.CheckPlanUsersDto;

import java.util.List;

/**
 * <AUTHOR>
 * 探水计划以及施工人员对应表 服务接口
 */
public interface ICheckPlanUsersService {
    /**
     * 插入数据
     * @param checkPlanUsersDto
     * @return  是否插入成功
     */
    boolean add(CheckPlanUsersDto checkPlanUsersDto);

    /**
     * 根据计划ID批量删除数据
     * @param checkPlanId
     * @return  是否删除成功
     */
    Boolean deleteByCheckPlanId(String checkPlanId,String orgCode);

    /**
     * 根据计划ID查询人员信息
     * @param checkPlanId
     * @return 人员LIST
     */

    List<CheckPlanUsers> getByCheckPlanId(String checkPlanId,String orgCode);


    /**
     * 批量插入数据
     * @param list
     * @return
     */
    Boolean addList(List<CheckPlanUsers> list);

    /**
     * 逻辑删除
     * @param checkPlanId
     * @param orgCode
     * @return 是否删除成功
     */
    Boolean logicDelete(String checkPlanId, String orgCode);

}
