package jylink.cpds.service;

import jylink.cpds.serviceModel.dto.CoordinateViewDto;
import jylink.cpds.serviceModel.dto.EnforcementBasisDto;
import java.util.List;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.EnforcementBasisQueryModel;
import jylink.cpds.serviceModel.PagerParams;

/**
 * (EnforcementBasis)表服务接口
 *
 * <AUTHOR>
 * @since 2020-09-22 17:13:43
 */
public interface IEnforcementBasisService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    EnforcementBasisDto queryById(String id,String orgCode);
    /**
     * 通过ID确认数据是否存在
     *
     * @param id 主键
     * @return boolean
     */
    boolean anyById(String id,String orgCode);

    /**
     * 新增数据
     *
     * @param enforcementBasisDto 实例对象
     * @return 实例对象
     */
    boolean insert(EnforcementBasisDto enforcementBasisDto);

    /**
     * 修改数据
     *
     * @param enforcementBasisDto 实例对象
     * @return 实例对象
     */
    boolean update(EnforcementBasisDto enforcementBasisDto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);
    
     /**
     * 数据查询
     * @return 
     */
    Pager<EnforcementBasisDto> getByPager(  PagerParams<EnforcementBasisQueryModel> queryModel,String orgCode);

    /**
     * 根据执法类型查询执法依据
     * @param type  执法类型
     * @return  查询结果
     */
    List<CoordinateViewDto> getByType(String type);

}