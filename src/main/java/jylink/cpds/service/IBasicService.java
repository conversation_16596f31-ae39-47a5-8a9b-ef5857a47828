package jylink.cpds.service;

import com.ada.jykjcloudx.sdk.api.entity.OrgTree;
import jylink.cpds.domain.MineInfo;
import jylink.cpds.domain.OrgLogicTree;
import jylink.cpds.serviceModel.BasicOrgTree;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 基本service
 * @date 2020/4/28 16:59
 * @version: V1.0
 */
public interface IBasicService {

    List<BasicOrgTree> getOrgTreeByOrgId(String orgCode);
    /**
     * 获取所有矿的orgCodes
     * <AUTHOR>
     * @date 2020/6/19 16:15
     * @param
     * @return  java.util.List<java.lang.String>
     */
    List<String> getOrgCodes();


    List<String> getOrgCodes(String selectCode);


    /**
     * 获取所有矿的所属orgCode下的所有
     * <AUTHOR>
     * @date 2020/6/19 16:15
     * @param
     * @return  java.util.List<java.lang.String>
     */
    List<String> getOrgCodesByOrgId(String orgCode);

    /**
     * 通过treeType  和 orgCode查询所属树  包括机构树和逻辑树
     * <AUTHOR>
     * @date 2020/6/22 10:25
     * @param
     * @return
     */
    OrgLogicTree getOrgLogicTreeList(String treeType,String orgCode,String orgId);

    OrgLogicTree getOrgLogicTreeListNoId(String treeType,String orgCode);

    /**
     * 获取当前机构下所有下属煤矿编码不要token
     * @param orgId  机构id
     * @return  查询结果
     */
    List<String> getOrgCodesNoToken(String orgId,String userId,String orgCode);

    /**
     *  判断 展示 市  县 展示级别
     * <AUTHOR>
     * @date 2021/1/28 14:21
     * @param
     * @return java.util.Map<java.lang.String,java.lang.String>
     */
    Map<String, String> getFormHeadShowStatus() ;

    /**
     *  获取当前机构 下级机构中 已经接入的矿编码
     * <AUTHOR>
     * @date 2022/7/15 16:54
     * @param
     * @return java.util.List<java.lang.String>
     */
    List<String> getAllAccessOrgCodes();

    List<String> getAllAccessOrgCodes(String selectOrgCode);
    /**
     *  获取当前机构下 所有的矿编码
     * <AUTHOR>
     * @date 2022/7/15 16:55
     * @param
     * @return java.util.List<java.lang.String>
     */
    List<String> getAllOrgCodes();

    /**
     * 根据逻辑树ID获取全部 机构编码
     * @param logicId  逻辑树节点ID
     * @return 查询结果
     */
    List<String> getAllLogicOrgCodes(String logicId);

    /**
     * 获取全部已接入机构
     * @param logicId  逻辑树ID
     * @return
     */
    List<String> getAllAccessLogicCodes(String logicId);

    public List<MineInfo> getMineInfos(List<String> orgCodes);

    OrgTree getLogicOrgTreeById(String logicId,String logicType);

}
