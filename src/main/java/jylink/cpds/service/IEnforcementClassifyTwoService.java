package jylink.cpds.service;

import jylink.cpds.serviceModel.ListItem;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.PagerParams;
import jylink.cpds.serviceModel.dto.EnforcementClassifyTwoDto;
import jylink.cpds.serviceModel.params.EnforcementClassifyTwoQueryModel;

import java.util.List;

/**
 * (EnforcementClassifyTwo)表服务接口
 *
 * <AUTHOR>
 * @since 2020-10-12 16:39:06
 */
public interface IEnforcementClassifyTwoService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    EnforcementClassifyTwoDto queryById(String id, String orgCode);
    /**
     * 通过ID确认数据是否存在
     *
     * @param id 主键
     * @return boolean
     */
    boolean anyById(String id, String orgCode);

    /**
     * 新增数据
     *
     * @param enforcementClassifyTwoDto 实例对象
     * @return 实例对象
     */
    boolean insert(EnforcementClassifyTwoDto enforcementClassifyTwoDto);

    /**
     * 修改数据
     *
     * @param enforcementClassifyTwoDto 实例对象
     * @return 实例对象
     */
    boolean update(EnforcementClassifyTwoDto enforcementClassifyTwoDto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);

     /**
     * 数据查询
     * @return
     */
    Pager<EnforcementClassifyTwoDto> getByPager(PagerParams<EnforcementClassifyTwoQueryModel> queryModel, String orgCode);

    /**
     * 根据一级code查询二级数据信息
     * @param oneCode  一级code
     * @return  查询结果
     */
    List<EnforcementClassifyTwoDto> getByCode(String oneCode);

    /**
     * 查询需要配置的二级菜单信息
     * @param needConfig  是否需要配置
     * @return
     */
    List<ListItem> getConfigType(boolean needConfig);

    /**
     * 根据一级菜单code查询二级菜单中需要配置的数据信息
     * @param oneCode  一级编码
     * @param needConfig  是否需要配置
     * @return  查询结果
     */
    List<EnforcementClassifyTwoDto> getConfigTwoCode(String oneCode, boolean needConfig);

}