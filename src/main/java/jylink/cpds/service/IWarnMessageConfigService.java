package jylink.cpds.service;

import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.PagerParams;
import jylink.cpds.serviceModel.dto.WarnMessageConfigDto;
import jylink.cpds.serviceModel.params.WarnMessageConfigQueryModel;

/**
 * (WarnMessageConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-25 17:33:41
 */
public interface IWarnMessageConfigService {

    /**
     * 通过ID查询单条数据
     *
     * @param orgCode 机构编码
     * @param id      主键
     * @return 实例对象
     */
    WarnMessageConfigDto queryById(String id, String orgCode);

    /**
     * 通过ID确认数据是否存在
     *
     * @param orgCode 机构编码
     * @param id      主键
     * @return boolean
     */
    boolean anyById(String id, String orgCode);

    /**
     * 数据查询
     *
     * @param queryModel 查询实体
     * @param orgCode    机构编码
     * @return
     */
    Pager<WarnMessageConfigDto> getByPager(PagerParams<WarnMessageConfigQueryModel> queryModel, String orgCode);

}
