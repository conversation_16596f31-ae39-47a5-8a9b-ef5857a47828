package jylink.cpds.service;

import jylink.cpds.domain.EvacuateConfigFile;
import jylink.cpds.domain.EvacuateWarn;
import jylink.cpds.domain.EvacuateWarnPlus;
import jylink.cpds.serviceModel.Pager;

import java.util.List;

/**
 * (EvacuateWarn)表服务接口
 *
 * <AUTHOR>
 * @since 2020-03-16 11:28:21
 */
public interface IEvacuateWarnService {

    /**
     * 通过ID查询单条数据
     *
     * @param uuid 主键
     * @return 实例对象
     */
    EvacuateWarn queryById(String uuid);

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<EvacuateWarn> queryAllByLimit(int offset, int limit);

    /**
     * 新增数据
     *
     * @param evacuateWarn 实例对象
     * @return 实例对象
     */
    EvacuateWarn insert(EvacuateWarn evacuateWarn);

    /**
     * 修改数据
     *
     * @param evacuateWarn 实例对象
     * @return 实例对象
     */
    EvacuateWarn update(EvacuateWarn evacuateWarn);

    /**
     * 通过主键删除数据
     *
     * @param uuid 主键
     * @return 是否成功
     */
    boolean deleteById(String uuid);

    /**
    * 添加警告消息
    * <AUTHOR>
    * @date 2020/3/16 14:07
    * @return  boolean
    */
    EvacuateConfigFile addWarn();
    /**
    * 超管消警告
    * <AUTHOR>
    * @date 2020/3/16 15:38
    * @return  void
    */
    boolean cleanWarn(String id);
    /**
    * 一键报警警告list
    * <AUTHOR>
    * @date 2020/3/16 16:05
    */
    Pager<EvacuateWarn> getWarnList(int currentPage, int pageSize);
    /**
    * 一键报警警告消息报警消息
    * <AUTHOR>
    * @date 2020/3/16 16:14
    * @return  java.util.List<jylink.cpds.domain.EvacuateWarn>
    */
    List<EvacuateWarnPlus> getWarnInInfo();
}