package jylink.cpds.service;

import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.dto.GeophysicalReportDto;
import jylink.cpds.serviceModel.dto.OrgGeophysicalDto;

import java.util.List;

public interface IGeophysicalReportService {

    /**
     * 根据id查询
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  查询结果
     */
    GeophysicalReportDto getById(String id, String orgCode);

    /**
     * 分页
     * @param orgName  机构名称
     * @param endDate  结束时间
     * @param result  物探结果
     * @param currentPage  当前页
     * @param pageSize  页面大小
     * @return  查询结果
     */
    Pager<GeophysicalReportDto> getByOrgCode(String orgName,String startDate,String endDate,String result,Integer method,Integer type,int currentPage, int pageSize,
                                             String mineCityZoneCode,String  mineCountyIdCode,String mineProvzoneCode , String orgCode , String workFaceId);

    /**
     * 获取全部物探个数
     * @param orgName
     * @param startDate
     * @param endDate
     * @param result
     * @param method
     * @param type
     * @param orgCode
     * @param workFaceId
     * @return
     */
    OrgGeophysicalDto getAllGeophysicalCount(String orgName,String startDate,String endDate,String result,Integer method,Integer type , String orgCode , String workFaceId);

    /**
     * 分页
     * @param orgCode  机构编码
     * @param workName  设计id
     * @param result  物探结果
     * @param currentPage  当前页
     * @param pageSize  页面大小
     * @return  查询结果
     */
    Pager<GeophysicalReportDto> getOrgCode(String orgCode,String workName,String result,int currentPage, int pageSize);

    /**
     * 添加数据
     * @param instance  数据实体
     * @return  是否添加成功
     */
    boolean add(GeophysicalReportDto instance);

    /**
     * 修改数据
     * @param instance  数据实体
     * @return  是否添加成功
     */
    boolean update(GeophysicalReportDto instance);

    /**
     * 根据id查询数据是否存在
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  是否存在
     */
    boolean anyById(String id,String orgCode);

    /**
     * 删除
     * @param id 主键id
     * @return  是否删除成功
     */
    boolean delete(String id,String orgCode);

    /**
     * 根据设计id和机构编码查询数据信息
     * @param tunnelId  设计id
     * @param orgCode  机构编码
     * @return  查询结果
     */
    List<GeophysicalReportDto> getByTunnelId(String tunnelId,String orgCode);

    /**
     * 获取物探管理数据信息
     * @param orgName  机构名称
     * @param month  近几月
     * @param currentPage  当前页
     * @param pageSize  页面大小
     * @return
     */
    Pager<OrgGeophysicalDto> getGeophysical(String orgName , Integer month , int currentPage, int pageSize);

    OrgGeophysicalDto getGeophysicalCount(String orgName , Integer month);

}
