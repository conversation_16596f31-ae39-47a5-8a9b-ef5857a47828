package jylink.cpds.service;

import jylink.cpds.serviceModel.dto.DrainAccountPersonDto;
import jylink.cpds.serviceModel.dto.DrainPersonDto;
import jylink.cpds.serviceModel.person.InterfaceRealResult;
import jylink.cpds.serviceModel.person.PersonInfo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IPersonService {

    /**
     * 根据机构编码和人员定位卡信息查询人员数据
     * @param orgCode  机构编码
     * @param personCards  人员定位卡号
     * @param startTime 台账ID
     * @param endTime
     * @return
     */
    List<InterfaceRealResult> getPersonInfo(String orgCode , List<String> personCards  , Date startTime ,  Date endTime);

    /**
     * 根据机构编码和人员定位卡信息查询人员数据
     * @param orgCode  机构编码
     * @param personCard  人员定位卡号
     * @param drainId 台账ID
     * @return
     */
    List<InterfaceRealResult> getPersonTrackInfo(String orgCode , String personCard , String drainId);

    /**
     * 根据机构编码和人员定位卡信息查询人员数据
     * @param orgCode  机构编码
     * @param personCards  人员定位卡号
     * @param startTime 台账ID
     * @param endTime
     * @return
     */
    List<InterfaceRealResult> getHistoryPersonInfo(String orgCode , List<String> personCards  , Date startTime ,  Date endTime);

    /**
     * 获取人员轨迹
     * @param orgCode
     * @param personCard
     * @param mineEnterTime
     * @return
     */
    List<InterfaceRealResult> getPersonTrack(String orgCode , String personCard , String mineEnterTime);

    /**
     * 根据台账获取人员轨迹信息
     * @param drainId  台账ID
     * @return  查询结果
     */
    DrainPersonDto getPersonTrack(String drainId);

    /**
     * 根据机构名称和人员定位卡号获取人员卡号信息
     * @param orgCode
     * @param perCardNos
     * @return
     */
    List<PersonInfo> getPersonCardInfo(String orgCode , List<String> perCardNos);

    /**
     * 根据机构编码和人员定位卡信息查询人员数据
     * @param orgCode  机构编码
     * @param personCards  人员定位卡号
     * @param startTime 台账ID
     * @param endTime
     * @return
     */
    List<InterfaceRealResult> getRealInfoAndTrack(String orgCode , List<String> personCards  , Date startTime ,  Date endTime);

    /**
     * 根据机构编码和人员定位卡信息查询人员数据
     * @param orgCode  机构编码
     * @param personCards  人员定位卡号
     * @param startTime 台账ID
     * @param endTime
     * @return
     */
    List<InterfaceRealResult> getHistoryInfoAndTrack(String orgCode , List<String> personCards  , Date startTime ,  Date endTime);

    /**
     *
     * @param drainId
     * @return
     */
    DrainAccountPersonDto getDrainPersonInfo(String drainId);

    /**
     * 根据机构编码和人员定位卡信息查询入井和出井时间段与指定时间范围有交集的实时人员数据
     * @param orgCode  机构编码
     * @param personCards  人员定位卡号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<InterfaceRealResult> getPersonInfoByTimeIntersection(String orgCode , List<String> personCards  , Date startTime ,  Date endTime);

    /**
     * 根据机构编码和人员定位卡信息查询入井和出井时间段与指定时间范围有交集的历史人员数据
     * @param orgCode  机构编码
     * @param personCards  人员定位卡号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<InterfaceRealResult> getHistoryPersonInfoByTimeIntersection(String orgCode , List<String> personCards  , Date startTime ,  Date endTime);

}
