package jylink.cpds.service;

import jylink.cpds.serviceModel.NetWorkingDTO;
import jylink.cpds.serviceModel.NetWorkingSituationDTO;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.PagerParams;
import jylink.cpds.serviceModel.dto.AcceptanceHoleDto;
import jylink.cpds.serviceModel.dto.CoalInfoDto;
import jylink.cpds.serviceModel.dto.MaterialCoalInfoDto;
import jylink.cpds.serviceModel.params.CoalMaterialQueryModel;

import java.util.List;

public interface IManagerAPPService {

    /**
     * 查询下属煤矿已上传未上传材料煤矿个数
     * @return  查询结果
     */
    List<MaterialCoalInfoDto> getMaterialInfo();

    /**
     * 获取煤矿材料数据信息
     * @param model  查询实体
     * @return  查询结果
     */
    Pager<CoalInfoDto> getInfo(PagerParams<CoalMaterialQueryModel> model);

    /**
     *  获取联网情况
     * <AUTHOR>
     * @date 2021/1/20 17:29
     * @param
     * @return void
     */
    List<NetWorkingDTO> netWorkIng();
    
    /**
     *  联网情况
     * <AUTHOR>
     * @date 2021/1/25 14:49
     * @param 
     * @return jylink.cpds.serviceModel.NetWorkingSituationDTO
     */
    NetWorkingSituationDTO netWorkIngSituation();

    /**
     * 获取机构数机构编码
     * @return
     */
    String getTreeOrgCode();

    /**
     * 获取钻孔详情
     * @param holeDetailId  台账id
     * @return  查询结果
     */
    AcceptanceHoleDto getHoleInfo(String holeDetailId);


}
