package jylink.cpds.service;

import jylink.cpds.serviceModel.dto.EnforcementMainUserDto;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.params.EnforcementMainUserQueryModel;
import jylink.cpds.serviceModel.PagerParams;

/**
 * 检查任务人员分类表(EnforcementMainUser)表服务接口
 *
 * <AUTHOR>
 * @since 2020-10-14 15:28:44
 */
public interface IEnforcementMainUserService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    EnforcementMainUserDto queryById(String id,String orgCode);
    /**
     * 通过ID确认数据是否存在
     *
     * @param id 主键
     * @return boolean
     */
    boolean anyById(String id,String orgCode);

    /**
     * 新增数据
     *
     * @param enforcementMainUserDto 实例对象
     * @return 实例对象
     */
    boolean insert(EnforcementMainUserDto enforcementMainUserDto);

    /**
     * 修改数据
     *
     * @param enforcementMainUserDto 实例对象
     * @return 实例对象
     */
    boolean update(EnforcementMainUserDto enforcementMainUserDto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(String id);
    
     /**
     * 数据查询
     * @return 
     */
    Pager<EnforcementMainUserDto> getByPager(  PagerParams<EnforcementMainUserQueryModel> queryModel,String orgCode);

    /**
     * 判断当前人是否是组长
     * @param mainId  执法主表地
     * @param userId  用户id
     * @return  查询结果
     */
    boolean isLeader(String mainId,String userId);

}