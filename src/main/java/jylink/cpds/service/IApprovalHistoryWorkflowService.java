package jylink.cpds.service;

import jylink.cpds.serviceModel.dto.ApprovalHistoryWorkflowDto;

import java.util.List;

/**
 * 探水审批历史数据服务接口
 *
 * <AUTHOR>
 */
public interface IApprovalHistoryWorkflowService {
    /**
     * 根据数据Id查询
     *
     * @param dataId 数据Id
     * @return 查询结果
     */
    List<ApprovalHistoryWorkflowDto> getByDataId(String dataId);

    /**
     * 添加历史审批数据
     *
     * @param dataId 审批模块的数据Id
     * @return 是否添加成功
     */
    boolean add(String userId, String dataId);
}
