package jylink.cpds.serviceModel;

public enum ServiceEquipmentType {
    BASIC(0, "基础类"),
    NETWORK(1, "网络类");
    /**
     * 枚举类型
     */
    private final int type;

    /**
     * 类型解释
     */
    private final String interpretation;
    ServiceEquipmentType(int type, String interpretation) {
        this.type = type;
        this.interpretation = interpretation;
    }
    public int getValue() {
        return type;
    }

    public String getInterpretation() {
        return interpretation;
    }


}
