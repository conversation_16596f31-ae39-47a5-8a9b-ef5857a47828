package jylink.cpds.serviceModel.params;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 数据退回参数模型
 */
@Data
public class ApprovalRefusedParams {
    /**
     * 数据Id
     */
    @NotBlank
    @Size(min = 36,max = 36)
    private String id;

    /**
     * 退回原因
     */
    @NotBlank
    @Size(max = 50)
    private String reason;
    /**
     * 密码
     */
    private String password;
}
