package jylink.cpds.serviceModel.params;

import lombok.Data;

import java.util.Date;

/**
 * @Author: hupf
 * @Data:2023/6/8 14:41
 * @Description: 下属机构摄像头监控筛选
 */
@Data
public class CameraServiceLogQueryModel {

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 摄像机ip
     */
    private String cameraIp;
    /**
     * 工作面id
     */
    private String workFaceId;

    /**
     * 绑定开始时间
     */
    private Date bindStartTime;

    /**
     * 绑定结束时间
     */
    private Date bindEndTime;

    /**
     * 解绑开始时间
     */
    private Date unbindStartTime;

    /**
     * 解绑结束时间
     */
    private Date unbindEndTime;

}
