package jylink.cpds.serviceModel.params;

import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 审批通过参数模型
 */
@Data
public class ApprovalReasonEditModel {

    /**
     * 审批数据id
     */
    @Size(min = 36, max = 36)
    private String id;

    /**
     * 原因
     */
    @Size(max = 50)
    private String reason;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 状态
     */
    private String status;

    /**
     * 密码
     */
    private String password;

}
