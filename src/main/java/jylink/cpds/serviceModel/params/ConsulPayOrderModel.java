package jylink.cpds.serviceModel.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @DESCRIPTION: 专家会诊订单支付实体
 * @author: hanpt
 * @DATE: 2022/2/18 15:15
 */
@Data
@ApiModel
public class ConsulPayOrderModel {
    @NotBlank(message = "订单id不可为空")
    @ApiModelProperty(required = true,value = "订单id")
    private String orderId;
    /**
     * 实际支付的金额   单位 元
     */
    @NotNull(message = "支付金额不可为空")
    @ApiModelProperty(required = true,value = "实际支付的金额  单位 元")
    private Double payFee;




}
