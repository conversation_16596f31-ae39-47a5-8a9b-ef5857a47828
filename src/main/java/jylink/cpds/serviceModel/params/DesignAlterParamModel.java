package jylink.cpds.serviceModel.params;

import jylink.cpds.serviceModel.WorkType;
import jylink.cpds.serviceModel.dto.ApprovalWorkflowDto;
import jylink.cpds.serviceModel.dto.FileDto;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Data
public class DesignAlterParamModel {
    /**
     * 设计Id
     */
    @Size(min = 36,max = 36)
    private String tunnelId;

    /**
     * 工作面类型
     */
    //@NotNull
    private WorkType workType;

    /**
     * 煤层
     */
    private String coalSeam;

    /**
     * 预计探水次数
     */
    //private Integer explorationTimes;

    /**
     * 钻机型号
     */
    @NotBlank
    @Size(max = 30)
    private String machineType;

    /**
     * 巷道总长度
     */
    @NotNull
    private double tunnelDistance;

    /**
     * 方位角
     */
    private Double tunnelAzimuth;

    /**
     * 钻杆长度
     */
    @NotNull
    private double poleLength;

    /**
     * 变更原因
     */
    @NotNull
    private String alterReason;

    /**
     * 孔参数
     */
    @NotNull
    private List<HoleDesignParamModel> holes;

    /**
     * 审批流程数据
     */
    @NotNull
    private List<List<ApprovalWorkflowParamsModel>> flows;
    /**
     * 文档列表
     */
    private List<FileDto> documents;
    /**
     * 图片列表
     */
    private List<FileDto> pictures;

    /**
     * 获取扁平化的审批流程数据
     *
     * @return ApprovalWorkflowDto集合对象
     */
    public List<ApprovalWorkflowDto> getFlattenFlows() {
        List<ApprovalWorkflowDto> flowList = new ArrayList<>();
        int approvalOrder = 1;
        for (List<ApprovalWorkflowParamsModel> flowParams : flows) {
            int displayOrder = 1;
            for (ApprovalWorkflowParamsModel workFlowDto : flowParams) {
                ApprovalWorkflowDto dto = new ApprovalWorkflowDto();
                dto.setUserId(workFlowDto.getUserId());
                dto.setUserName(workFlowDto.getUserName());

                dto.setApprovalOrder(approvalOrder);
                dto.setDisplayOrder(displayOrder);
                flowList.add(dto);
                displayOrder++;
            }

            approvalOrder++;
        }

        return flowList;
    }
}
