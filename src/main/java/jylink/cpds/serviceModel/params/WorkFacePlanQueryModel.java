package jylink.cpds.serviceModel.params;

import java.util.Date;

import lombok.Data;


/**
 * 工作面计划表(WorkFacePlan)查询实体类
 *
 * <AUTHOR>
 * @since 2024-12-18 19:04:01
 */
@Data
public class WorkFacePlanQueryModel {

    /**
     * 主键id
     */
    private String id;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 扩展字段
     */
    private String extendedFields;

    /**
     * 备用字段
     */
    private String diyColumn;

    /**
     * 工作面ID
     */
    private String workFaceId;

    /**
     * 工作面名称
     */
    private String workName;

    /**
     * 年份
     */
    private Integer yearNum;

    /**
     * 季度
     */
    private Integer quarterOfYear;

    /**
     * 预计探水次数
     */
    private Integer expectNumber;

}
