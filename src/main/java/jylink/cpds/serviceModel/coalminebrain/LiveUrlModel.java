package jylink.cpds.serviceModel.coalminebrain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @DESCRIPTION: 视频实体
 * @author: hanpt
 * @DATE: 2021/11/1 17:36
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LiveUrlModel {
    @JsonProperty("flv")
    private String flv;
    @JsonProperty("hls")
    private String hls;
    @JsonProperty("img_url")
    private String imgUrl;
    @JsonProperty("rtmp")
    private String rtmp;
    @JsonProperty("rtsp_url")
    private String rtspUrl;
    @JsonProperty("http_flv")
    private String httpFlv;
}
