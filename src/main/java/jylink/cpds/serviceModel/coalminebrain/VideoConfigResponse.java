package jylink.cpds.serviceModel.coalminebrain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoConfigResponse {
    /**
     * 检测id
     */
    @JsonProperty("config_id")
    private String configId;

    /**
     * 煤矿大脑盒子/GPU的ip地址
     */
    @JsonProperty("end_ip")
    private String endIp;

    /**
     * 煤矿大脑盒子/GPU的位置信息
     */
    @JsonProperty("end_location")
    private String endLocation;

    /**
     * json流地址
     */
    @JsonProperty("json_url")
    private String jsonUrl;

    /**
     * 直播流地址 hls或rtmp
     */
    @JsonProperty("live_url")
    private Map<String, String> liveUrl;

    /**
     * 安装位置
     */
    private String location;

    /**
     * 模型名
     */
    @JsonProperty("model_name")
    private String modelName;

    /**
     * 原始视频流地址
     */
    @JsonProperty("orignal_url")
    private String orignalUrl;

    /**
     * 视频服务接口地址
     */
    @JsonProperty("video_service_interface")
    private String videoServiceInterface;

    /**
     * 视频服务地址
     */
    @JsonProperty("video_service")
    private String videoService;

    /**
     * 权重文件
     */
    @JsonProperty("weights_file")
    private String weightsFile;

    /**
     * 是否有可更新的模型版本，可更新为1，不可更新为-1
     */
    @JsonProperty("model_update_flag")
    private Integer modelUpdateFlag;

    /**
     * 是否有可更新的权重文件，可更新为1，不可更新为-1
     */
    @JsonProperty("model_weights_update_flag")
    private Integer modelWeightsUpdateFlag;
}
