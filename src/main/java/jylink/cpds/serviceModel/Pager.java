package jylink.cpds.serviceModel;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @param <T> 数据类型
 * <AUTHOR>
 * 分页模型
 */
@Data
@ApiModel
public class Pager<T> {

    /**
     * 总条数
     */
    @ApiModelProperty("总条数")
    private Long totalCount;

    /**
     * 数据集合
     */
    @ApiModelProperty("数据集合")
    List<T> list;

    public Pager(long total, List<T> dataList) {
        totalCount = total;
        list = dataList;
    }

}
