package jylink.cpds.serviceModel;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @Description: 专业设备文件类型
 * @date 2020/3/11 15:10
 * @version: V1.0
 */
public enum SpecialEquasFileTypeEnum {
    IMAGE("0","设计图","equs_picture"),
    FILE("1","文档资料","equs_file");
    private String type;
    private String name;
    private String value;

    SpecialEquasFileTypeEnum(String type, String name,String value) {
        this.type = type;
        this.name = name;
        this.value = value;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static String getValueByType(String type){
        SpecialEquasFileTypeEnum[] specialEquasFileTypeEnums = values();
        for (SpecialEquasFileTypeEnum specialEquasFileTypeEnum : specialEquasFileTypeEnums) {
            if (StringUtils.equals(specialEquasFileTypeEnum.getType(),type)) {
                return specialEquasFileTypeEnum.getValue();
            }
        }
        return "";
    }
}
