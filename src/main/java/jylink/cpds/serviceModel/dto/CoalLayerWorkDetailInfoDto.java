package jylink.cpds.serviceModel.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CoalLayerWorkDetailInfoDto {

    /**
     * 煤层key
     */
    private String coalLayerKey;

    /**
     * 煤层名称
     */
    private String coalLayerName;

    /**
     * 巷道长度
     */
    private Double tunnelDistance;

    /**
     * 工作面名称
     */
    private String workName;

    /**
     * 计划里程
     */
    private Double checkPosition;

    /**
     * 预计探水时间
     */
    private Date predictionDate;

    /**
     * 超前距离
     */
    private Double overDistance;

    /**
     * 允许掘进距离
     */
    private Double allowableSurvey;

    /**
     * 设计ID
     */
    private String tunnelId;

    /**
     * 工作面ID
     */
    private String workFaceId;

    /**
     * 剩余掘进距离
     */
    private Double surplusDistance;

    /**
     * 识别状态0未下发识别任务1正在识别2结束识别
     */
    private Integer status;

    /**
     * 结束识别时间
     */
    private Date endTime;

    private Integer checkStatus;

    private Date checkDate;

}
