package jylink.cpds.serviceModel.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jylink.cpds.serviceModel.WorkFlowStatus;
import jylink.cpds.serviceModel.WorkType;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 探水设计Dto
 */
@Data
public class TunnelDesignDto {
    /**
     * 数据Id
     */
    private String id;

    /**
     * 机构编码
     */
    @JsonIgnore
    private String orgCode;

    /**
     * 机构编码
     */
    private String orgName;

    /**
     * 工作面名称
     */
    private String workName;

    /**
     * 工作面编号
     */
    private String workCode;

    /**
     * 工作面类型
     */
    private WorkType workType;

    /**
     * 预计探水次数
     */
    private Integer explorationTimes;

    /**
     * 钻机型号
     */
    private String machineType;

    /**
     * 巷道总长度
     */
    private double tunnelDistance;

    /**
     * 方位角
     */
    private Double tunnelAzimuth;

    /**
     * 探测状态，是否已完成
     */
    private Boolean detectionStatus;

    /**
     * 钻杆长度
     */
    private double poleLength;

    /**
     * 是否已查看(上级单位)
     */
    private Boolean isViewed;

    /**
     * 状态
     */
    private WorkFlowStatus status;

    /**
     * 创建人Id
     */
    private String createUserId;

    /**
     * 提交审批人
     */
    private String approvalName;

    /**
     * 提交审批时间
     */
    private Date approvalTime;

    /**
     * 文档列表
     */
    private List<FileDto> documents;

    /**
     * 图片列表
     */
    private List<FileDto> pictures;

//    /**
//     * 物探报告列表
//     */
//    private List<FileDto> report;

    /**
     * 审批流程数据
     */
    private List<List<ApprovalWorkflowDto>> flows;
    private List<List<ApprovalUserDto>> userFlows;

    /**
     * 钻孔设计数据
     */
    private List<HoleDesignDto> designs;

    /**
     * 是否审批
     */
    private Boolean isAgree;
    /**
     * 变更id
     */
    private String alterId;
    /*
    * 变更按钮展示逻辑  0允许变更 1不允许变更
    * */
    private String changeFlag;

    /**
     * 煤层
     */
    private String coalSeam;

    /**
     * 工作面id
     */
    private String workFaceId;

    private String tunnelId;

}
