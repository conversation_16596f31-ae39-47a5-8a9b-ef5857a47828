package jylink.cpds.serviceModel.dto;

import jylink.cpds.serviceModel.DrainData;
import jylink.cpds.serviceModel.FullVideoItem;
import lombok.Data;

import java.util.List;

@Data
public class VideoItemDto {

    /**
     * 视频地址
     */
    private String videoUrl;
    /**
     * 视频id
     */
    private String videoId;
    /**
     * 视频地址,多段 视频返回
     */
    private List<FullVideoItem> videoUrlItems;

    /**
     * 标题
     */
    private String title;

    /**
     * 是否是HLS地址
     */
    private String mimeType;

    /**
     * 缩略图地址
     */
    private String poster;

    /**
     * 台账相关数据
     */
    private DrainData drainData;

    /**
     * 工作面名称
     */
    private String workName;

    /**
     * 工作面ID
     */
    private String workFaceId;

    /**
     * 子视频地址
     */
    private List<VideoDrillingDto> videoDrilling;

    /**
     * 通道号
     */
    private Integer channel;

    /**
     * 流媒体摄像机编码
     */
    private String indexCode;

    public VideoItemDto() {
        drainData = new DrainData();
    }

}
