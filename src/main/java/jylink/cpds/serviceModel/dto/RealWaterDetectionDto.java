package jylink.cpds.serviceModel.dto;

import jylink.cpds.serviceModel.Pager;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RealWaterDetectionDto {

    /**
     * 煤矿个数
     */
    private int mineNum;

    /**
     * 工作面个数
     */
    private int workFaceNum;

    /**
     * 计划个数
     */
    private int planNum;

    /**
     * 计划未探水
     */
    private int noAnalysisNum;

    /**
     * 正在识别计划个数
     */
    private int analysisNum;

    /**
     * 已完成探水
     */
    private int finishAnalysisNum;

    /**
     * 实时探水信息分页
     */
    private Pager<RealWaterDetectionDetailDto> realWaterDetectionDetailDtoPager;

    /**
     * 未下发探水任务分页
     */
    private Pager<NoAnalysisPlanListDto> noAnalysisPlanListDtoPager;

    /**
     * 已下发探水任务分页
     */
    private Pager<DrillPlanListDto> drillPlanListDtoPager;

    /**
     * 最近下发的
     */
    private DrillPlanListDto drillPlanListDto;

}
