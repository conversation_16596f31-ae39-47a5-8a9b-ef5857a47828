package jylink.cpds.serviceModel.dto;

import lombok.Data;

import java.util.List;

@Data
public class CoalRockSegmentationDto {

    /**
     * 孔id
     */
    private String id;

    /**
     * 孔作业id
     */
    private String holeDetailId;

    /**
     * 孔号
     */
    private String holeNo;

    /**
     * 方位角
     */
    private Double azimuth;

    /**
     * 倾角
     */
    private Double obliquity;

    /**
     * 孔深
     */
    private Integer holeDistance;

    /**
     * 杆数
     */
    private Integer poleNumber;

    /**
     * 杆长
     */
    private Double poleLength;

    /**
     * 异常位置
     */
    private Integer abnormalLocation;

    /**
     * 异常类型
     */
    private Integer abnormalType;

    /**
     * 异常范围模型
     */
    private List<AbNormalLimitDto> abNormalLimitDtos;
}
