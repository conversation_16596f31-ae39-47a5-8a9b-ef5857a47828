package jylink.cpds.serviceModel.dto;


import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * 探放台账修改所需要Dto模型
 */
@Data
public class UpdateDrainAccountDto {
    /**
     * 数据Id
     */
    private String id;

    /**
     * 探水设计主键
     */
    private String tunnelId;

    /**
     * 探水计划主键
     */
    private String checkPlanId;

    /**
     * 工作面名称
     */
    private String workName;

    /**
     * 工作面编码
     */
    private String workCode;

    /**
     * 探水里程
     */
    private Double surveyWaterMileage;

    /**
     * 探水时间
     */
    private Date surveyWaterDate;
}
