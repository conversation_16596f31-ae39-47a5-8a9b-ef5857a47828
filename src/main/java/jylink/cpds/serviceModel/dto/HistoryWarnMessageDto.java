package jylink.cpds.serviceModel.dto;

import jylink.cpds.serviceModel.MessageRankType;
import lombok.Data;

import java.util.Date;
@Data
public class HistoryWarnMessageDto {

    /**
     * 主键id
     */
    private String id;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 设计id
     */
    private String tunnelId;

    /**
     * 工作面名称
     */
    private String workName;

    /**
     * 探水里程
     */
    private Double surveyWaterMileage;

    /**
     * 消息标题
     */
    private String messageTitle;

    /**
     * 消息内容
     */
    private String messageContent;

    /**
     * 关联id
     */
    private String dataId;

    /**
     * 消息级别
     */
    private String messageLevel;

    /**
     * 消息类型
     */
    private Integer messageType;

    /**
     * 消息类型描述
     */
    private String messageTypeName;

    /**
     * 触发人id
     */
    private String triggerPersonId;

    /**
     * 触发人姓名
     */
    private String triggerPersonName;

    /**
     * 触发时间
     */
    private Date triggerTime;

    /**
     * 是否取消
     */
    private boolean isCancel;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 取消人id
     */
    private String cancelPersonId;

    /**
     * 取消人姓名
     */
    private String cancelPersonName;

    /**
     * 警告消息id
     */
    private String warnMessageId;
    /**
     * 消息等级（0一级，1二级，2三级，3四级）
     */
    private MessageRankType messageRank;

    /**
     * 市
     */
    private String mineCityzoneName;
    /**
     * 县
     */
    private String zoneCountyIdName;

    /**
     * 处置方式
     */
    private String handingMethod;

    /**
     * 回复状态
     */
    private Integer replyFlag;

    /**
     * 报警督办（详情）
     */
    private String messageDetail;

    /**
     * 跳转地址
     */
    private String extendFields;

    private boolean needJump;

    private String skipUrl;

    /**
     * 持续时间（秒）
     */
    private long duration;

}
