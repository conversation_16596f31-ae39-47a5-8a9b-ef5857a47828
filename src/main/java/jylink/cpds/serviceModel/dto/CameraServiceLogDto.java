package jylink.cpds.serviceModel.dto;

import lombok.Data;

import java.util.Date;

/**
 * @Author: hupf
 * @Data:2023/6/8 14:41
 * @Description: 摄像机服务日志Dto
 */
@Data
public class CameraServiceLogDto {
    /**
     * 工作面id
     */
    private String workFaceId;

    /**
     * 工作面名称
     */
    private String workName;

    /**
     * 摄像机ip
     */
    private String cameraIp;

    /**
     * 绑定时间
     */
    private Date bindTime;

    /**
     * 绑定用户id
     */
    private String bindUserId;

    /**
     * 绑定用户
     */
    private String bindUserName;

    /**
     * 解绑用户id
     */
    private String unbindUserId;

    /**
     * 解绑用户
     */
    private String unbindUserName;

    /**
     * 解绑时间
     */
    private Date unbindTime;

    /**
     * 使用时长
     */
    private String usageDuration;

}
