package jylink.cpds.serviceModel.dto;

import lombok.Data;

import java.util.List;

@Data
public class OrgGeophysicalDto {

    /**
     * 物探正常数
     */
    private Integer normalNum;

    /**
     * 物探异常次数
     */
    private Integer abNormalNum;

    /**
     *
     */
    private Integer total;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 物探信息实体
     */
    private List<OrgGeophysicalDto> orgGeophysicalDtoList;

}
