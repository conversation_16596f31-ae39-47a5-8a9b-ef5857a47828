package jylink.cpds.serviceModel.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 三专台账实体
 */
@Data
@ApiModel
public class ThreeSpecialtyDto {
    /**
     * 机构code
     */
    @ApiModelProperty(required = false,notes = "机构编码")
    private String orgCode;
    /**
     * 机构名称
     */
    @ApiModelProperty(required = false,notes = "机构名称")
    private String orgName;
    /**
     * 水文地质类型
     */
    @ApiModelProperty(required = false,notes = "水文地质类型")
    private String hydrologyType;
    private String hydrologyCode;
    /**
     * 工作面数量
     */
    @ApiModelProperty(required = false,notes = "工作面数量")
    private String workNum;
    /**
     * 专用探水设备
     */
    @ApiModelProperty(required = false,notes = "专用探水设备")
    private String equsNum;
    /**
     * 防治水副总
     */
    @ApiModelProperty(required = false,notes = "防治水副总")
    private String deputyGeneral;
    /**
     * 防治水副总Id
     */
    @ApiModelProperty(required = false,notes = "防治水副总Id")
    private String deputyGeneralId;
    /**
     * 地质类专业技术人员标准人数
     */
    @ApiModelProperty(required = false,notes = "地质类专业技术人员标准人数")
    private String geologyStandardPers;
    /**
     * 地质类专业技术人员实际人数
     */
    @ApiModelProperty(required = false,notes = "地质类专业技术人员实际人数")
    private String geologyActualPers;
    /**
     * 探放水人员标准人数
     */
    @ApiModelProperty(required = false,notes = "探放水人员标准人数")
    private String waterExplorationStandardPers;
    /**
     * 探放水人员实际人数
     */
    @ApiModelProperty(required = false,notes = "探放水人员实际人数")
    private String waterExplorationActualPers;

    private String leaderNum;

    /**
     * 生产能力
     */
    @ApiModelProperty(required = false,notes = "生产能力")
    private double productivePower;

    /**
     * 市级上级名称
     */
    @ApiModelProperty(required = false,notes = "市级上级名称")
    private String mineCityzoneName;

    /**
     * 县级上级名称
     */
    @ApiModelProperty(required = false,notes = "县级上级名称")
    private String zoneCountyIdName;

    /**
     * 设备是否满足
     */
    @ApiModelProperty(required = false,notes = "设备是否满足")
    private Integer equsFlag;

    /**
     * 技术人员是否满足
     */
    @ApiModelProperty(required = false,notes = "技术人员是否满足")
    private Integer geologyFlag;

    /**
     * 探放水特工是否满足
     */
    @ApiModelProperty(required = false,notes = "探放水特工是否满足")
    private Integer waterExplorationFlag;

}
