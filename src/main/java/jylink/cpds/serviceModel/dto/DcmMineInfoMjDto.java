package jylink.cpds.serviceModel.dto;

import java.util.Date;
import lombok.Data;


@Data
public class DcmMineInfoMjDto {
    /**
    * 主键id
    */
    private String id;
    

    private String corpId;
        

    private String uniformSocialCreditCode;
        

    private String corpName;
        

    private String corpNameAlias;
        

    private String address;
        

    private String cmpiSmallGroupName;
        

    private String mineProvzoneCode;
        

    private String mineProvzoneName;
        

    private String mineCityzoneCode;
        

    private String mineCityzoneName;
        

    private String zoneCountyIdCode;
        

    private String zoneCountyIdName;
        

    private String suozaiQuxian;
        

    private String parentCode;
        

    private String parentName;
        

    private String cmpiSafetysupdepartName;
        

    private String parentTypeCode;
        

    private String parentTypeName;
        

    private String xkzStatus;
        

    private String legalName;
        

    private String legalTel;
        

    private String contacts;
        

    private String tel;
        

    private String email;
        

    private String postCode;
        

    private String corpTypeCode;
        

    private String corpTypeName;
        

    private String economyTypeCode;
        

    private String economyTypeName;
        

    private String standardClassCode;
        

    private String standardClassName;
        

    private Object promitMkgwwZdcs;
        

    private String approvedMineDepth;
        

    private String mineStatusCode;
        

    private String mineStatusName;
        

    private String mineClassName;
        

    private String mineClassCode;
        

    private Object designOutput;
        

    private Object pr0ved0utput;
        

    private String primitKaicaisxMheight;
        

    private String mineMinestyleCode;
        

    private String mineMinestyleName;
        

    private String cmpiCstarttime;
        

    private String productDate;
        

    private String mineMinetypeName;
        

    private String mineMinetypeCode;
        

    private String mineTransmitstyleCode;
        

    private String mineTransmitstyleName;
        

    private String minePowerstyleCode;
        

    private String minePowerstyleName;
        

    private String mineVentilatestyleName;
        

    private String mineVentilatestyleCode;
        

    private String mineTransmitstyleDesc;
        

    private String minePowerstyleDesc;
        

    private String mineVentilatestyleDesc;
        

    private String mineGeohazardtypeName;
        

    private String mineGeohazardtypeCode;
        

    private String kjHydrogeologicalTypeCode;
        

    private String kjHydrogeologicalTypeName;
        

    private String hydrogeologicalTypeCode;
        

    private String hydrogeologicalTypeName;
        

    private String mineWaterburst;
        

    private String mineWaterburstMax;
        

    private String mineWsGradeCode;
        

    private String mineWsGradeName;
        

    private String mineFireCode;
        

    private String mineFireName;
        

    private String mineFireDesc;
        

    private String grimeExplosiveDesc;
        

    private String grimeExplosiveCode;
        

    private String grimeExplosiveName;
        

    private String rockburstCode;
        

    private String rockburstName;
        

    private String rfLith0l0gy;
        

    private String trafficCondition;
        

    private String cmpiRange;
        

    private String adjacentMine;
        

    private String cmpiMarea;
        

    private String geologicalSituation;
        

    private String ajzjwebUpdateTime;
        

    private Date updateTime;
        

    private String coalSupervisionBureau;
        

    private Object zhuwellLongitude;
        

    private Object zhuwellLatitude;
        

    private Object zhuwellAngle;
        

    private String safemonitorSystemCode;
        

    private String safemonitorSystemName;
        

    private String gaspmpSystemCode;
        

    private String gaspmpSystemName;
        

    private String cmpiPyabbreviation;
        

    private String cmpiUname;
        

    private String cmpiSroup;
        

    private String cmpiSafetyadmindepartCode;
        

    private String cmpiSafetyadmindepartName;
        

    private String cmpiSafetysupdepartCode;
        

    private String cmpiObphoto;
        

    private String cmpiMmanagerphoto;
        

    private String cmpiTlocationmap;
        

    private String cmpiCmdunit;
        

    private Date cmpiWconstime;
        

    private String cmpiOphone;
        

    private String cmpiSphone;
        

    private String cmpiSfaxcall;
        

    private String cmpiDisdirname;
        

    private String cmpiDsophone;
        

    private String cmpiIpointcoor;
        

    private Object cmpiResourceres;
        

    private Object cmpiResource;
        

    private String cmpiMoverlimitCode;
        

    private String cmpiMoverlimitName;
        

    private Object cmpiMheight;
        

    private Object cmpiGelevation;
        

    private String cmpiMhorizontal;
        

    private Object cmpiMdndepth;
        

    private String cmpiMcoalseam;
        

    private String cmpiSscoal;
        

    private Object cmpiAolyear;
        

    private Object cmpiCcyoutput;
        

    private Integer cmpiWellsnum;
        

    private Integer cmpiVsnumber;
        

    private Integer cmpiRairsnumber;
        

    private Integer cmpiCseamsnum;
        

    private String cmpiMmcoalseam;
        

    private Object cmpiTmairintake;
        

    private Object cmpiTrairvolume;
        

    private String cmpiWname;
        

    private String cmpiMwlmethod;
        

    private String cmpiAwlmethod;
        

    private Object cmpiEmpower;
        

    private String cmpiLctype;
        

    private String cmpiTwirerope;
        

    private Object cmpiWrrdiameter;
        

    private Object cmpiMwdepth;
        

    private String cmpiMlemodel;
        

    private Object cmpiAwdepth;
        

    private String cmpiVlemodel;
        

    private String cmpiAuxitamethod;
        

    private Object cmpiAgasemission;
        

    private Object cmpiRgasemission;
        

    private String cmpiGasabnormalarea;
        

    private Integer cmpiScperiod;
        

    private String cmpiHtemperatureCode;
        

    private String cmpiHtemperatureName;
        

    private String cmpiTwhazardCode;
        

    private String cmpiTwhazardName;
        

    private String cmpiWdwrteamCode;
        

    private String cmpiWdwrteamName;
        

    private String cmpiWdwremodel;
        

    private Date cmpiCwsgasaccepdate;
        

    private Integer cmpiHnumber;
        

    private Integer cmpiPhnumber;
        

    private Integer cmpiPronumareas;
        

    private Integer cmpiApronumareas;
        

    private Integer cmpiWfacenum;
        

    private Integer cmpiHfacenum;
        

    private Integer cmpiAcmfacenumber;
        

    private Integer cmpiPwfacenumber;
        

    private Integer cmpiFullmwfnumber;
        

    private String cmpiCmmpro;
        

    private String cmpiDmpro;
        

    private Object cmpiMmrate;
        

    private Object cmpiDmrate;
        

    private Integer status;
        

    private String dutyPersonName;
        

    private String dutyPersonTel;
        

    private String cmpiCityCode;
        

    private String cmpiCityName;
        

    private String cmpiCountyCode;
        

    private String cmpiCountyName;
        

    private String cmpiSmallGroupCode;
        

    private String assessLevelCode;
        

    private String assessLevelName;
        

    private String mineRiskGradeCode;
        

    private String mineRiskGradeName;
        

    private String minestatusComparelast;
        

    private String minenameLasttime;
        

    private String corpId09;
        

    private String affiliatedCity;
        

    private String affiliatedGroup;
        

    private String historicalCode;
        



}