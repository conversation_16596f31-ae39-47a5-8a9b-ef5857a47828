package jylink.cpds.serviceModel.dto;

import lombok.Data;

import java.util.List;

@Data
public class CityAbstractDto {

    /**
     * 市区有几个县市
     */
    private Integer countryNumber;

    /**
     * 煤矿个数
     */
    private Integer coalNumber;

    /**
     * 拥有煤矿的县市的名称
     */
    private String hasCoalCountryName;

    /**
     * 未拥有煤矿的县市的名称
     */
    private String noCoalCountryName;

    /**
     * 县级
     */
    private List<CountryAbstractDto> countryAbstractDtos;

    /**
     * 水文地质类型简单煤矿个数
     */
    private int simple;

    /**
     * 水文地质类型中等煤矿个数
     */
    private int secondary;

    /**
     * 水文地质类型复杂煤矿个数
     */
    private int complex;

    /**
     * 水文地质类型极复杂煤矿个数
     */
    private int extremelyComplex;

}
