package jylink.cpds.serviceModel.dto;

import lombok.Data;

import java.util.Date;

@Data
public class WorkFaceStatisticsDto {

    /**
     * 工作面id
     */
    private String workFaceId;

    /**
     * 工作面名称
     */
    private String workName;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县名称
     */
    private String countryName;

    /**
     * 预计探水次数
     */
    private int explorationTimes;

    /**
     * 实探
     */
    private int realNumber;

    /**
     * 探水状态
     */
    private String status;

    /**
     * 探水里程
     */
    private Double surveyWaterMileage;

    /**
     * 一季度预计
     */
    private long oneQuarterDesign;

    /**
     * 一季度实际
     */
    private long oneQuarterReal;

    /**
     * 二季度预计
     */
    private long twoQuarterDesign;

    /**
     * 二季度实际
     */
    private long twoQuarterReal;

    /**
     * 三季度预计
     */
    private long threeQuarterDesign;

    /**
     * 三季度实际
     */
    private long threeQuarterReal;

    /**
     * 四季度预计
     */
    private long fourQuarterDesign;

    /**
     * 四季度实际
     */
    private long fourQuarterReal;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 掘进状态
     */
    private int detectionStatus;

}
