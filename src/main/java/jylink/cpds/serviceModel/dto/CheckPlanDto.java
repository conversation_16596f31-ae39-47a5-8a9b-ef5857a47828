package jylink.cpds.serviceModel.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jylink.cpds.domain.CheckPlanUsers;
import jylink.cpds.serviceModel.DrillingItem;
import jylink.cpds.serviceModel.WorkFlowStatus;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 探水计划Dto
 */
@Data
public class CheckPlanDto {
    /**
     * 数据Id
     */
    private String id;

    /**
     * 机构编码
     */
    @JsonIgnore
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 巷道数据Id
     */
    private String tunnelId;

    /**
     * 巷道名称
     */
    private String workName;

    /**
     * 施工单位
     */
    private String workOrgName;

    /**
     * 探水地点
     */
    private String detectionPlace;

    /**
     * 探测位置
     */
    private Double checkPosition;

    /**
     * 预计探水时间
     */

    private Date predictionDate;

    /**
     * 公费标准
     */
    //private double feeScale;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否查看(监管单位)
     */
    private Boolean isViewed;

    /**
     * 状态
     */
    private WorkFlowStatus status;

    /**
     * 创建人Id
     */
    private String createUserId;

    /**
     * 提交审批人
     */
    private String approvalName;

    /**
     * 提交审批时间
     */
    private Date approvalTime;

    /**
     * 探水计划子表数据
     */
    private List<CheckPlanDetailDto> details;

    /**
     * 文件列表---三视图
     */
    private List<FileDto> files;
    /**
     * 施工组织附件
     */
    private List<FileDto> organizationFiles;
    /**
     * 技术交底附件
     */
    private List<FileDto> technicalFile;

    /**
     * 物探报告
     */
    private List<FileDto> report;

    /**
     * 审批流程数据
     */
    //@JsonIgnore
    private List<List<ApprovalWorkflowDto>> flows;
    private List<List<ApprovalUserDto>> userFlows;

    /**
     * 是否审批
     */
    private Boolean isAgree;
    /**
     * 施工单位Id
     */
    private String workOrgId;
    /**
     * 施工班次Id
     */
    private String classId;
    /**
     * 施工班次
     */
    private String classNoName;
    /**
     * 施工人员
     */
    private List<CheckPlanUsers> planUsers;

    /**
     * 是否添加停止掘进通知单
     */
    private boolean isAddStopNotices;

    /**
     * 是否添加探水通知单
     */
    private boolean isAddSurveyNotices;

    /**
     * 是否第一次探水
     */
    private boolean isFirst;

    /**
     * 停止掘进通知单内容
     */
    private String stopNoticeContent;

    /**
     * 探水通知单内容
     */
    private String surveyNoticeContent;

    /**
     * 钻探目的
     */
    private DrillingItem drillingItem;

    /**
     * 是否加密钻孔
     */
    private Integer encryptedDrilling;

    /**
     * 是否钻探异常
     */
    private Integer drillingAnomaly;

    /**
     * 物探管理id
     */
    private String geophysicalId;

    /**
     * 备用字段
     */
    private String diyColumn;

}
