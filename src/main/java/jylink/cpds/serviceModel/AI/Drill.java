package jylink.cpds.serviceModel.AI;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class Drill {
    /**
     * 卸钻次序
     */
    @JSONField(name = "serial_num")
    private int serialNumber;

    /**
     * 在场人数
     */
    @JSONField(name = "person_num")
    private int numberOfPerson;

    /**
     * 卸钻开始时间
     */
    @JSONField(name = "start_time")
    public Date startTime;

    /**
     * 卸钻结束时间
     */
    @JSONField(name = "end_time")
    public Date endTime;

    /**
     * 视频地址
     */
    @JSONField(name = "cs_video_url")
    public String videoUrl;
}
