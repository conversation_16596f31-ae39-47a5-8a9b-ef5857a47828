package jylink.cpds.serviceModel.AI;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * 接受Ocr设备信息
 */
@Data
public class OcrMessage {
    /**
     * 机构编码
     */
    @JsonProperty("cs_mine_code")
    private String mineCode;

    /**
     *工作面编码id
     */
    @JsonProperty("cs_workface_id")
    private String workfaceId;

    /**
     * 探水里程
     */
    @JsonProperty("cs_workface_location")
    private String workfaceLocation;

    /**
     * 通道号
     */
    @JsonProperty("cs_channel_number")
    private String channelNumber;

    /**
     * 海康机构编码
     */
    @JsonProperty("hkOrgCode")
    private String hkOrgCode;

    /**
     * 孔号
     */
    @JsonProperty("cs_hole_number")
    private String holeNo;

    /**
     * 副孔号
     */
    @JsonProperty("cs_drill_code_attach")
    private String codeAttach;

    /**
     * 是否识别成功  若为识别成功，则为1，否则为0
     */
    private  String isSuccess;
}
