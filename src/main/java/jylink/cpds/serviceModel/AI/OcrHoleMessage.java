package jylink.cpds.serviceModel.AI;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 实时展示识别杆数
 */
@Data
public class OcrHoleMessage {

    /**
     * 机构编码
     */
    @JsonProperty("cs_mine_code")
    private String mineCode;

    /**
     *工作面编码id
     */
    @JsonProperty("cs_workface_id")
    private String workfaceId;

    /**
     * 探水里程
     */
    @JsonProperty("cs_workface_location")
    private String workfaceLocation;

    /**
     * 通道号
     */
    @JsonProperty("cs_channel_number")
    private String channelNumber;

    /**
     * 孔号
     */
    @JsonProperty("cs_hole_number")
    private String holeNumber;

    /**
     * 副孔号
     */
    @JsonProperty("cs_drill_code_attach")
    private String codeAttach;

    /**
     * 海康机构编码
     */
    @JsonProperty("hk_org_code")
    private String hkOrgCode;

    /**
     * 当前杆数
     */
    @JsonProperty("serial_num")
    private String poleNumber;
}
