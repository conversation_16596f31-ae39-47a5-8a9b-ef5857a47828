package jylink.cpds.serviceModel;

import java.util.Date;
import lombok.Data;


/**
 * (EnforcementBasis)查询实体类
 *
 * <AUTHOR>
 * @since 2020-09-22 17:13:43
 */
@Data
public class EnforcementBasisQueryModel  {
    /**
    * 主键id
    */
    private String id;
    
    /**
    * 执法类型
    */
    private String enforcementType;

    /**
    * 执法依据文件
    */
    private String enforcementDocument;

    /**
    * 执法依据文件key
    */
    private String enforcementDocumentKey;

    /**
    * 执法依据
    */
    private String enforcementBasis;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 排序
    */
    private Integer order;



}