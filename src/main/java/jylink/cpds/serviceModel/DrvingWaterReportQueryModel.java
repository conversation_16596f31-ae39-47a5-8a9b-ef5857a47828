package jylink.cpds.serviceModel;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description: 有掘必探累计掘进查看掘进汇报实体
 * @date 2020/5/13 11:40
 * @version: V1.0
 */
@Data
@ApiModel
public class DrvingWaterReportQueryModel {
    @ApiModelProperty(required = true,value = "工作面id")
    @NotBlank
    private String tunnelId;
    @ApiModelProperty(required = true,value = "机构编码")
    @NotBlank
    private String orgCode;
    @ApiModelProperty(required = true,value = "本次探水时间")
    @NotBlank
    private String nowDate;
    @ApiModelProperty(required = true,value = "下次探水时间")
    @NotBlank
    private String nextDate;

}
