package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.domain.MineCertPlus;
import jylink.cpds.service.*;
import jylink.cpds.serviceModel.*;
import jylink.cpds.serviceModel.dto.*;
import jylink.cpds.serviceModel.params.CoalMaterialQueryModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/managerAPP")
@Scope("prototype")
public class ManageAPPController extends BaseController {

    /**
     * 孔作业服务
     */
    @Autowired
    private IHoleDetailService holeDetailService;

    /**
     * 三专服务
     */
    @Autowired
    private IThreeSpecialtyService threeSpecialtyService;

    /**
     * 管理APP服务
     */
    @Autowired
    private IManagerAPPService service;

    /**
     * 材料服务
     */
    @Autowired
    private IMaterialCurrentService materialCurrentService;

    /**
     * 历史警告消息服务
     */
    @Autowired
    private IHistoryWarnMessageService historyWarnMessageService;

    /**
     * 探水计划服务
     */
    @Autowired
    private ICheckPlanService checkPlanService;

    /**
     * 探水计划服务
     */
    @Autowired
    private IDrvingWaterService drvingWaterService;

    /**
     * 物探管理服务
     */
    @Autowired
    private IGeophysicalReportService geophysicalReportService;

    /**
     * 煤矿服务
     */
    @Autowired
    private IDcmMineInfoMjService dcmMineInfoMjService;

    /**
     * 五证服务
     */
    @Autowired
    private IMineCertService mineCertService;

    /**
     * 专业的设备
     */
    @Autowired
    private ISpecialEqusService specialEqusService;

    /**
     * 根据水文地质类型获取行政图矿点
     * @param type  水文地质类型
     * @param orgName  机构名称
     * @return  查询结果
     */
    @GetMapping("/coalInfoByHyType/{type}")
    @SysLog("根据水文地质类型获取行政图矿点")
    public ActionResult getCoalInfoByHyType(@PathVariable String type, @RequestParam(name = "orgName",required = false) String orgName,
                                            @RequestParam("currentPage") int currentPage,@RequestParam("pageSize") int pageSize){
        return ok(dcmMineInfoMjService.getCoalInfoPager(type,orgName,currentPage,pageSize));
    }

    /**
     * 根据机构编码查询数据信息
     * @param orgCode  机构编码
     * @return  查询结果
     */
    @GetMapping("/getMineCert")
    @SysLog("获取五证信息")
    public ActionResult getMineCert(@RequestParam("orgCode") String orgCode) {
        List<MineCertPlus> mineCertDto = mineCertService.getByOrgCode(orgCode);
        return ok(mineCertDto);
    }

    /**
     * 专业的设备分页
     * @param orgCode  机构编码
     * @param currentPage  当前页
     * @param pageSize  页面大小
     * @return  查询结果
     */
    @GetMapping("/getNoGrouting")
    @SysLog("专业的设备查询分页")
    public ActionResult getNoGrouting(@RequestParam("orgCode") String orgCode, @RequestParam("currentPage") int currentPage,@RequestParam("pageSize") int pageSize) {
        Pager<SpecialEqusDto> pager = specialEqusService.getNoGroutingPager(orgCode, currentPage, pageSize);
        return ok(pager);
    }

    /**
     * 获取物探管理分页
     * @param orgName  机构名称
     * @param month  近几月
     * @param currentPage  当前页
     * @param pageSize  页面大小
     * @return  查询结果
     */
    @GetMapping("/getGeophysical")
    @SysLog("获取物探管理数据信息")
    public ActionResult getGeophysical(@RequestParam(required = false,name = "orgName") String orgName,@RequestParam(required = false,name = "month") Integer month,
                                       @RequestParam("currentPage") int currentPage,@RequestParam("pageSize") int pageSize) {
        Pager<OrgGeophysicalDto> dtoPager = geophysicalReportService.getGeophysical(orgName, month, currentPage, pageSize);
        return ok(dtoPager);
    }

    /**
     * 分页查询
     * @param currentPage  当前页
     * @param pageSize  页面大小
     * @param workName  设计id
     * @param result  物探结果
     * @return  查询结果
     */
    @GetMapping("/getGeophysicalPager")
    @SysLog("物探报告分页查询")
    public ActionResult getByOrgCode(@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize,
                                     @RequestParam("orgCode") String orgCode, @RequestParam(name = "workName", required = false) String workName,
                                     @RequestParam(name = "result", required = false) String result){
        Pager<GeophysicalReportDto> pager = geophysicalReportService.getOrgCode(orgCode, workName, result, currentPage, pageSize);
        return ok(pager);
    }

    @GetMapping("/getGeophysicalCount")
    @SysLog("获取物探管理数据信息")
    public ActionResult getGeophysicalCount(@RequestParam(required = false,name = "orgName") String orgName,@RequestParam(required = false,name = "month") Integer month) {
        OrgGeophysicalDto orgGeophysicalDto = geophysicalReportService.getGeophysicalCount(orgName, month);
        return ok(orgGeophysicalDto);
    }

    /**
     * 获取动态监测数据
     * @param currentPage  当前页
     * @param pageSize  页面大小
     * @param orgName  机构名称
     * @return  查询结果
     */
    @GetMapping("/getMonitoringInfo")
    @SysLog("获取动态监测数据")
    public ActionResult getMonitoringInfo(@RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize,
                                          @RequestParam(required = false,name = "orgName") String orgName) {
        Pager<MineInfoOrgDto> monitoringInfo = drvingWaterService.getMonitoringInfo(orgName, currentPage, pageSize);
        return ok(monitoringInfo);
    }

    /**
     * 获取计划审批通过的设计id
     * @param orgCode  机构编码
     * @return  查询结果
     */
    @GetMapping("/getTunnelDown")
    @SysLog("获取设计下拉接口")
    public ActionResult getTunnelDown(@RequestParam("orgCode") String orgCode) {
        List<ListItem> tunnelDown = checkPlanService.getTunnelDown(orgCode);
        return ok(tunnelDown);
    }

    @GetMapping("/getAnomalyInfo")
    @SysLog("获取异常分析分页数据")
    public ActionResult getAnomalyInfo(@RequestParam(name = "time",required = false) Integer time,@RequestParam(name = "handle", required = false) Boolean handle,
                                       @RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize) {
        Pager<AnomalyAnalysisDto> anomalyInfo = historyWarnMessageService.getAnomalyInfo(time, handle, currentPage, pageSize);
        return ok(anomalyInfo);
    }

    /**
     * 获取异常分析异常个数
     * @param orgCode  机构编码
     * @param startDate  开始时间
     * @param endDate  结束时间
     * @return  查询结果
     */
    @GetMapping("/getAnomalyNum")
    @SysLog("获取异常分析异常个数")
    public ActionResult getAnomalyNum(@RequestParam("orgCode") String orgCode,@RequestParam(name ="startDate", required = false) String startDate,
                                      @RequestParam(name ="endDate", required = false) String endDate) {
        AnomalyAnalysisDto anomalyNum = historyWarnMessageService.getAnomalyNum(orgCode, startDate, endDate);
        return ok(anomalyNum);
    }

    /**
     * 获取下属机构所有已上传未上传矿数
     * @return  查询结果
     */
    @GetMapping("/getMaterialInfo")
    @SysLog("获取资料管理")
    public ActionResult getMaterialInfo() {
        List<MaterialCoalInfoDto> materialInfo = service.getMaterialInfo();
        return ok(materialInfo);
    }

    /**
     * 获取每个煤矿材料上报情况信息
     * @param model  查询条件
     * @return  查询结果
     */
    @PostMapping("/getInfo")
    @SysLog("获取每个煤矿材料上报情况信息")
    public ActionResult getInfo(@RequestBody PagerParams<CoalMaterialQueryModel> model) {
        Pager<CoalInfoDto> info = service.getInfo(model);
        return ok(info);
    }

    /**
     * 根据机构编码查询资料数据信息
     * @param orgCode  机构编码
     * @param oneCode
     * @return
     */
    @GetMapping("/getMaterialView")
    @SysLog("根据机构编码查询材料信息")
    public ActionResult selByPage(@RequestParam("orgCode") String orgCode ,@RequestParam ("oneCode") String oneCode,@RequestParam(name = "fileName",required = false) String fileName) {
        MaterialViewDto messageDtos = materialCurrentService.getMaterialInfo(orgCode,oneCode,fileName);
        return json(messageDtos);
    }

    /**
     * 获取所有满足和不满足条件的矿数
     * @return  查询结果
     */
    @GetMapping("/getThreeSpecialInfo")
    @SysLog("获取三专数据信息")
    public ActionResult getThreeSpecialInfo() {
        ThreeSpecialNumberDto threeSpecialCount = threeSpecialtyService.getThreeSpecialCount();
        return ok(threeSpecialCount);
    }

    /**
     * 获取所有不满足条件的矿的三专数据信息
     * @param orgName  机构名称
     * @param type  类型
     * @param currentPage  当前页
     * @param pageSize  页大小
     * @param status 0--正常
     * @return  查询结果
     */
    @GetMapping("/getNoSatisfy")
    @SysLog("获取所有不满足条件的矿的三专数据信息")
    public ActionResult getNoSatisfy(@RequestParam(name = "orgName",required = false) String orgName,@RequestParam("type") Integer type,
                                     @RequestParam("currentPage") int currentPage,@RequestParam("pageSize") int pageSize,
                                     @RequestParam(name = "status",required = false) Integer status) {
        Pager<ThreeSpecialtyDto> pager = threeSpecialtyService.getNoSatisfy(orgName, type, currentPage, pageSize,status);
        return ok(pager);
    }

    @GetMapping("/getVerificationCount")
    @SysLog("获取相互验证信息")
    public ActionResult getVerificationCount() {
        VerificationCountDto verificationCount = checkPlanService.getVerificationCount();
        return ok(verificationCount);
    }

    /**
     * 获取相互验证煤矿信息
     * @param orgName  机构名称
     * @param sortType  排序方式 0,未验证次数倒序，1
     * @return  查询结果
     */
    @GetMapping("/getVerificationInfo")
    @SysLog("获取相互验证煤矿信息")
    public ActionResult getVerificationInfo(@RequestParam(name = "orgName" ,required = false) String orgName,@RequestParam("sortType") Integer sortType ,
                                            @RequestParam("currentPage") int currentPage,@RequestParam("pageSize") int pageSize) {
        Pager<VerificationCountDto> verificationInfo = checkPlanService.getVerificationInfo(orgName, sortType,currentPage,pageSize);
        return ok(verificationInfo);
    }

    /**
     *
     * @param orgCode
     * @param type
     * @param tunnelId
     * @param currentPage
     * @param pageSize
     * @return
     */
    @GetMapping("/getInfoByOrgCode/{orgCode}")
    @SysLog("获取相互验证煤矿信息")
    public ActionResult getInfoByOrgCode(@PathVariable("orgCode") String orgCode,@RequestParam("type") Integer type , @RequestParam(name = "tunnelId" ,required = false) String tunnelId,
                                         @RequestParam("currentPage") int currentPage,@RequestParam("pageSize") int pageSize) {
        Pager<VerificationPlanDto> verificationInfo = checkPlanService.getVerificationPlan(orgCode,type,tunnelId,currentPage,pageSize);
        return ok(verificationInfo);
    }

    /**
     *  数据联网情况
     * <AUTHOR>
     * @date 2021/1/20 10:59
     * @param
     * @return jylink.cpds.serviceModel.ActionResult
     */
    @GetMapping("/netWorkIng")
    @SysLog("数据联网情况")
    public ActionResult netWorkIng(){
        List<NetWorkingDTO> list = service.netWorkIng();
        return ok(list);
    }
    
    /**
     *  数据联网情况正常异常个数
     * <AUTHOR>
     * @date 2021/1/25 15:13
     * @param 
     * @return jylink.cpds.serviceModel.ActionResult
     */
    @GetMapping("/netWorkIngSituation")
    @SysLog("数据联网情况正常异常个数")
    public ActionResult netWorkIngSituation(){
        NetWorkingSituationDTO netWorkingSituationDTO = service.netWorkIngSituation();
        return ok(netWorkingSituationDTO);
    }

    /**
     *
     * @return
     */
    @GetMapping("/getOrgCode")
    @SysLog("获取机构编码")
    public ActionResult getTreeOrgCode() {
        return ok(service.getTreeOrgCode());
    }

    /**
     * 获取孔数据详情
     * @param holeDetailId  孔id
     * @return  查询结果
     */
    @GetMapping("/getHoleInfo")
    @SysLog("获取孔数据详情")
    public ActionResult getHoleInfo(@RequestParam("holeDetailId") String holeDetailId) {
        if (!holeDetailService.anyById(holeDetailId)) {
            return notFound(TfsMessageConstants.HOLE_DETAIL_ID_NOT_FOUND);
        }
        AcceptanceHoleDto holeInfo = service.getHoleInfo(holeDetailId);
        return ok(holeInfo);
    }

}
