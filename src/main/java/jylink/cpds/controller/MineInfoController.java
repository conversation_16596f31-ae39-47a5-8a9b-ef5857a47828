package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.config.CheckOrgCode;
import jylink.cpds.domain.MineInfo;
import jylink.cpds.service.IFileService;
import jylink.cpds.service.IMineInfoService;
import jylink.cpds.serviceModel.ActionResult;
import jylink.cpds.serviceModel.dto.EnforcementClassifyTwoDto;
import jylink.cpds.serviceModel.dto.MineInfoDto;
import jylink.cpds.serviceModel.dto.MineInfoOrgDto;
import jylink.cpds.serviceModel.dto.WorkFaceExpendDto;
import jylink.cpds.serviceModel.params.FileSaveParamsModel;
import jylink.cpds.serviceModel.params.MineInfoEditModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * 煤矿基本信息控制器
 */
@RestController
@RequestMapping("/api/mineinfo")
@Scope("prototype")
public class MineInfoController extends BaseController {

    /**
     * 文件服务
     */
    @Autowired
    private IFileService fileService;

    /**
     * 日志实例
     */
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 煤矿基本信息业务对象
     */
    @Autowired
    private IMineInfoService mineInfoService;

    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * 根据机构编码获取煤矿基本信息
     *
     * @return 煤矿基本信息数据
     */
    @GetMapping
    @SysLog("煤矿基本信息根据机构查询")
    public ActionResult getByOrgCode(@RequestParam("orgCode") String orgCode) {
        MineInfoDto dtoList = mineInfoService.getByOrgCode(orgCode);
        return json(dtoList);
    }

    @GetMapping("/singleWorkFaceInfo")
    public ActionResult createSingleWorkFaceInfoBy(
            @RequestParam("workFaceId") String workFaceId,
            @RequestParam("orgCode") String orgCode) {
        WorkFaceExpendDto singleWorkFaceInfoBy = mineInfoService.createSingleWorkFaceInfoBy(workFaceId, orgCode);
        return json(singleWorkFaceInfoBy);
    }

    @GetMapping("/singleMaterialInfo")
    public ActionResult createSingleMaterialInfoBy(
            @RequestParam("dataId") String dataId,
            @RequestParam("orgCode") String orgCode) {
        EnforcementClassifyTwoDto singleMaterialInfoBy = mineInfoService.createSingleMaterialInfoBy(dataId, orgCode);
        return json(singleMaterialInfoBy);
    }

    @GetMapping("/orgNameMatchQuery")
    @SysLog("煤矿基本信息根据机构名称关键字查询")
    public ActionResult orgNameMatchQuery(@RequestParam("keyWord") String keyWord,
                                          @RequestParam(value = "isAccess", required = false, defaultValue = "true") boolean isAccess) {

        try {
            Optional<List<MineInfoDto>> byOrgNameKeyWord = mineInfoService.getByOrgNameKeyWord(keyWord, isAccess);
            if (byOrgNameKeyWord.isPresent()) {
                return json(byOrgNameKeyWord.get());
            } else {
                return notFound("未找到匹配数据");
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
            return badRequest(e.getMessage());
        }

    }

    /**
     * 根据Id获取数据
     *
     * @param id 数据Id
     * @return 数据对象
     */
    @GetMapping("/{id}")
    @SysLog("煤矿基本信息根据id查询")
    public ActionResult getById(@PathVariable String id) {
        if (!mineInfoService.anyById(id, getOrgCode())) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }

        MineInfoDto dtoModel = mineInfoService.getById(id);
        return json(dtoModel);
    }

    /**
     * 添加数据
     *
     * @param model 参数实体对象
     * @return 添加结果
     */
    @PostMapping
    @SysLog("煤矿基本信息添加")
    public ActionResult post(@Validated @RequestBody MineInfoEditModel model) {
        MineInfoDto dtoModel = mapper.map(model, MineInfoDto.class, "mineInfoAddMap");
        dtoModel.setOrgCode(getOrgCode());
        dtoModel.setOrgName(getOrgName());
        if (mineInfoService.add(dtoModel)) {
            return json(HttpStatus.CREATED, "添加成功。");
        } else {
            return badRequest(TfsMessageConstants.ADD_FAIL);
        }
    }

    /**
     * 修改数据
     *
     * @param model 参数实体对象
     * @return 修改结果
     */
    @PutMapping
    @SysLog("煤矿基本信息修改")
    public ActionResult put(@Validated @RequestBody MineInfoEditModel model) {
        MineInfoDto dtoModel = mapper.map(model, MineInfoDto.class, "mineInfoEditMap");
        dtoModel.setOrgCode(getOrgCode());
        dtoModel.setOrgName(getOrgName());
        if (mineInfoService.update(dtoModel)) {
            return ok("修改成功。");
        } else {
            return badRequest("修改失败！");
        }
    }

    /**
     * 根据Id删除数据
     *
     * @param id 数据Id
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    @SysLog("煤矿基本信息删除")
    public ActionResult delete(@PathVariable String id) {
        boolean delResult = mineInfoService.delete(id);
        if (delResult) {
            return json(HttpStatus.NO_CONTENT, "删除成功。");
        } else {
            return badRequest("数据删除失败！");
        }
    }

    @GetMapping("/getAll")
    @SysLog("煤矿基本信息  获取全部机构信息")
    public ActionResult getAll(){
        String orgId = getOrgId();
        List<MineInfoOrgDto> mineInfoDtos = mineInfoService.getByOrgCodes(orgId);
        return ok(mineInfoDtos);
    }

    /**
     * 文件上传
     *
     * @param id    探水计划id
     * @param files 图片文件
     * @return Json提示信息
     */
    @PostMapping("/fileUpload/{id}")
    @SysLog("煤矿基本信息文件上传")
    @CheckOrgCode
    public ActionResult uploadFile(@PathVariable String id, @RequestParam("files") MultipartFile[] files, @RequestParam("module") String module) {
        //判断module参数是否正确
        if (!module.equals(MineInfo.PRODUCTION_GEOLOGICAL)) {
            return badRequest("module参数错误！");
        }
        for (MultipartFile multipartFile : files) {
            String fileName = multipartFile.getOriginalFilename();
            assert fileName != null;
            String fileType = fileName.substring(fileName.lastIndexOf("."));

            if (!".pdf".equals(fileType)&&!".PDF".equals(fileType)){
                return badRequest(TfsMessageConstants.FILE_FORMAT_ERROR);
            }
            try {
                byte[] fileBytes = multipartFile.getBytes();
                FileSaveParamsModel model = new FileSaveParamsModel();
                model.setDataId(id);
                model.setModuleName(module);
                model.setFileType(fileType);
                model.setOrgCode(getOrgCode());
                model.setFile(fileBytes);
                model.setFileName(fileName);
                model.setOrgName(getOrgName());

                boolean saveResult = fileService.saveFile(model);
                if (!saveResult) {
                    return badRequest("文件保存失败！");
                }
            } catch (Exception e) {
                logger.error(e.toString());
                return internalServerError("文件保存失败！");
            }
        }

        return ok("保存成功。");
    }


    /**
     * 删除文件数据
     * @param fileId 文件id
     * @return Json提示信息
     */
    @DeleteMapping("/fileDelete/{fileId}")
    @SysLog("删除煤矿基本信息文件数据")
    @CheckOrgCode
    public ActionResult deleteFile( @PathVariable String fileId) {

        if (!fileService.any(fileId)) {
            return notFound("该文件不存在！");
        }
        if (fileService.deleteById(fileId)) {
            return ok("文件删除成功。");
        }
        return badRequest("删除失败！");
    }

}
