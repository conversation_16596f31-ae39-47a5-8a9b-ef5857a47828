package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.service.*;
import jylink.cpds.serviceModel.*;
import jylink.cpds.serviceModel.dto.SummaryNoticeDto;
import jylink.cpds.serviceModel.params.*;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * 探水总结
 */
@RestController
@RequestMapping("/api/summarynotice")
@Scope("prototype")
public class SummaryNoticeController extends BaseController {
    /**
     * 探水设计模块服务
     */
    @Autowired
    private ISummaryNoticeService service;

    /**
     * 探水台账服务
     */
    @Autowired
    private IDrainAccountService accountService;

    /**
     * dozer 实例对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * 根据数据Id查询数据
     *
     * @param id 数据Id
     * @return 探水设计数据
     */
    @GetMapping("/{id}")
    @SysLog("探水总结表   根据数据Id查询数据")
    public ActionResult getById(@PathVariable String id) {
        if (!service.anyById(id,getOrgCode())) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        SummaryNoticeDto dto = service.getById(id, getOrgCode());
        return json(dto);
    }

    /**
     * 根据台账id获取数据
     *
     * @param drainId 台账id
     * @return Json格式数据
     */
    @GetMapping
    @SysLog("探水总结表   根据台账id获取数据")
    public ActionResult getByDrainId(@RequestParam("drainId") String drainId) {
        List<SummaryNoticeDto> dtoList = service.getByDrainId(drainId, getOrgCode());
        return json(dtoList);
    }

    /**
     * 添加数据
     *
     * @param models 实体对象集合
     * @return Json提示信息
     */
    @PostMapping("/{drainId}")
    @SysLog("探水总结表   添加")
    public ActionResult add(@PathVariable("drainId") String drainId,
                            @Validated @RequestBody SummaryNoticeAddModel models) {
        if (!accountService.anyById(drainId)) {
            return notFound("探水台账Id错误！");
        }
        //查询是否存在数据
/*      if (service.queryCountByDrainId(drainId, getOrgCode()) > 0)
            return badRequest("不可重复添加数据！");*/
        //判断集合是否为空
        if (models == null ) {
            return badRequest("数据不可为空！");
        }
        //判断数据条数是否和验收表相同
        /*long acceptCount = acceptanceCheckService.getCount(drainId, getOrgCode());
        if (acceptCount != models.size())
            return badRequest("数据条数和验收钻孔数量不符！");*/

        SummaryNoticeDto dto = mapper.map(models, SummaryNoticeDto.class);

            dto.setOrgCode(getOrgCode());
            dto.setOrgName(getOrgName());


        if (service.add(drainId, dto)) {
            return created("添加成功。");
        }

        return badRequest(TfsMessageConstants.ADD_FAIL);
    }

    /**
     * 修改数据
     *
     * @param drainId 探水台账Id
     * @param model  实体对象
     * @return Json提示信息
     */
    @PutMapping("/{drainId}")
    @SysLog("探水总结表   修改")
    public ActionResult update(@PathVariable String drainId,
                               @Validated @RequestBody SummaryNoticeEditModel model) {
        if (!accountService.anyById(drainId)) {
            return notFound("台账Id错误！");
        }

        SummaryNoticeDto dto = mapper.map(model, SummaryNoticeDto.class);

        boolean updateResult = service.update(dto);
        if (updateResult) {
            return ok("修改成功。");
        }
        return badRequest("数据修改失败！");
    }

    /**
     * 数据上报
     *
     * @param id 数据Id
     * @return Json提示信息
     */
    @PutMapping("/upload/{id}")
    @SysLog("探水总结表   数据上报")
    public ActionResult upload(@PathVariable String id) {
        if (!service.anyById(id,getOrgCode())) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        //修改数据上报状态
        boolean updateRes = service.upload(id, LocalDateTime.now().toDate());
        if (updateRes) {
            return json(id, "上报成功。");
        }
        return badRequest("数据上报失败！");
    }

    /**
     * 删除数据
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @SysLog("探水总结表   删除")
    public ActionResult delete(@PathVariable String id) {
        boolean any = service.anyById(id, getOrgCode());
        if (!any) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        boolean delResult = service.delete(id, getOrgCode());
        if (delResult) {
            return ok("数据删除成功。");

        } else {
            return badRequest("数据删除失败！");
        }
    }
}
