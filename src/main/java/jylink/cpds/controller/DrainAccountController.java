package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.api.entity.McMessageWarnDTO;
import com.ada.jykjcloudx.sdk.api.service.MessageWarnService;
import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.config.CheckOrgCode;
import jylink.cpds.domain.DrainAccount;
import jylink.cpds.service.*;
import jylink.cpds.serviceModel.ActionResult;
import jylink.cpds.serviceModel.ListItem;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.WorkFlowStatus;
import jylink.cpds.serviceModel.dto.*;
import jylink.cpds.serviceModel.params.DrainAccountAddModel;
import jylink.cpds.serviceModel.params.DrainAccountEditModel;
import jylink.cpds.serviceModel.params.FileSaveParamsModel;
import org.apache.ibatis.annotations.Param;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 探水台账控制器
 */
@RestController
@RequestMapping("/api/drainaccount")
@Scope("prototype")
public class DrainAccountController extends BaseController {

    /**
     * 文件服务
     */
    @Autowired
    private IFileService fileService;
    /**
     * 探水台账服务
     */
    @Autowired
    private IDrainAccountService drainAccountService;

    /**
     * 探水设计模块服务
     */
    @Autowired
    @Qualifier("tunnelDesignService")
    private ITunnelDesignService tunnelDesignService;

    /**
     * 探水计划service
     */
    @Autowired
    @Qualifier("checkPlanService")
    private ICheckPlanService checkPlanService;
    /**
     *
     */
    @Autowired
    private ICheckPlanUsersService checkPlanUsersService;

    /**
     * 探水计划子表service
     */
    @Autowired
    private ICheckPlanDetailService checkPlanDetailService;

    /**
     * 钻孔验收表service
     */
    @Autowired
    private IAcceptanceCheckService acceptanceCheckService;

    /**
     * dozer 实例对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * 平台消息服务
     */
    @Autowired
    private MessageWarnService messageWarnService;

    /**
     * 日志实例
     */
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 保存煤层倾角参数
     *
     * @return 是否保存成功
     */
    @GetMapping("/saveCoalAngel")
    @SysLog("保存煤层倾角")
    public ActionResult saveCoalAngel(@RequestParam("coalAngel") String coalAngel, @RequestParam("drainId") String drainId) {
        DrainAccountDto drainAccount = drainAccountService.getById(drainId);
        if (drainAccount == null) {
            return badRequest(TfsMessageConstants.ID_NOT_FOUND);
        }
        if (coalAngel.isEmpty()) {
            return json("煤层倾角不能为空！");
        }
        drainAccount.setCoalAngle(Double.parseDouble(coalAngel));
        boolean update = drainAccountService.updateCoalAngle(drainAccount);
        if (update) {
            return ok("更新成功！");
        } else {
            return badRequest("更新失败！");
        }
    }


    /**
     * 根据机构编码获取数据
     *
     * @return Json格式数据
     */
    @GetMapping
    @SysLog("根据orgCode获取台账数据")
    public ActionResult getByOrgCode() {
        List<DrainAccountDto> dtoList = drainAccountService.getOrgCode(getOrgCode());
        return json(dtoList);
    }

    /**
     * 根据数据Id查询数据
     *
     * @param id 数据Id
     * @return 探水设计数据
     */
    @GetMapping("/{id}")
    @SysLog("根据ID获取台账数据")
    public ActionResult getById(@PathVariable String id) {
        if (!drainAccountService.anyById(id)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        DrainAccountByIdDto drainAccountByIdDto = drainAccountService.getDetail(id);
        return json(drainAccountByIdDto);
    }


    /**
     * 根据数据Id查询 修改所需要的数据
     *
     * @param id 数据Id
     * @return 探水设计数据
     */
    @GetMapping("/updatemessage/{id}")
    @SysLog("修改台账数据")
    public ActionResult getUpdateMessage(@PathVariable String id) {
        if (!drainAccountService.anyById(id)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        UpdateDrainAccountDto dto = drainAccountService.getUpdateMessage(id, getOrgCode());
        return json(dto);
    }

    /**
     * 根据计划id查询数据
     *
     * @param checkPlanId 数据Id
     * @return 探水设计数据
     */
    @GetMapping("/select/{checkPlanId}")
    @SysLog("根据计划ID查询台账数据")
    public ActionResult getMessageByCheckPlanId(@PathVariable String checkPlanId) {
        DrainAccount drainAccount = drainAccountService.getByCheckPlanId(checkPlanId, getOrgCode());
        DrainAccountDto drainAccountDto = new DrainAccountDto();
        if (drainAccount != null) {
            drainAccountDto = mapper.map(drainAccount, DrainAccountDto.class);
            return json(drainAccountDto);
        }
        return json(null);
    }

    /**
     * 根据数据Id查询对应工作面名称
     *
     * @param id 数据Id
     * @return 探水设计数据
     */
    @GetMapping("/workName/{id}")
    @SysLog("根据台账ID查询工作面名称")
    public ActionResult getWorkName(@PathVariable String id) {
        if (!drainAccountService.anyById(id)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }

        String dto = drainAccountService.getWorkName(id, getOrgCode());
        return json(dto);
    }

    /**
     * 数据查询
     *
     * @param currentPage 当前页码
     * @param pageSize    显示行数
     * @param workName    工作面名称
     * @param startTime   查询年份
     * @param status      数据状态
     * @param endTime     是否完成台账填写
     * @return Json数据
     */
    @GetMapping("/find")
    @SysLog("分页查询台账数据")
    public ActionResult get(@RequestParam("currentPage") int currentPage,
                            @RequestParam("pageSize") int pageSize,
                            @RequestParam(name = "workName", required = false) String workName,
                            @RequestParam(name = "startTime", required = false) String startTime,
                            @RequestParam(name = "endTime", required = false) String endTime,
                            @RequestParam(name = "status", required = false) String status) {
        Integer flowStatus;
        // 判断状态是否为空
        if (status == null || status.isEmpty()) {
            flowStatus = null;
        } else {
            flowStatus = WorkFlowStatus.valueOf(status).getValue();
        }

        Pager<DrainAccountDto> dtoList = drainAccountService.getByParams(getOrgCode(), workName, startTime, endTime, flowStatus,
                currentPage, pageSize);
        return json(dtoList);
    }

    /**
     * 获取钻孔分析列表
     *
     * @param currentPage
     * @param pageSize
     * @param workName
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/acceptanceds")
    @SysLog("获取钻孔分析列表")
    public ActionResult getDrainAccounts(@RequestParam("currentPage") int currentPage,
                                         @RequestParam("pageSize") int pageSize,
                                         @RequestParam(name = "workName", required = false) String workName,
                                         @RequestParam(name = "startTime", required = false) String startTime,
                                         @RequestParam(name = "endTime", required = false) String endTime) {
        Pager<DrainAccountDto> dtoList = drainAccountService.getByParams(getOrgCode(), workName, startTime, endTime, WorkFlowStatus.Acceptance.getValue(),
                currentPage, pageSize);

        return json(dtoList);
    }

    /**
     * 根据机构编码查询所有工作面名称
     *
     * @return 该机构下的所有工作面名称
     */
    @GetMapping("/workname")
    @SysLog("根据机构编码查询所有工作面名称")
    public ActionResult getWorkName() {
        List<String> dtoList = drainAccountService.getWorkName(getOrgCode());
        return json(dtoList);
    }

    /**
     * 根据机构编码查询所有探水时间
     *
     * @return 该机构下的所有探水时间
     */
    @GetMapping("/date")
    @SysLog("根据机构编码查询所有探水时间")
    public ActionResult getSurveyWaterDate() {
        List<String> dtoList = drainAccountService.getSurveyWaterDate(getOrgCode());
        return json(dtoList);
    }

    /**
     * 根据机构编码查询所有探水时间
     *
     * @return 该机构下的所有探水时间
     */
    @GetMapping("/surveywatermileage")
    @SysLog("根据工作面名称机构编码获取探水里程列表")
    public ActionResult getSurveyWaterMileage(@RequestParam(name = "workName", required = false) String workName) {
        List<Integer> dtoList = drainAccountService.getSurveyWaterMileage(getOrgCode(), workName);
        return json(dtoList);
    }

    /**
     * 获取探水里程下拉列表
     *
     * @param tunnelId 设计Id
     * @return json数据
     */
    @GetMapping("/dropdown/{tunnelId}")
    @SysLog("获取探水里程下拉列表")
    public ActionResult getDropDownList(@PathVariable("tunnelId") String tunnelId) {
        List<DrainAccountDto> accounts = drainAccountService.getByTunnelId( tunnelId,null);
        List<ListItem> items = accounts.stream()
                .sorted(Comparator.comparing(DrainAccountDto::getSurveyWaterMileage).reversed())
                .map(a -> new ListItem(String.valueOf(a.getSurveyWaterMileage()),
                        String.valueOf(a.getSurveyWaterMileage())))
                .collect(Collectors.toList());
        return ok(items);

    }

    /**
     * 获取探水里程下拉列表
     *
     * @param workFaceId 工作面Id
     * @return json数据
     */
    @GetMapping("/getMileageDownList")
    @SysLog("获取探水里程下拉列表")
    public ActionResult getDropDownList(@Param("workFaceId") String workFaceId,@Param("orgCode") String orgCode,@RequestParam(required = false,name = "cameraIp") String cameraIp) {
        TunnelDesignDto tunnelDesignDto = tunnelDesignService.getByWorkFaceId(workFaceId, orgCode);
        List<ListItem> items = new ArrayList<>();
        if(tunnelDesignDto!=null){
            List<DrainAccountDto> accounts = drainAccountService.getByTunnelId( tunnelDesignDto.getId(),cameraIp);
            items = accounts.stream()
                    .sorted(Comparator.comparing(DrainAccountDto::getSurveyWaterMileage).reversed())
                    .map(a -> new ListItem(String.valueOf(a.getId()),
                            String.valueOf(a.getSurveyWaterMileage())))
                    .collect(Collectors.toList());
        }
        return ok(items);
    }

    /**
     * 添加探水台账
     *
     * @param model 参数模型
     * @return Json提示信息
     */
    @PostMapping
    @SysLog("添加台账数据")
    public ActionResult add(@Validated @RequestBody DrainAccountAddModel model) {
        if (!tunnelDesignService.anyById(model.getTunnelId(), getOrgCode())) {
            return notFound("探水设计Id错误！");
        }
        if (!checkPlanService.anyById(model.getCheckPlanId(), getOrgCode())) {
            return notFound("探水计划Id错误！");
        }
        // 探水里程唯一校验
        if (drainAccountService.anyBySurveyWaterMileage(model.getTunnelId(), model.getSurveyWaterMileage(),
                getOrgCode())) {
            return conflict("探水里程已经存在！");
        }
        // 探水时间唯一校验
        if (drainAccountService.anyBySurveyWaterDate(model.getTunnelId(), getOrgCode(), model.getSurveyWaterDate())) {
            return conflict("探水时间重复");
        }
        DrainAccountDto dto = mapper.map(model, DrainAccountDto.class);
        String uuid = UUID.randomUUID().toString();
        dto.setId(uuid);
        dto.setOrgCode(getOrgCode());
        dto.setOrgName(getOrgName());
        boolean ad = drainAccountService.add(dto);
        if (!ad) {
            return badRequest(TfsMessageConstants.ADD_FAIL);
        }

        return created(uuid, "添加成功。");
    }

    /**
     * 修改数据
     *
     * @param model 实体对象
     * @return Json提示信息
     */
    @PutMapping
    @SysLog("修改台账数据")
    public ActionResult update(@Validated @RequestBody DrainAccountEditModel model) {
        DrainAccountDto dto = mapper.map(model, DrainAccountDto.class);
        dto.setOrgCode(getOrgCode());
        dto.setOrgName(getOrgName());
        boolean any = drainAccountService.anyById(model.getId());
        if (!any) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        if (!tunnelDesignService.anyById(model.getTunnelId(), getOrgCode())) {
            return notFound("探水设计Id错误！");
        }
        if (!checkPlanService.anyById(model.getCheckPlanId(), getOrgCode())) {
            return notFound("探水计划Id错误！");
        }

        int status = drainAccountService.getDataStatus(model.getId(), getOrgCode());
        if (status == WorkFlowStatus.Approved.getValue() || status == WorkFlowStatus.Acceptance.getValue()) {
            return json(HttpStatus.FORBIDDEN, "该数据状态为已验收或已审核，不能编辑！");
        }
        boolean updateResult = drainAccountService.update(dto);
        if (updateResult) {
            return ok("修改成功。");
        }

        return badRequest("数据修改失败！");
    }

    /**
     * 数据生成台账
     *
     * @param id 数据Id
     * @return Json提示信息
     */
    @PutMapping("/operatedrain/{id}")
    @SysLog("数据生成台账")
    public ActionResult uploadDrainid(@PathVariable String id) {
        if (!drainAccountService.anyById(id)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }

        if (!drainAccountService.anyUploaded(id, getOrgCode())) {
            return notFound("信息填写不完整！");
        }

        boolean uploadSuccessful = drainAccountService.uploadDrainid(id, getOrgCode());
        if (uploadSuccessful) {
            DrainAccountDto drainAccountDto = drainAccountService.getById(id);
            // 获取探水计划孔数
            List<String> checkPlanId = new ArrayList<>();
            checkPlanId.add(drainAccountDto.getCheckPlanId());
            List<CheckPlanDetailDto> checkPlanDetailDtos = checkPlanDetailService.getByCheckPlanId(checkPlanId);
            // 获取实际填报的孔数
            List<AcceptanceCheckDto> acceptanceCheckDtoList = acceptanceCheckService.getByDrainId(id, getOrgCode());
            if (checkPlanDetailDtos.size() != acceptanceCheckDtoList.size()) {
                List<String> userIdList = getAllUserIdList();
                String[] users = new String[userIdList.size()];
                String[] userIds = userIdList.toArray(users);
                McMessageWarnDTO mcMessageWarnDTO = new McMessageWarnDTO();
                mcMessageWarnDTO.setReceiveIds(userIds);
                mcMessageWarnDTO.setContainUserSelf("1");
                mcMessageWarnDTO.setTitle("设计的孔数和实际打的孔数一不一样");
                StringBuilder content = new StringBuilder()
                        .append(drainAccountDto.getWorkName())
                        .append("在")
                        .append(drainAccountDto.getSurveyWaterMileage())
                        .append("处，设计孔数为：")
                        .append(checkPlanDetailDtos.size())
                        .append(",实际孔数为：")
                        .append(acceptanceCheckDtoList.size());
                mcMessageWarnDTO.setContent(content.toString());
                mcMessageWarnDTO.setWarnLevel("1");
                mcMessageWarnDTO.setStartTime(LocalDateTime.now().toDate());
                mcMessageWarnDTO.setNeedHandle("0");
                mcMessageWarnDTO.setFastUrl("0");
                mcMessageWarnDTO.setSkipUrl("0");
                mcMessageWarnDTO.setSendType("1");
                messageWarnService.sendMessageWarn(mcMessageWarnDTO);
            }
            return ok("生成成功。");
        }
        return badRequest("数据修改失败");
    }

    /**
     * 数据查看
     *
     * @param id 数据Id
     * @return Json提示信息
     */
    @PutMapping("/view/{id}")
    @SysLog("台账数据查看")
    public ActionResult view(@PathVariable String id) {
        if (!drainAccountService.anyById(id)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }

        drainAccountService.view(id);
        return ok("已经查看。");
    }

    /**
     * 删除数据
     *
     * @param id 数据Id
     * @return 删除是否成功
     */
    @DeleteMapping("/{id}")
    @SysLog("删除台账数据")
    public ActionResult delete(@PathVariable String id) {
        if (!drainAccountService.anyById(id)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        // 查询数据状态
        int status = drainAccountService.getDataStatus(id, getOrgCode());
        if (status == WorkFlowStatus.Approved.getValue() || status == WorkFlowStatus.Acceptance.getValue()) {
            return json(HttpStatus.FORBIDDEN, "该数据状态为已验收或已审核，不能删除！");
        }
        if (!drainAccountService.delete(id, getOrgCode())) {
            return badRequest("数据删除失败！");
        }
        return ok("删除成功。");
    }

    @GetMapping("/getByTunnelIdAndSurveyWaterMileage")
    @SysLog("根据设计ID和探水里程查询台账数据")
    public ActionResult get(@RequestParam("tunnelId") String tunnelId, @RequestParam("surveyWaterMileage") Double surveyWaterMileage) {
        DrainAccount drainAccount = new DrainAccount();
        List<DrainAccount> drainAccounts = drainAccountService.getByTunnalIdAndSurveyWaterMileage(surveyWaterMileage, getOrgCode(), tunnelId);
        if (drainAccounts != null && !drainAccounts.isEmpty()) {
            drainAccount = drainAccounts.get(0);
        }
        return json(drainAccount);
    }


    /**
     * 文件上传
     *
     * @param id    探水计划id
     * @param files 图片文件
     * @return Json提示信息
     */
    @PostMapping("/fileUpload/{id}")
    @SysLog("台账图片上传")
    @CheckOrgCode
    public ActionResult uploadFile(@PathVariable String id, @RequestParam("files") MultipartFile[] files, @RequestParam("module") String module) {
        if (!drainAccountService.anyById(id)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        //判断module参数是否正确
        if (!module.equals(DrainAccount.DRILLING_ENGINEERING_SUMMARY)) {
            return badRequest("module参数错误！");
        }
        //查询数据是否存在，存在删除
        if (fileService.anyByDataIdAndModuleName(id, module) && !fileService.deleteByDataIdAndModuleName(id, module)) {
            return badRequest("删除失败！");
        }
        for (MultipartFile multipartFile : files) {
            String fileName = multipartFile.getOriginalFilename();
            assert fileName != null;
            String fileType = fileName.substring(fileName.lastIndexOf("."));

            try {
                byte[] fileBytes = multipartFile.getBytes();
                FileSaveParamsModel model = new FileSaveParamsModel();
                model.setDataId(id);
                model.setFileType(fileType);
                model.setOrgCode(getOrgCode());
                model.setFile(fileBytes);
                model.setFileName(fileName);
                model.setOrgName(getOrgName());
                model.setModuleName(module);

                boolean saveResult = fileService.saveFile(model);
                if (!saveResult) {
                    return badRequest("文件保存失败！");
                }
            } catch (Exception e) {
                logger.error(e.toString());
                return internalServerError("文件保存失败！");
            }
        }

        return ok("保存成功。");
    }


    /**
     * 删除文件数据
     *
     * @param id     数据Id
     * @param fileId 文件id
     * @return Json提示信息
     */
    @DeleteMapping("/{id}/{fileId}")
    @SysLog("删除探水计划文件数据")
    @CheckOrgCode
    public ActionResult deleteFile(@PathVariable String id, @PathVariable String fileId) {
        if (!drainAccountService.anyById(id)) {
            return notFound("台账Id错误！");
        }
        if (!fileService.anyById(fileId, id)) {
            return notFound("该文件不存在！");
        }
        if (fileService.deleteById(fileId)) {
            return ok("文件删除成功。");
        }
        return badRequest("删除失败！");
    }
}
