package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import jylink.cpds.config.CheckOrgCode;
import jylink.cpds.domain.EvacuateConfig;
import jylink.cpds.domain.EvacuateConfigFile;
import jylink.cpds.service.IEvacuateConfigService;
import jylink.cpds.serviceModel.ActionResult;
import jylink.cpds.serviceModel.Pager;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

/**
 * (EvacuateConfig)表控制层
 *
 * <AUTHOR>
 * @since 2020-03-16 11:28:12
 */
@RestController
@RequestMapping("/api/evacuateConfig")
@Scope("prototype")
public class EvacuateConfigController extends BaseController {
    /**
     * 服务对象
     */
    @Autowired
    private IEvacuateConfigService evacuateConfigService;

    /**
    * 针对工作面上传撤离路线图
    * <AUTHOR>
    * @date 2020/3/16 17:06
    * @return  jylink.cpds.serviceModel.ActionResult
    */
    @PostMapping()
    @SysLog("针对工作面上传撤离路线图")
    @CheckOrgCode
   public ActionResult add(@RequestPart(value = "file")   MultipartFile[] file, @RequestPart(value = "tunnelId") String tunnelId, @RequestPart(value = "refugeRoute") String refugeRoute){

        EvacuateConfig evacuateConfig = new EvacuateConfig();
        evacuateConfig.setTunnelId(tunnelId);
        evacuateConfig.setRefugeRoute(refugeRoute);
        try {
            if(evacuateConfigService.add(file,evacuateConfig)){
                return ok();
            }
        } catch (Exception e) {
            return badRequest(e.getMessage());
        }
        return badRequest("上传撤离路线图失败!");
   }

    /**
    * 针对工作面上传撤离路线图 修改
    * <AUTHOR>
    * @date 2020/3/16 17:06
    * @return  jylink.cpds.serviceModel.ActionResult
    */
    @PostMapping("/update")
    @SysLog("针对工作面上传撤离路线图修改")
    @CheckOrgCode
    public ActionResult update(@RequestPart(value = "file")   MultipartFile[] file, @RequestPart(value = "tunnelId") String tunnelId, @RequestPart(value = "refugeRoute") String refugeRoute){

        EvacuateConfig evacuateConfig = new EvacuateConfig();
        evacuateConfig.setTunnelId(tunnelId);
        evacuateConfig.setRefugeRoute(refugeRoute);
        try {
            if(evacuateConfigService.update(file,evacuateConfig)){
                return ok();
            }
        } catch (Exception e) {
            return badRequest(e.getMessage());
        }
        return badRequest("上传撤离路线图失败!");
    }

    /**
    * 上传撤离图删除
    * <AUTHOR>
    * @date 2020/3/16 17:13
    * @return  jylink.cpds.serviceModel.ActionResult
    */
    @DeleteMapping
    @SysLog("上传撤离图删除")
    @CheckOrgCode
    public ActionResult delete(@RequestParam("id") String id){
        if (evacuateConfigService.deleteById(id)) {
            return ok();
        }
        return badRequest("删除失败!");
    }
    /**
    * 撤离图列表
    * <AUTHOR>
    * @date 2020/3/16 17:16
    * @return  jylink.cpds.serviceModel.ActionResult
    */
    @GetMapping
    @SysLog("撤离图列表")
    public ActionResult getList(@RequestParam("currentPage") int currentPage,
                                    @RequestParam("pageSize") int pageSize){
        Pager<EvacuateConfigFile> list = evacuateConfigService.getList(currentPage,pageSize);
        return ok(list);
    }
    /**
    * 撤离图信息
    * <AUTHOR>
    * @date 2020/3/16 17:30
    * @return  jylink.cpds.serviceModel.ActionResult
    */
    @GetMapping("/getDetailInfo")
    @SysLog("撤离图信息")
    public ActionResult getDetailInfo(@RequestParam("id") String id){
        EvacuateConfigFile  info = evacuateConfigService.getDetailInfo(id);
        return ok(info);
    }

}