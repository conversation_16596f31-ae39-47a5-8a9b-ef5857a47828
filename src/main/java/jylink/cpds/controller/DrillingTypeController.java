package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.service.IDrillingTypeService;
import jylink.cpds.serviceModel.ActionResult;
import jylink.cpds.serviceModel.dto.DrillingTypeDto;
import jylink.cpds.serviceModel.params.DrillingTypeAddModel;
import jylink.cpds.serviceModel.params.DrillingTypeEditModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/drillingtype")
@Scope("prototype")
public class DrillingTypeController extends BaseController {

    /**
     * 钻机类型配置service服务
     */
    private final IDrillingTypeService service;

    /**
     * dozer转换
     */
    private final Mapper mapper;

    @Autowired
    public DrillingTypeController(IDrillingTypeService service, Mapper mapper) {
        this.service = service;
        this.mapper = mapper;
    }

    /**
     * 根据机构编码查询数据信息
     *
     * @return 查询结果列表
     */
    @GetMapping
    @SysLog("根据机构编码查询钻机类型配置")
    public ActionResult getByOrgCode() {
        List<DrillingTypeDto> drillingTypeDtos = service.getByOrgCode(getOrgCode());
        return ok(drillingTypeDtos);
    }

    /**
     * 添加数据
     *
     * @param drillingTypeAddModel 添加实体模型
     * @return 是否添加成功
     */
    @PostMapping
    @SysLog("添加钻机类型配置")
    public ActionResult add(@Validated @RequestBody DrillingTypeAddModel drillingTypeAddModel) {
        if (service.anyByType(getOrgCode(), drillingTypeAddModel.getDrillingType())) {
            return badRequest("钻孔型号已存在");
        }
        DrillingTypeDto drillingTypeDto = mapper.map(drillingTypeAddModel, DrillingTypeDto.class);
        drillingTypeDto.setOrgCode(getOrgCode());
        drillingTypeDto.setOrgName(getOrgName());
        if (service.add(drillingTypeDto)) {
            return created("数据创建成功");
        }
        return badRequest("数据创建失败");
    }

    /**
     * 根据数据id查询信息
     *
     * @param id 数据id
     * @return 查询结果
     */
    @GetMapping("{id}")
    @SysLog("根据ID查询钻机类型配置")
    public ActionResult getById(@PathVariable("id") String id) {
        String orgCode = getOrgCode();
        if (!service.anyById(id, orgCode)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        DrillingTypeDto drillingTypeDto = service.getById(id, orgCode);
        return ok(drillingTypeDto);
    }

    /**
     * 更新数据信息
     *
     * @param drillingTypeEditModel 修改参数模型
     * @return 是否修改成功
     */
    @PutMapping
    @SysLog("修改钻机类型配置")
    public ActionResult update(@Validated @RequestBody DrillingTypeEditModel drillingTypeEditModel) {
        String orgCode = getOrgCode();
        if (!service.anyById(drillingTypeEditModel.getId(), orgCode)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        if (service.anyByType(orgCode, drillingTypeEditModel.getDrillingType())) {
            return badRequest("钻机型号已存在");
        }
        DrillingTypeDto drillingTypeDto = mapper.map(drillingTypeEditModel, DrillingTypeDto.class);
        drillingTypeDto.setOrgCode(orgCode);
        drillingTypeDto.setOrgName(getOrgName());
        if (service.update(drillingTypeDto)) {
            return ok("数据修改成功");
        }
        return badRequest("数据修改失败");
    }

    /**
     * 根据数据id删除信息
     *
     * @param id 数据id
     * @return 是否删除成功
     */
    @DeleteMapping("{id}")
    @SysLog("删除钻机类型配置")
    public ActionResult delete(@PathVariable("id") String id) {
        if (!service.anyById(id, getOrgCode())) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        if (service.delete(id)) {
            return ok("数据删除成功");
        }
        return badRequest("数据删除失败");
    }
}
