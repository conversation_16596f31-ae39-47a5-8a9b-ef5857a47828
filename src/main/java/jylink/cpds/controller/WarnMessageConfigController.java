package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.service.IWarnMessageConfigService;
import jylink.cpds.serviceModel.ActionResult;
import jylink.cpds.serviceModel.PagerParams;
import jylink.cpds.serviceModel.params.WarnMessageConfigQueryModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.*;

/**
 * (WarnMessageConfig)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-25 17:33:42
 */
@RestController
@RequestMapping("api/warnMessageConfig")
@Scope("prototype")
public class WarnMessageConfigController extends BaseController {
    /**
     * 服务对象
     */
    @Autowired
    private IWarnMessageConfigService warnMessageConfigService;
    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    @SysLog("详情")
    public ActionResult detail(@PathVariable("id") String id) {
        return json(warnMessageConfigService.queryById(id, getOrgCode()));
    }

    /**
     * 分页查找
     */
    @GetMapping("/selByPage")
    @SysLog("分页")
    public ActionResult selByPage(@RequestBody PagerParams<WarnMessageConfigQueryModel> queryModel) {
        return json(warnMessageConfigService.getByPager(queryModel, getOrgCode()));
    }

}
