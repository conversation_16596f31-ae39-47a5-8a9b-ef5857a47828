package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.config.CheckOrgCode;
import jylink.cpds.domain.File;
import jylink.cpds.service.IFileService;
import jylink.cpds.service.ISpecialEqusService;
import jylink.cpds.serviceModel.*;
import jylink.cpds.serviceModel.dto.FileDto;
import jylink.cpds.serviceModel.dto.SpecialEqusDropDownDto;
import jylink.cpds.serviceModel.dto.SpecialEqusDto;
import jylink.cpds.serviceModel.params.FileSaveParamsModel;
import jylink.cpds.serviceModel.params.SpecialEqusEditModel;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/specialEqus")
@Scope("prototype")
public class SpecialEqusController extends BaseController{


    /**
     * 专业的设备服务
     */
    @Autowired
    private ISpecialEqusService service;

    /**
     * 文件服务
     */
    @Autowired
    private IFileService fileService;

    /**
     * mapper帮助类
     */
    @Autowired
    private Mapper mapper;

    /**
     * dozer帮助类
     */
    @Autowired
    private DozerUtils mapperUtils;

    /**
     * 日志实例
     */
    private Logger logger = LoggerFactory.getLogger(this.getClass());



    /**
     * 根据主键id查询数据信息
     * @param id  主键id
     * @return  查询结果
     */
    @GetMapping("/{id}")
    @SysLog("专业的设备  根据主键id查询数据信息")
    public ActionResult getById(@PathVariable("id") String id){
        if (!service.anyById(id)) {
            return badRequest(TfsMessageConstants.ID_NOT_FOUND);
        }
        SpecialEqusDto specialEqusDto = service.getById(id);
        List<File> pictureFile = fileService.getByDataIdAndModuleName(id, SpecialEquasFileTypeEnum.IMAGE.getValue());
        List<File> fileFile = fileService.getByDataIdAndModuleName(id, SpecialEquasFileTypeEnum.FILE.getValue());
        specialEqusDto.setFileDtoList(pictureFile);
        specialEqusDto.setFileFileDtoList(fileFile);
        specialEqusDto.setEquStatusChinese(specialEqusDto.getEquStatus().getInterpretation());
        specialEqusDto.setEquTypeChinese(specialEqusDto.getEquType().getInterpretation());
        return ok(specialEqusDto);
    }

    /**
     * 根据机构编码查询数据信息
     * @return  查询结果
     */
    @GetMapping("/getList")
    @SysLog("专业的设备  根据机构编码查询数据信息")
    public ActionResult getByOrgCode(){
        List<SpecialEqusDto> specialEqusDtos = service.getByOrgCode(getOrgCode());
        return ok(specialEqusDtos);
    }

    /**
     * 根据机构编码查询数据信息
     * @return  查询结果
     */
    @GetMapping("/getNoGrouting")
    @SysLog("专业的设备  根据机构编码查询数据信息")
    public ActionResult getNoGrouting(@RequestParam("orgCode") String orgCode){
        List<SpecialEqusDto> specialEqusDtos = service.getNoGrouting(orgCode);
        specialEqusDtos.forEach(specialEqusDto -> {
            specialEqusDto.setEquStatusChinese(specialEqusDto.getEquStatus().getInterpretation());
            specialEqusDto.setEquTypeChinese(specialEqusDto.getEquType().getInterpretation());
        });
        return ok(specialEqusDtos);
    }

    @GetMapping("/getDropDownList")
    @SysLog("专业的设备  根据机构编码查询数据信息")
    public ActionResult getDropDownList(){
        List<SpecialEqusDropDownDto> specialEqusDtos = service.getDropDownList(getOrgCode());
        return ok(specialEqusDtos);
    }

    /**
     * 根据机构编码查询数据信息
     * @param currentPage  当前页
     * @param pageSize  页面大小
     * @return  查询结果
     */
    @GetMapping()
    @SysLog("专业的设备  根据机构编码查询数据信息")
    public ActionResult getByOrgCode(@RequestParam("currentPage") int currentPage,
                                     @RequestParam("pageSize") int pageSize){
        Pager<SpecialEqusDto> specialEqusDtos = service.getParam(getOrgCode(),currentPage,pageSize);
        specialEqusDtos.getList().forEach(specialEqusDto -> {
            specialEqusDto.setEquStatusChinese(specialEqusDto.getEquStatus().getInterpretation());
            specialEqusDto.setEquTypeChinese(specialEqusDto.getEquType().getInterpretation());
        });
        return ok(specialEqusDtos);
    }

    /**
     * 添加
     * @param addModel  参数实体
     * @return  是否添加成功
     */
    @PostMapping
    @SysLog("专业的设备  添加")
    public ActionResult add(@Validated @RequestBody SpecialEqusEditModel addModel){
        SpecialEqusDto specialEqusDto = mapper.map(addModel, SpecialEqusDto.class);
        specialEqusDto.setOrgCode(getOrgCode());
        specialEqusDto.setOrgName(getOrgName());
        specialEqusDto.setUserId(getUserId());
        specialEqusDto.setUserName(getRealname());
//        ecispecialEqusDto.setEquCurrentSituation(EquCurrentSituationEnum.valueOf(addModel.getEquCurrentSituation()));
//        spalEqusDto.setEquType(EquTypeEnum.valueOf(addModel.getEquType()));
        if (service.add(specialEqusDto)) {
            return created("添加成功");
        }
        return badRequest("添加失败");
    }

    /**
     * 修改数据
     * @param editModel  参数实体
     * @return  是否修改成功
     */
    @PutMapping
    @SysLog("专业的设备  修改")
    public ActionResult update(@RequestBody SpecialEqusEditModel editModel){
        if (!service.anyById(editModel.getId())) {
            return badRequest(TfsMessageConstants.ID_NOT_FOUND);
        }
        SpecialEqusDto specialEqusDto = mapper.map(editModel, SpecialEqusDto.class);
        specialEqusDto.setOrgCode(getOrgCode());
        specialEqusDto.setOrgName(getOrgName());
        specialEqusDto.setUserId(getUserId());
        specialEqusDto.setUserName(getRealname());
//        specialEqusDto.setEquCurrentSituation(EquCurrentSituationEnum.valueOf(editModel.getEquMaintenanceCondition()));
//        specialEqusDto.setEquType(EquTypeEnum.valueOf(editModel.getEquType()));
        if (service.update(specialEqusDto)) {
            return ok("修改成功");
        }
        return badRequest("修改失败");
    }

    /**
     * 根据主键id删除数据
     * @param id  主键id
     * @return  是否删除成功
     */
    @DeleteMapping("/{id}")
    @SysLog("专业的设备  根据主键id删除数据")
    public ActionResult delete(@PathVariable("id") String id){
        if (!service.anyById(id)) {
            return badRequest(TfsMessageConstants.ID_NOT_FOUND);
        }
        if (!service.delete(id,getOrgCode())) {
            return badRequest("删除失败");
        }
        return ok("删除成功");
    }

    /**
     * 上传文件
     *
     * @param id    数据Id
     * @param files PDF文件
     * @return Json提示信息
     */
    @PostMapping("/fileupload/{id}")
    @SysLog("三专：专业的设备  上传文件")
    @CheckOrgCode
    public ActionResult uploadFile(@PathVariable String id, @RequestParam("files") MultipartFile[] files,@RequestParam(value = "type" , required = false) String type) {

        if (files.length == 0) {
            return badRequest("文件为空！");
        }
        for (MultipartFile file : files) {
            FileSaveParamsModel model = new FileSaveParamsModel();
            String fileName = file.getOriginalFilename();
            assert fileName != null;
            String fileType = fileName.substring(fileName.lastIndexOf("."));
            if (StringUtils.isBlank(type) || StringUtils.equals(SpecialEquasFileTypeEnum.IMAGE.getType(),type)){
                if (!".png".equals(fileType)&&!".jpg".equals(fileType)&&!".pneg".equals(fileType)&&!".JPG".equals(fileType)){
                    return badRequest(TfsMessageConstants.FILE_FORMAT_ERROR);
                }
                model.setModuleName(SpecialEquasFileTypeEnum.IMAGE.getValue());
            } else if(StringUtils.equals(SpecialEquasFileTypeEnum.FILE.getType(),type)){
                if (!".pdf".equals(fileType)&&!".PDF".equals(fileType)){
                    return badRequest(TfsMessageConstants.FILE_FORMAT_ERROR);
                }
                model.setModuleName(SpecialEquasFileTypeEnum.FILE.getValue());
            } else {
                return badRequest(TfsMessageConstants.FILE_FORMAT_ERROR);
            }
            try {
                byte[] fileBytes = file.getBytes();
                model.setDataId(id);
                model.setOrgName(getOrgName());
                model.setOrgCode(getOrgCode());
                model.setFileName(fileName);
                model.setFileType(fileType);
                model.setFile(fileBytes);

                boolean save = fileService.saveFile(model);
                if (!save) {
                    return badRequest("文件保存失败！");
                }
            } catch (Exception e) {
                logger.error(e.toString());
                return internalServerError("文件保存失败！");
            }
        }
        return ok("保存成功。");
    }


    /**
     * 获取主键id
     * @return  UUID
     */
    @GetMapping("/getUUID")
    @SysLog("专业的设备  获取主键id")
    public ActionResult getUUID(){
        String id = UUID.randomUUID().toString();
        return ok(id);
    }

    /**
     * 删除文件数据
     * @param fileId 文件id
     * @return Json提示信息
     */
    @DeleteMapping("fileDelete/{fileId}")
    @SysLog("三专：专业的设备  删除文件")
    @CheckOrgCode
    public ActionResult deleteFile( @PathVariable String fileId) {
        if (!fileService.any(fileId)) {
            return notFound("文件不存在！");
        }
        if (fileService.deleteById(fileId)) {
            return ok("删除成功。");
        }
        return badRequest("删除失败！");
    }

    /**
     * 获取文件地址列表
     *
     * @param id 数据Id
     * @return Json数据
     */
    @GetMapping("/getfiles/{id}")
    @SysLog("专业的设备  获取文件地址列表")
    public ActionResult getFileUrls(@PathVariable String id,@RequestParam(value = "type") String type) {
        List<FileDto> fileDtos = fileService.getByDataId(id);
        List<FileDto> collect = fileDtos.stream().filter(fileDto -> fileDto.getModuleName().equals(SpecialEquasFileTypeEnum.getValueByType(type))).collect(Collectors.toList());
        return json(collect);
    }


    /**
     * 获取下拉列表
     * @return
     */
    @GetMapping("/getEquType")
    @SysLog("获取设备类型下拉列表")
    public ActionResult getEquType(){
        List<ListItem> listItems = new ArrayList<>();
        List<EquTypeEnum> equTypeEnums = new ArrayList<>();
        equTypeEnums.add(EquTypeEnum.GEOCHEMICAL_EXPLORATION_EQUIPMENT);
        equTypeEnums.add(EquTypeEnum.GEOPHYSICAL_EQUIPMENT);
        equTypeEnums.add(EquTypeEnum.GROUTING_EQUIPMENT);
        equTypeEnums.add(EquTypeEnum.DRILLING_EQUIPMENT);
        for (EquTypeEnum equTypeEnum : equTypeEnums){
            ListItem item = new ListItem();
            item.setKey(String.valueOf(equTypeEnum.getValue()));
            item.setValue(equTypeEnum.getInterpretation());
            listItems.add(item);
        }
        return json(listItems);
    }



    /**
     * 获取下拉列表
     * @return
     */
    @GetMapping("/getStatus")
    @SysLog("获取设备状态下拉列表")
    public ActionResult getStatus(){
        List<ListItem> listItems = new ArrayList<>();
        List<EquCurrentSituationEnum> equCurrentSituationEnums = new ArrayList<>();
        equCurrentSituationEnums.add(EquCurrentSituationEnum.REPAIR);
        equCurrentSituationEnums.add(EquCurrentSituationEnum.INTACT);
        for (EquCurrentSituationEnum equCurrentSituationEnum : equCurrentSituationEnums){
            ListItem item = new ListItem();
            item.setKey(String.valueOf(equCurrentSituationEnum.getValue()));
            item.setValue(equCurrentSituationEnum.getInterpretation());
            listItems.add(item);
        }
        return json(listItems);
    }
}
