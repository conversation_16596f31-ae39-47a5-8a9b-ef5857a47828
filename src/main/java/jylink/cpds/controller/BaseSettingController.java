package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.service.IBaseSettingService;
import jylink.cpds.serviceModel.ActionResult;
import jylink.cpds.serviceModel.PagerParams;
import jylink.cpds.serviceModel.dto.BaseSettingDto;
import jylink.cpds.serviceModel.params.BaseSettingQueryModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.*;

/**
 * (BaseSetting)表控制层
 *
 * <AUTHOR>
 * @since 2020-10-27 19:07:56
 */
@RestController
@RequestMapping("api/baseSetting")
@Scope("prototype")
@Slf4j
public class BaseSettingController extends BaseController{
    /**
     * 服务对象
     */
    @Autowired
    private IBaseSettingService service;
    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;
    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    @SysLog("详情")
    public ActionResult detail(@PathVariable("id") String id) {
        return json(service.queryById(id,getOrgCode()));
    }

     /**
     * 分页查找
     *
     */
    @PostMapping("/selByPage")
    @SysLog("分页")
    public ActionResult selByPage(@RequestBody PagerParams<BaseSettingQueryModel> queryModel) {
        return json(service.getByPager(queryModel,getOrgCode()));
    }
    
     /**
     * 通过主键删除单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @DeleteMapping("/{id}")
    @SysLog("删除")
    public ActionResult delete(@PathVariable("id") String id) {
        if (!service.anyById(id,getOrgCode())) {
            return notFound("数据不存在");
        }
        if( service.deleteById(id)){
            return ok();
        }
        return badRequest("刪除失败!");
    }

    /**
     * 根据类型查询数据信息
     * @param type  类型
     * @return  查询结果
     */
    @GetMapping("/getByType")
    @SysLog("根据类型查询数据信息")
    public ActionResult getByType(@RequestParam("type") String type,@RequestParam("orgCode") String orgCode) {
        BaseSettingDto settingDto = service.getByKey(type, orgCode);
        return ok(settingDto);
    }

}