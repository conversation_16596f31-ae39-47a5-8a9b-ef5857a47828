package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.security.service.JykjCloudxUser;
import com.ada.jykjcloudx.sdk.security.util.SecurityUtils;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.service.IEnforcementMainService;
import jylink.cpds.service.IEnforcementMainUserService;
import jylink.cpds.serviceModel.*;
import jylink.cpds.serviceModel.dto.EnforcementMainUserDto;
import jylink.cpds.serviceModel.dto.EnforcementSummaryDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.context.annotation.Scope;
import com.github.dozermapper.core.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import org.springframework.validation.annotation.Validated;
import jylink.cpds.serviceModel.dto.EnforcementMainDto;
import jylink.cpds.serviceModel.params.EnforcementMainAddModel;
import jylink.cpds.serviceModel.params.EnforcementMainEditModel;
import jylink.cpds.serviceModel.params.EnforcementMainQueryModel;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 执法主表(EnforcementMain)表控制层
 *
 * <AUTHOR>
 * @since 2020-09-21 14:24:21
 */
@RestController
@RequestMapping("api/enforcementMain")
@Scope("prototype")
public class EnforcementMainController extends BaseController{

    /**
     * 执法人员表
     */
    @Autowired
    private IEnforcementMainUserService enforcementMainUserService;

    /**
     * 服务对象
     */
    @Autowired
    private IEnforcementMainService enforcementMainService;

    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * dozer 辅助类
     */
    @Autowired
    private DozerUtils mapperUtils;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    @SysLog("执法主表详情")
    public ActionResult detail(@PathVariable("id") String id) {
        EnforcementMainDto enforcementMainDto = enforcementMainService.queryById(id, SecurityUtils.getUser().getOrgCode());
        return json(enforcementMainDto);
    }

    /**
     * 执法计划下发
     * @param id  主键id
     * @return  是否下发成功
     */
    @PostMapping("/issued/{id}")
    @SysLog("执法计划下发")
    public ActionResult issued(@PathVariable("id") String id){
        EnforcementMainDto enforcementMainDto = enforcementMainService.queryById(id, SecurityUtils.getUser().getOrgCode());
        if (enforcementMainDto == null) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        if (enforcementMainDto.getEnforcementStatus() != EnforcementStatusEnum.CREATE.getCode()) {
            return badRequest("数据状态错误，不可下发");
        }
        if (enforcementMainService.updateStatus(id, EnforcementStatusEnum.ISSUED.getCode(), SecurityUtils.getUser().getOrgCode())) {
            return ok("下发成功");
        }
        return badRequest("下发失败");
    }

    /**
     * 执法文书计划下发
     * @param id  主键id
     * @return  是否下发成功
     */
    @PostMapping("/documentIssued/{id}")
    @SysLog("执法文书下发")
    public ActionResult documentIssued(@PathVariable("id") String id){
        EnforcementMainDto enforcementMainDto = enforcementMainService.queryById(id, SecurityUtils.getUser().getOrgCode());
        if (enforcementMainDto == null) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        if (enforcementMainDto.getEnforcementStatus() != EnforcementStatusEnum.SUBMITTED.getCode()) {
            return badRequest("该执法计划全部检查工作尚未完成，不可下发文书");
        }
        JykjCloudxUser user = SecurityUtils.getUser();
        if (!enforcementMainUserService.isLeader(id,user.getId())) {
            return badRequest("当前登录人并非组长，不可下发执法文书");
        }
        if (StringUtils.isBlank(enforcementMainDto.getIllegalFact()) || StringUtils.isBlank(enforcementMainDto.getEnforcementBasis()) || StringUtils.isBlank(enforcementMainDto.getPenaltyDecision())) {
            return badRequest("尚未完成执法意见汇总");
        }
        if (enforcementMainService.updateStatus(id, EnforcementStatusEnum.DOCUMENT_ISSUED.getCode(), SecurityUtils.getUser().getOrgCode())) {
            return ok("下发成功");
        }
        return badRequest("下发失败");
    }

    @GetMapping("/print")
    @SysLog("执法打印")
    public ActionResult print(String id) {
        return json(enforcementMainService.getDetailByMainId(id));
    }
    
     /**
     * 分页查找
     *
     */
    @PostMapping("/getByPage")
    @SysLog("执法主表分页")
    public ActionResult selByPage(@RequestBody PagerParams<EnforcementMainQueryModel> queryModel) {
        Pager<EnforcementMainDto> pager = enforcementMainService.getByPager(queryModel, SecurityUtils.getUser().getOrgCode());
        return json(pager);
    }
    
     /**
     * 通过主键删除单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @DeleteMapping("/{id}")
    @SysLog("执法主表删除")
    public ActionResult delete(@PathVariable("id") String id) {
        if (!enforcementMainService.anyById(id)) {
            return badRequest(TfsMessageConstants.ID_NOT_FOUND);
        }
        EnforcementMainDto enforcementMainDto = enforcementMainService.queryById(id, SecurityUtils.getUser().getOrgCode());
        if (enforcementMainDto.getEnforcementStatus() != EnforcementStatusEnum.CREATE.getCode()) {
            return badRequest("该执法任务已下发，不可删除");
        }
        if( enforcementMainService.deleteById(id)){
            return ok("删除成功");
        }
        return badRequest("刪除失败!");
    }
    
     /**
     * 新增数据
     *
     * @param addModel
     * @return 
     */
    @PostMapping()
    @SysLog("执法主表新增")
    public ActionResult insert(@Validated @RequestBody EnforcementMainAddModel  addModel) {
        EnforcementMainDto dto = mapper.map(addModel, EnforcementMainDto.class);
        //判断人员是否重复
        HashSet set = new HashSet<>(addModel.getUserAddModels());
        if (set.size() != addModel.getUserAddModels().size()) {
            return badRequest("人员信息重复");
        }
        List<String> strings = new ArrayList<>();
        addModel.getUserAddModels().forEach(user->{
            strings.addAll(user.getEnforcementMainUserDtos().stream().map(EnforcementMainUserDto::getEnforcementType).collect(Collectors.toList()));
        });
        HashSet hashSet = new HashSet<>(strings);
        if (hashSet.size() != strings.size()) {
            return badRequest("执法信息重复");
        }
        try {
            JykjCloudxUser user = SecurityUtils.getUser();
            dto.setEnforcementOrgCode(user.getOrgCode());
            dto.setUserId(getUserId());
            dto.setUserName(getRealname());
//            List<EnforcementMainUserDto> enforcementMainUserDtos = mapperUtils.ma(addModel.getUserAddModels(), EnforcementMainUserDto.class);
//            dto.setUserDtos(enforcementMainUserDtos);
            if (enforcementMainService.insert(dto,addModel.getUserAddModels())) {
                return ok("添加成功");
            }
        } catch (Exception e) {
           return badRequest(e.getMessage());
        }
        return badRequest("添加失败");
    }
    
     /**
     * 修改数据
     *
     * @param editModel
     * @return 
     */
    @PostMapping("/update")
    @SysLog("执法主表修改")
    public ActionResult update(@Validated @RequestBody  EnforcementMainEditModel  editModel) {
        EnforcementMainDto dto = mapper.map(editModel, EnforcementMainDto.class);
        HashSet set = new HashSet<>(editModel.getUserAddModels());
        if (set.size() != editModel.getUserAddModels().size()) {
            return badRequest("人员信息重复");
        }
        List<String> strings = new ArrayList<>();
        editModel.getUserAddModels().forEach(user->{
            strings.addAll(user.getEnforcementMainUserDtos().stream().map(EnforcementMainUserDto::getEnforcementType).collect(Collectors.toList()));
        });
        HashSet hashSet = new HashSet<>(strings);
        if (hashSet.size() != strings.size()) {
            return badRequest("执法信息重复");
        }
        JykjCloudxUser user = SecurityUtils.getUser();
        EnforcementMainDto enforcementMainDto = enforcementMainService.queryById(editModel.getId(), user.getOrgCode());
        if (enforcementMainDto.getEnforcementStatus() != EnforcementStatusEnum.CREATE.getCode()) {
            return badRequest("该执法任务已下发，不可修改");
        }
        try {
            dto.setEnforcementOrgCode(user.getOrgCode());
            dto.setUserId(getUserId());
            dto.setUserName(getRealname());
//            List<EnforcementMainUserDto> enforcementMainUserDtos = mapperUtils.mapList(editModel.getUserAddModels(), EnforcementMainUserDto.class);
//            dto.setUserDtos(enforcementMainUserDtos);
            if(enforcementMainService.update(dto,editModel.getUserAddModels())){
                return ok();
            }
        } catch (Exception e) {
           return badRequest(e.getMessage());
        }
         return badRequest("修改失败!");
    }

    /**
     * 根据用户和机构查询执法主表信息
     * @return  查询结果
     */
    @PostMapping("getByUser")
    @SysLog("根据用户和机构查询执法数据信息")
    public ActionResult getByUser (@RequestBody @Validated PagerParams<QueryModel> queryModel) {
        JykjCloudxUser user = SecurityUtils.getUser();
        if (StringUtils.isNotBlank(queryModel.getObjParams().getZoneCode())){
            if (queryModel.getObjParams().getZoneCode().startsWith(RegionEnum.CITY.getValue())){
                queryModel.getObjParams().setMineCityZoneCode(queryModel.getObjParams().getZoneCode().replace(RegionEnum.CITY.getValue(),""));
            } else if (queryModel.getObjParams().getZoneCode().startsWith(RegionEnum.COUNTY.getValue())){
                queryModel.getObjParams().setMineCountyIdCode(queryModel.getObjParams().getZoneCode().replace(RegionEnum.COUNTY.getValue(),""));
            } else if (queryModel.getObjParams().getZoneCode().startsWith(RegionEnum.PROVINCE.getValue())){
                queryModel.getObjParams().setMineProvzoneCode(queryModel.getObjParams().getZoneCode().replace(RegionEnum.PROVINCE.getValue(),""));
            }
        }
        Pager<EnforcementMainDto> mainDtos = enforcementMainService.getByUser(user.getOrgCode(), user.getId(),queryModel);
        return ok(mainDtos);
    }

    /**
     * 根据用户id和执法主表id查询执法数据信息
     * @param mainId  执法主表id
     * @return  查询结果
     */
    @GetMapping("/getByUserIdentifity/{mainId}")
    @SysLog("根据班组长身份标识及人员id查询执法用户信息")
    public ActionResult getByUser(@PathVariable("mainId") String mainId) {
        JykjCloudxUser user = SecurityUtils.getUser();
        EnforcementMainDto enforcementMainDto = enforcementMainService.getByUserIdentifity(mainId, user.getId(), user.getOrgCode());
        return ok(enforcementMainDto);
    }

    /**
     * 更新执法主表详情数据信息
     * @param summaryDto  编辑实体
     * @return
     */
    @PutMapping("/updateSummary")
    @SysLog("更新执法主表详情")
    public ActionResult updateDetail(@Validated @RequestBody EnforcementSummaryDto summaryDto){
        if (!enforcementMainService.anyById(summaryDto.getId())) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        JykjCloudxUser user = SecurityUtils.getUser();
        EnforcementMainDto enforcementMainDto = enforcementMainService.queryById(summaryDto.getId(), user.getOrgCode());
        if (enforcementMainDto.getEnforcementStatus() != EnforcementStatusEnum.SUBMITTED.getCode()) {
            return badRequest("全部执法人未完成执法意见提交或已下发执法文书");
        }
        if (!enforcementMainUserService.isLeader(summaryDto.getId(),user.getId())) {
            return badRequest("当前登录人并非组长，不可下发执法文书");
        }
        if (enforcementMainService.updateSummary(summaryDto)) {
            return ok("更新成功");
        }
        return badRequest("更新失败");
    }

    /**
     * 获取执法意见汇总信息
     * @param id  主键id
     * @return  查询结果
     */
    @GetMapping("/getSummary/{id}")
    @SysLog("获取执法意见汇总信息")
    public ActionResult getSummary(@PathVariable("id") String id) {
        if (!enforcementMainService.anyById(id)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        JykjCloudxUser user = SecurityUtils.getUser();
        EnforcementSummaryDto enforcementSummaryDto = enforcementMainService.getSummary(id, user.getOrgCode());
        return ok(enforcementSummaryDto);
   }
}