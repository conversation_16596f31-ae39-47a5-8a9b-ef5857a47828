package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import jylink.cpds.service.IPersonService;
import jylink.cpds.serviceModel.ActionResult;
import jylink.cpds.serviceModel.dto.DrainAccountPersonDto;
import jylink.cpds.serviceModel.dto.DrainPersonDto;
import jylink.cpds.serviceModel.person.InterfaceRealResult;
import jylink.cpds.serviceModel.person.PersonInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/person")
@Scope("prototype")
public class PersonController extends BaseController{

    @Autowired
    private IPersonService service;

    /**
     *
     * @param orgCode
     * @param personCards
     * @return
     */
    @GetMapping()
    @SysLog("或人员定位信息")
    public ActionResult getPersonInfo(@RequestParam("orgCode") String orgCode , @RequestParam("personCards") List<String> personCards ,
                                      @RequestParam("startTime") Date startTime , @RequestParam("endTime") Date endTime) {
        List<InterfaceRealResult> personInfo = service.getPersonInfo(orgCode, personCards , startTime , endTime);
        return ok(personInfo);
    }

    /**
     *
     * @param orgCode
     * @param personCard
     * @return
     */
    @GetMapping("/getPersonTrackInfo")
    @SysLog("获取人员定位入出井记录")
    public ActionResult getPersonTrackInfo(@RequestParam("orgCode") String orgCode , @RequestParam("personCard") String personCard , @RequestParam("drainId") String drainId) {
        List<InterfaceRealResult> personInfo = service.getPersonTrackInfo(orgCode, personCard , drainId);
        return ok(personInfo);
    }


    /**
     *
     * @param orgCode
     * @param personCards
     * @return
     */
    @GetMapping("/historry")
    @SysLog("或人员定位信息")
    public ActionResult getPersonHistoryInfo(@RequestParam("orgCode") String orgCode , @RequestParam("personCards") List<String> personCards ,
                                      @RequestParam("startTime") Date startTime , @RequestParam("endTime") Date endTime) {
        List<InterfaceRealResult> personInfo = service.getHistoryPersonInfo(orgCode, personCards , startTime , endTime);
        return ok(personInfo);
    }

    /**
     *
     * @param orgCode
     * @param personCard
     * @param enterMineTime
     * @return
     */
    @GetMapping("/getDetailTrack")
    @SysLog("获取详情轨迹")
    public ActionResult getDetailTrack(@RequestParam("orgCode") String orgCode , @RequestParam("personCard") String personCard , @RequestParam("enterMineTime") String enterMineTime) {
        List<InterfaceRealResult> personTrack = service.getPersonTrack(orgCode, personCard, enterMineTime);
        return ok(personTrack);
    }

    /**
     *
     * @param drainId
     * @return
     */
    @GetMapping("getPersonTrack")
    @SysLog("根据台账获取轨迹信息")
    public ActionResult getPersonTrack(@RequestParam("drainId") String drainId) {
        DrainPersonDto personTrack = service.getPersonTrack(drainId);
        return ok(personTrack);
    }

    @GetMapping("/getPersonCardInfo")
    @SysLog("获取人员定位卡号信息")
    public ActionResult getPersonCardInfo(@RequestParam("orgCode") String orgCode , @RequestParam("personCards") List<String> personCards) {
        List<PersonInfo> personInfos = service.getPersonCardInfo(orgCode , personCards);
        return ok(personInfos);
    }

    /**
     *
     * @param orgCode
     * @param personCards
     * @return
     */
    @GetMapping("/getRealInfoAndTrack")
    @SysLog("或人员定位信息")
    public ActionResult getRealInfoAndTrack(@RequestParam("orgCode") String orgCode , @RequestParam("personCards") List<String> personCards ,
                                      @RequestParam("startTime") Date startTime , @RequestParam("endTime") Date endTime) {
        List<InterfaceRealResult> personInfo = service.getRealInfoAndTrack(orgCode, personCards , startTime , endTime);
        return ok(personInfo);
    }

    /**
     *
     * @param orgCode
     * @param personCards
     * @return
     */
    @GetMapping("/getHistoryInfoAndTrack")
    @SysLog("或人员定位信息")
    public ActionResult getHistoryInfoAndTrack(@RequestParam("orgCode") String orgCode , @RequestParam("personCards") List<String> personCards ,
                                            @RequestParam("startTime") Date startTime , @RequestParam("endTime") Date endTime) {
        List<InterfaceRealResult> personInfo = service.getHistoryInfoAndTrack(orgCode, personCards , startTime , endTime);
        return ok(personInfo);
    }

    /**
     *
     * @param drainId
     * @return
     */
    @GetMapping("/getDrainPersonInfo")
    @SysLog("根据台账获取人员数据")
    public ActionResult getDrainPersonInfo(@RequestParam("drainId") String drainId) {
        try {
            DrainAccountPersonDto drainAccountPersonDto = service.getDrainPersonInfo(drainId);
            return ok(drainAccountPersonDto);
        } catch (Exception e) {
            return badRequest(e.getMessage());
        }
    }

}
