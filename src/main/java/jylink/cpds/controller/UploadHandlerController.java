package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import jylink.cpds.serviceModel.UploadResult;
import org.apache.commons.codec.digest.DigestUtils;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 前端富文本编辑器文件上传专用Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/upload")
@Scope("prototype")
public class UploadHandlerController extends BaseController {
    /**
     * 日志
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 文件接收接口
     *
     * @param file 文件体
     * @return Json提示信息
     */
    @PostMapping
    @SysLog("富文本编辑器  文件接收接口")
    public ResponseEntity<UploadResult> upload(MultipartFile file) {
        List<String> imgTypes = new ArrayList<>(5);
        imgTypes.add(".jpg");
        imgTypes.add(".jpeg");
        imgTypes.add(".png");
        imgTypes.add(".gif");
        imgTypes.add(".bmp");
        String originalFilename = file.getOriginalFilename();
        assert originalFilename != null;
        String fileType = Objects.requireNonNull(originalFilename).substring(originalFilename.lastIndexOf("."));
        if (!imgTypes.contains(fileType)) {
            return new ResponseEntity<>(new UploadResult("Fail", null, "只允许上传图片！"), HttpStatus.BAD_REQUEST);
        }

        try {
            byte[] buffer = file.getBytes();
            String md5 = DigestUtils.md5Hex(buffer);
            String fileName = md5 + fileType;

            // 设置文件相关路径
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
            String month = sdf.format(LocalDateTime.now().toDate());
            String dirPath = new File("").getAbsolutePath() + "/static/file" + File.separator + month;
            // 输出的文件流保存到本地文件，按月保存
            File tempFile = new File(dirPath);
            if (!tempFile.exists()) {
                tempFile.mkdirs();
            }
            String filePath = tempFile.getPath() + File.separator + fileName;
            try (FileOutputStream os = new FileOutputStream(filePath)) {
                os.write(buffer, 0, buffer.length);
            }

            String url = "/static/file/" + month + "/" + fileName;
            return new ResponseEntity<>(new UploadResult("Success", url, null), HttpStatus.OK);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return new ResponseEntity<>(new UploadResult("Fail", null, "系统出错！"), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
