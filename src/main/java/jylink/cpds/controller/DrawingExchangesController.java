package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.service.IDrawingExchangesFilesService;
import jylink.cpds.service.IDrawingExchangesService;
import jylink.cpds.serviceModel.ActionResult;
import jylink.cpds.serviceModel.Pager;
import jylink.cpds.serviceModel.PagerParams;
import jylink.cpds.serviceModel.WorkFlowStatus;
import jylink.cpds.serviceModel.dto.DrawingExchangesDto;
import jylink.cpds.serviceModel.dto.DrawingExchangesFilesDto;
import jylink.cpds.serviceModel.params.DrawingExchangesQueryModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 图纸交换记录主表(DrawingExchanges)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-31 17:21:44
 */
@RestController
@RequestMapping("api/drawingExchanges")
@Scope("prototype")
public class DrawingExchangesController extends BaseController {
    /**
     * 服务对象
     */
    @Autowired
    private IDrawingExchangesService drawingExchangesService;
    /**
     * dozer 对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * 交换文件服务
     */
    @Autowired
    private IDrawingExchangesFilesService drawingExchangeFilesService;

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/{id}")
    @SysLog("图纸交换记录主表(2.8版)详情")
    public ActionResult detail(@PathVariable("id") String id) {
        DrawingExchangesDto dto = drawingExchangesService.queryById(id);
        if (Objects.isNull(dto)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        List<DrawingExchangesFilesDto> exchangeFilesDtos = drawingExchangeFilesService.getByExchangeId(id);
        dto.setDrawingExchangeFilesDtos(exchangeFilesDtos);
        return json(dto);
    }

    /**
     * 分页查找
     */
    @PostMapping("/selByPage")
    @SysLog("图纸交换记录主表分页")
    public ActionResult selByPage(@RequestBody PagerParams<DrawingExchangesQueryModel> queryModel) {
        return json(drawingExchangesService.getByPager(queryModel, getOrgId()));
    }

    /**
     * 获取图纸交换发送记录信息
     *
     * @param orgCode     机构编码
     * @param sendTime    发送时间
     * @param queryOrgCode      审批状态
     * @param currentPage 当前页
     * @param pageSize    页面大小
     * @return 查询结果
     */
    @GetMapping("/getSendInfo")
    @SysLog("获取发送记录信息")
    public ActionResult getSendInfo(@RequestParam(name = "orgCode", required = false) String orgCode, @RequestParam(name = "sendTime", required = false) String sendTime,
                                    @RequestParam("queryOrgCode") String queryOrgCode, @RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize) {
        Integer approvalStatue = WorkFlowStatus.Approved.getValue();
        Pager<DrawingExchangesDto> pager = drawingExchangesService.getSendInfo(queryOrgCode, orgCode, sendTime, approvalStatue, currentPage, pageSize);
        return ok(pager);
    }

    /**
     * 获取图纸交换发送记录信息
     *
     * @param orgCode     机构编码
     * @param receiveTime 接收时间
     * @param queryOrgCode      审批状态
     * @param currentPage 当前页
     * @param pageSize    页面大小
     * @return 查询结果
     */
    @GetMapping("/getReceiveInfo")
    @SysLog("获取发送记录信息")
    public ActionResult getReceiveInfo(@RequestParam(name = "orgCode", required = false) String orgCode, @RequestParam(name = "receiveTime", required = false) String receiveTime,
                                       @RequestParam("queryOrgCode") String queryOrgCode, @RequestParam("currentPage") int currentPage, @RequestParam("pageSize") int pageSize) {
        Integer approvalStatue = WorkFlowStatus.Approved.getValue();
        Pager<DrawingExchangesDto> pager = drawingExchangesService.getReceiveInfo(queryOrgCode, orgCode, receiveTime, approvalStatue, currentPage, pageSize);
        return ok(pager);
    }

}
