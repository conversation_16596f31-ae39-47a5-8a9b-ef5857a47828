package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.api.entity.McMessageNormalDTO;
import com.ada.jykjcloudx.sdk.api.entity.McMessageWarnDTO;
import com.ada.jykjcloudx.sdk.api.service.MessageNormalService;
import com.ada.jykjcloudx.sdk.api.service.MessageWarnService;
import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.CpdsConstants;
import jylink.cpds.common.HttpClientUtils;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.config.MessageUtils;
import jylink.cpds.dao.IDictDao;
import jylink.cpds.dao.ITunnelDesignDao;
import jylink.cpds.domain.Dict;
import jylink.cpds.domain.DrainAccountOperator;
import jylink.cpds.domain.HoleDetail;
import jylink.cpds.domain.LogRecord;
import jylink.cpds.feign.VideoFeignService;
import jylink.cpds.service.*;
import jylink.cpds.serviceModel.AI.*;
import jylink.cpds.serviceModel.*;
import jylink.cpds.serviceModel.analysis.AnalysisStatus;
import jylink.cpds.serviceModel.dto.*;
import jylink.cpds.serviceModel.hikvision.HKChannels;
import jylink.cpds.serviceModel.params.*;
import org.apache.commons.lang.StringUtils;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> AI视频识别 控制器
 */
@RestController
@RequestMapping("/api/analysis")
@Scope("prototype")
public class AIAnalysisController extends BaseController {
    /**
     * service服务
     */
    @Autowired
    private IAnalysisWorkService service;

    /**
     * 设计Dao
     */
    @Autowired
    private ITunnelDesignDao tunnelDesignDao;

    /**
     * 海康服务
     */
    @Autowired
    private IHikvisionService hikvisionService;

    /**
     * 历史任务表
     */
    @Autowired
    private IAnalysisHistoryWorkService historyWorkService;

    /**
     * 探水台账服务
     */
    @Autowired
    private IDrainAccountService accountService;

    /**
     * 验收信息服务
     */
    @Autowired
    private IAcceptanceCheckService acceptanceCheckService;

    /**
     * dozer对象
     */
    @Autowired
    private Mapper mapper;

    /**
     * 消息服务类
     */
    @Autowired
    private MessageNormalService messageNormalService;

    /**
     * 历史警告消息服务
     */
    @Autowired
    private IHistoryWarnMessageService historyWarnMessageService;

    /**
     * 警告消息服务
     */
    @Autowired
    private MessageWarnService messageWarnService;

    /**
     * 字典表dao服务
     */
    @Autowired
    private IDictDao dictDao;

    /**
     * 孔表服务
     */
    @Autowired
    private IHoleDetailService holeDetailService;

    /**
     * 警告消息服务
     */
    @Autowired
    private IWarnMessaageService warnMessaageService;


    /**
     * 工作面硬件绑定服务
     */
    @Autowired
    private ITunnelLedCameraService ledCameraService;

    /**
     * 日志主表服务
     */
    @Autowired
    private ILogRecordService logRecordService;

    /**
     * 日志子表服务
     */
    @Autowired
    private ILogRecordDetailService logRecordDetailService;

    /**
     * 海康服务
     */
    @Autowired
    private VideoFeignService videoFeignService;

    /**
     * 钻孔任务交接班表服务
     */
    @Autowired
    private IDrainAccountOperatorService operatorService;

    /**
     * 日志
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 根据台账Id获取探水的任务列表数据
     *
     * @param drainId 台账Id
     * @return Json数据
     */
    @GetMapping("/{drainId}")
    @SysLog("根据台账Id获取探水的任务列表数据")
    public ActionResult getList(@PathVariable("drainId") String drainId) {
        List<AnalysisWorkDto> analysisWorkDtos = service.getByDrainId(drainId);
        List<AIAnalysisStatus> listItems = new ArrayList<>();

        for (AnalysisWorkDto analysisWorkDto : analysisWorkDtos) {
            AIAnalysisStatus item = new AIAnalysisStatus();
            item.setWorkId(analysisWorkDto.getId());
            item.setCameraName(analysisWorkDto.getCameraName());
            item.setStatus(analysisWorkDto.getStatus().getInterpretation());
            item.setAnalysisDate(analysisWorkDto.getCreateTime());
            listItems.add(item);
        }

        return ok(listItems);
    }

    /**
     * 根据探放水钻孔验收表Id获取历史探水的任务列表数据
     *
     * @param holeDetailId 孔表Id
     * @return Json数据
     */
    @GetMapping("/historyWork/{holeDetailId}")
    @SysLog("根据探放水钻孔验收表Id获取历史探水的任务列表数据")
    public ActionResult getHistoryList(@PathVariable("holeDetailId") String holeDetailId) {
        List<AIAnalysisStatus> listItems = new ArrayList<>();
        HoleDetail holeDetail = holeDetailService.getById(holeDetailId);
        if (holeDetail == null) {
            return json(listItems);
        }
        List<HoleDetailDto> holeDetailDtos = holeDetailService.getByDrainIdAndHoleNo(holeDetail.getOrgCode(),
                holeDetail.getDrainId(), holeDetail.getHoleNo());
        if (holeDetailDtos != null && !holeDetailDtos.isEmpty()) {
            List<String> holeDetailIds = holeDetailDtos.stream().map(HoleDetailDto::getId).collect(Collectors.toList());
            List<AnalysisHistoryWorkDto> analysisHistoryWorkDtos = historyWorkService.getByHoleDetails(holeDetailIds,
                    getOrgCode());
            for (AnalysisHistoryWorkDto analysisHistoryWorkDto : analysisHistoryWorkDtos) {
                AIAnalysisStatus item = new AIAnalysisStatus();
                item.setWorkId(analysisHistoryWorkDto.getId());
                item.setCameraName(analysisHistoryWorkDto.getCameraName());
                item.setStatus(analysisHistoryWorkDto.getStatus().getInterpretation());
                listItems.add(item);
            }
        }

        return json(listItems);
    }

    /**
     * 添加任务
     *
     * @param model 参数实体
     * @return Json提示信息
     */
    @PostMapping
    @SysLog("添加探水任务")
    public ActionResult addLiveAnalysis(@Validated @RequestBody AnalysisWorkAddModel model) {
        // 查询数据Id是否正确
        if (!accountService.anyById(model.getDrainId())) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("计划任务未找到！");
            return new ActionResult(response, HttpStatus.OK);
        }
        DrainAccountDto account = accountService.getById(model.getDrainId());
        //添加日志主表信息
        LogRecord logRecord = logRecordService.add(new LogRecordDto(OperationType.REAL_TIME_TASK_DISTRIBUTION.getValue(), account.getWorkCode(),
                account.getWorkName(), account.getSurveyWaterMileage(), "", "", true));
        TunnelLedCameraDto tunnelLedCameraDto = ledCameraService.getByTunnelId(account.getTunnelId());
        if (tunnelLedCameraDto == null) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("没有找到配置数据，请先绑定硬件！");
            //添加日志操作子表
            logRecordDetailService.addDetail(logRecord.getId(), "工作面硬件绑定信息不存在", "web", 1);
            return new ActionResult(response, HttpStatus.OK);
        }
        //获取摄像头
        List<HKChannels> hikvisionResources = hikvisionService.getResource(getOrgCode());
        if ( hikvisionResources.isEmpty()) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("获取摄像头信息失败！");
            //添加日志操作子表
            logRecordDetailService.addDetail(logRecord.getId(), "获取摄像头信息失败", "web", 1);
            return new ActionResult(response, HttpStatus.OK);
        }

        Optional<HKChannels> hKChannel = hikvisionResources.stream().filter(c -> c.getIndexCode().equals(tunnelLedCameraDto.getIndexCode())).findFirst();
        if (!hKChannel.isPresent()) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("获取摄像头信息失败,请查看硬件工作面绑定信息！");
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "工作面摄像头绑定信息错误", "web", 1);
            return new ActionResult(response, HttpStatus.OK);
        }
        //添加日志子表信息
        logRecordDetailService.addDetail(logRecord.getId(), "工作面摄像头绑定信息检测通过", "web", 0);
        //检查摄像头是否在线
        if (!"1".equals(hKChannel.get().getIsOnline())) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("摄像头不在线，下发任务失败！");
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "摄像头不在线", "web", 1);
            return new ActionResult(response, HttpStatus.OK);
        }
        //添加日志子表信息
        logRecordDetailService.addDetail(logRecord.getId(), "摄像头检测通过", "web", 0);
        List<AnalysisWorkDto> analysisWorkDtoList = service.getByDrainId(model.getDrainId());

        // 没有任务 进行任务下发，或存在任务 但任务状态为取消成功可以继续下发
        Optional<AnalysisWorkDto> optional = analysisWorkDtoList.stream().max(Comparator.comparing(AnalysisWorkDto::getSortOrder));
        boolean hasCancel = optional.isPresent()
                && optional.get().getStatus().getValue() == AnalysisStatus.CancelFinish.getValue();
        if (analysisWorkDtoList.isEmpty() || hasCancel) {
            JsonResponseGeneric responseGeneric = service.addAnalysis(model.getDrainId(), getOrgCode(), getUserId(), logRecord.getId());
            if (responseGeneric.getStatusCode() == 200) {
                try {
                    WarnMessageDto warnMessageDto = warnMessaageService.getByDataId(account.getCheckPlanId(), getOrgCode(), WarnMessageType.PLAN_NO_EXECUTE.getValue());
                    if (warnMessageDto != null) {
                        HistoryWarnMessageDto historyWarnMessageDto = mapper.map(warnMessageDto, HistoryWarnMessageDto.class);
                        historyWarnMessageDto.setCancel(true);
                        historyWarnMessageDto.setTriggerTime(warnMessageDto.getCreateTime());
                        historyWarnMessageDto.setCancelTime(LocalDateTime.now().toDate());
                        historyWarnMessageDto.setCancelPersonId(getUserId());
                        historyWarnMessageDto.setCancelPersonName(getRealname());
                        historyWarnMessageDto.setWarnMessageId(warnMessageDto.getId());
                        historyWarnMessageDto.setId(UUID.randomUUID().toString());
                        historyWarnMessageDto.setOrgCode(getOrgCode());
                        historyWarnMessageDto.setOrgName(getOrgName());
                        historyWarnMessageDto.setMessageRank(MessageRankType.TWO);
                        if (!warnMessaageService.delete(historyWarnMessageDto.getWarnMessageId(), getOrgCode())) {
                            JsonResponse response = new JsonResponse();
                            response.setStatusCode(-1);
                            response.setMessage("删除警告消息失败！！");
                            //添加日志子表信息
                            logRecordDetailService.addDetail(logRecord.getId(), "删除警告消息失败", "web", 1);
                            return new ActionResult(response, HttpStatus.OK);
                        }
                        if (!historyWarnMessageService.add(historyWarnMessageDto)) {
                            JsonResponse response = new JsonResponse();
                            response.setStatusCode(-1);
                            response.setMessage("添加历史消息失败！！");
                            //添加日志子表信息
                            logRecordDetailService.addDetail(logRecord.getId(), "添加历史消息失败", "web", 1);
                            return new ActionResult(response, HttpStatus.OK);
                        }
                        //webSocketInformationEndpoint.sendMessageToGroupWithDefault(getOrgCode());
                    }
                } catch (Exception e) {
                    JsonResponse response = new JsonResponse();
                    response.setStatusCode(-1);
                    response.setMessage("警告消息服务未知异常！！");
                    //添加日志子表信息
                    logRecordDetailService.addDetail(logRecord.getId(), "警告消息未知异常", "web", 1);
                    return new ActionResult(response, HttpStatus.OK);
                }

                return ok("任务提交成功！");
            } else {
                JsonResponse response = new JsonResponse();
                response.setStatusCode(-1);
                response.setMessage(responseGeneric.getMessage());
                return new ActionResult(response, HttpStatus.OK);
            }
        } else {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("该任务不允许再进行下发！！");
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "该任务不允许再进行下发！！", "web", 1);
            return new ActionResult(response, HttpStatus.OK);
        }
    }

    /**
     * 结束识别
     *
     * @param drainId 台账Id
     * @return json数据
     */
    @GetMapping("/endAnalysis/{drainId}")
    @SysLog("结束识别")
    public ActionResult sendAnalysis(@PathVariable("drainId") String drainId) {
        // 查询台账数据
        DrainAccountDto account = accountService.getById(drainId);
        //添加日志操作主表
        LogRecordDto logRecordDto = new LogRecordDto(OperationType.REAL_TIME_TASK_FINISH.getValue(), account.getWorkCode(),
                account.getWorkName(), account.getSurveyWaterMileage(), "", "", true);
        LogRecord logRecord = logRecordService.add(logRecordDto);
        //存在进行中的孔不能结束大任务
        List<HoleDetail> holeDetails = holeDetailService.getByDrainIdAndStatus(getOrgCode(), drainId, HoleDetailStatus.IN_PROGRESS);
        if (!holeDetails.isEmpty()) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("该工作面存在尚未结束作业的孔，不能结束识别任务！");
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "存在尚未结束的实时孔作业", "web", 1);
            return new ActionResult(response, HttpStatus.OK);
        }
        DrainAccountOperator byDrainAndUserId = operatorService.getByDrainAndUserId(account.getId(), getOrgCode(), getUserId());
        if (byDrainAndUserId != null && byDrainAndUserId.getIsOperator() != 1) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("用户权限不足！");
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "用户没有结束任务权限", "web", 1);
            return new ActionResult(response, HttpStatus.OK);
        }
        AIParamsModel model = new AIParamsModel();
        model.setHoleState("JOB FINISH");
        model.setHoleNumber("00");
        model.setCodeAttach("");
        String jobId = account.getOrgCode() + "-" + account.getWorkCode() + "-" + account.getSurveyWaterMileage();
        model.setJobId(jobId);
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String json = objectMapper.writeValueAsString(model);
            // 查询AI地址
            Dict dict = dictDao.getByGroupcode(CpdsConstants.AI_SERVICE_PATH_GROUP_CODE, getOrgCode()).get(0);
            String result = HttpClientUtils.sendPostDataByJson(dict.getDictValue() + "/DetectHoleNumber", json, "utf8", null);
            if (result.contains("ok")) {
                //添加日志子表信息
                logRecordDetailService.addDetail(logRecord.getId(), TfsMessageConstants.AI_INTERACTIVE_SUCCESS, "web", 0);
                logger.info("AI请求成功");
                List<AnalysisWorkDto> analysisWorkDto = service.getByDrainIdAndStatus(drainId, AnalysisStatus.Analyzing.getValue());
                if (!analysisWorkDto.isEmpty()) {
                    boolean updateStatus = service.updateStatus(analysisWorkDto.get(0).getId(), AnalysisStatus.NormalFinish.getValue());
                    if (!updateStatus) {
                        JsonResponse response = new JsonResponse();
                        response.setMessage("更新实时任务表状态失败！");
                        response.setStatusCode(-1);
                        //添加日志子表信息
                        logRecordDetailService.addDetail(logRecord.getId(), "更新实时任务基本信息失败", "web", 1);
                        return new ActionResult(response, HttpStatus.OK);
                    }
                    //添加日志子表信息
                    logRecordDetailService.addDetail(logRecord.getId(), "更新实时任务基本信息成功", "web", 0);
                }
                return ok("任务结束成功！");
            } else {
                logger.warn(MessageFormat.format("AI请求失败，返回内容：{0}", result));
                //添加日志子表信息
                logRecordDetailService.addDetail(logRecord.getId(), "AI交互失败：" + result, "web", 1);
                JsonResponse response = new JsonResponse();
                response.setStatusCode(-1);
                response.setMessage("AI请求失败!");
                return new ActionResult(response, HttpStatus.OK);
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("服务器异常！");
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "服务器异常:" + ex.getMessage(), "web", 1);
            return new ActionResult(response, HttpStatus.OK);
        }
    }

    /**
     * 实时任务取消
     *
     * @param model 参数实体
     * @return Json提示信息
     */
    @PostMapping("/cancel")
    @SysLog("实时任务取消")
    public ActionResult cancelLiveAnalysis(@RequestBody AnalysisWorkCancelModel model) {
        logger.info(MessageFormat.format("取消识别。。。{0}", JSON.toJSONString(model)));
        if (!service.anyById(model.getAnalysisWorkId())) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage(TfsMessageConstants.ID_NOT_FOUND);
            return new ActionResult(response, HttpStatus.OK);
        }

        if (!accountService.anyById(model.getDrainId())) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("台账Id错误！");
            return new ActionResult(response, HttpStatus.OK);

        }
        DrainAccountDto drainAccountDto = accountService.getById(model.getDrainId());
        //添加日志操作主表
        LogRecordDto logRecordDto = new LogRecordDto(OperationType.REAL_TIME_TASK_CENEL.getValue(), drainAccountDto.getWorkCode(),
                drainAccountDto.getWorkName(), drainAccountDto.getSurveyWaterMileage(), "", "", true);
        LogRecord logRecord = logRecordService.add(logRecordDto);

        DrainAccountOperator byDrainAndUserId = operatorService.getByDrainAndUserId(model.getDrainId(), getOrgCode(), getUserId());
        if (byDrainAndUserId != null && byDrainAndUserId.getIsOperator() != 1) {
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "没有取消任务权限", "web", 1);
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("用户权限不足！");
            return new ActionResult(response, HttpStatus.OK);
        }
        //存在进行中的孔不能结束大任务
        List<HoleDetail> holeDetails = holeDetailService.getByDrainIdAndStatus(getOrgCode(), model.getDrainId(), HoleDetailStatus.IN_PROGRESS);
        if (!holeDetails.isEmpty()) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("该工作面存在尚未结束作业的孔，不能取消识别任务！");
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "存在尚未结束的实时孔作业", "web", 1);
            return new ActionResult(response, HttpStatus.OK);
        }
        AnalysisWorkDto analysisWorkDto = service.getById(model.getAnalysisWorkId());
        AnalysisCancelJob analysisCancelJob = new AnalysisCancelJob();
        analysisCancelJob.setChannel(String.valueOf(analysisWorkDto.getChannel()));
        analysisCancelJob.setHkOrgCode(analysisWorkDto.getHkOrgCode());
        analysisCancelJob.setOrgCode(analysisWorkDto.getOrgCode());
        analysisCancelJob.setSurveyWaterMileage(String.valueOf(drainAccountDto.getSurveyWaterMileage()));
        analysisCancelJob.setWorkCode(drainAccountDto.getWorkCode());
        if (!service.cancelReleaseTask(analysisCancelJob, logRecord.getId())) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("下发取消任务失败！");
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "与AI交互失败", "web", 1);
            return new ActionResult(response, HttpStatus.OK);
        }
        //添加日志子表信息
        logRecordDetailService.addDetail(logRecord.getId(), "与AI交互成功", "web", 0);

        // 3 正在识别 4下发取消任务等待ai反馈 5取消任务成功 1.正常结束 2.异常中断 0 下发成功等待ai开始识别
        boolean updateStatus = service.updateStatus(model.getAnalysisWorkId(), AnalysisStatus.CancelAwait.getValue());
        if (!updateStatus) {
            JsonResponse response = new JsonResponse();
            response.setStatusCode(-1);
            response.setMessage("更新实时任务表状态失败！");
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "更新实时任务基本信息失败", "web", 1);
            return new ActionResult(response, HttpStatus.OK);
        }
        //添加日志子表信息
        logRecordDetailService.addDetail(logRecord.getId(), "更新实时任务基本信息成功", "web", 0);
        return ok("任务取消成功！");
    }

    /**
     * 添加历史识别任务
     *
     * @param model 实体对象
     * @return json提示信息
     */
    @PostMapping("/history")
    @SysLog("添加历史识别任务")
    public ActionResult addHistoryAnalysis(@RequestBody AnalysisHistoryWorkAddModel model) {
        String orgCode = getOrgCode();
        String orgName = getOrgName();
        HoleDetail holeDetail = holeDetailService.getById(model.getHoleDetailId());
        //添加日志操作主表
        LogRecordDto logRecordDto = new LogRecordDto(OperationType.HISTORY_TASK_DISTRIBUTION.getValue(), holeDetail.getWorkCode(),
                holeDetail.getWorkName(), holeDetail.getSurveyWaterMileage(), holeDetail.getHoleNo(), "", true);
        LogRecord logRecord = logRecordService.add(logRecordDto);
        List<AnalysisWorkDto> analysisWorkList = service.getByDrainId(holeDetail.getDrainId());
        if (!analysisWorkList.isEmpty()) {
            AnalysisWorkDto analysisWorkDto = analysisWorkList.get(0);
            if (!analysisWorkDto.getStatus().equals(AnalysisStatus.NormalFinish) && !analysisWorkDto.getStatus().equals(AnalysisStatus.CancelFinish) && !analysisWorkDto.getStatus().equals(AnalysisStatus.AbnormalFinsih)) {
                //添加日志子表信息
                logRecordDetailService.addDetail(logRecord.getId(), "实时任务尚未结束", "web", 1);
                return badRequest("实时识别未结束不能开始历史识别");
            }
        }
        AcceptanceCheckDto acceptanceCheckDto = acceptanceCheckService.getByHeloNo(holeDetail.getHoleNo(), holeDetail.getDrainId());
        if (acceptanceCheckDto.getStatus() == WorkFlowStatus.Acceptance) {
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "孔已验收", "web", 1);
            return badRequest("该孔已验收，不能下发历史识别");
        }
        // 根据孔号底下的最大副孔号及对应的analysisHistoryWork的状态判断是否可以下发历史任务
        List<HoleDetailDto> holeDetailDtos = holeDetailService.getByDrainIdAndHoleNo(getOrgCode(),
                holeDetail.getDrainId(), holeDetail.getHoleNo());
        Optional<HoleDetailDto> holeDetailDtoOptional = holeDetailDtos.stream()
                .sorted((str1, str2) -> str2.compareTo(str1)).findFirst();
        HoleDetailDto detailDto = new HoleDetailDto();
        if (holeDetailDtoOptional.isPresent()) {
            detailDto = holeDetailDtoOptional.get();
        }
        List<AnalysisHistoryWorkDto> analysisHistoryWorkDtos = historyWorkService.getByHoleDetailId(detailDto.getId());
        if (detailDto.getStatus() != HoleDetailStatus.COMPLETE.getValue() && !analysisHistoryWorkDtos.isEmpty() && analysisHistoryWorkDtos.get(0).getStatus().equals(AnalysisStatus.Analyzing)) {
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "上个任务尚未结束", "web", 1);
            return badRequest("上个任务未完成，不能继续下发历史任务");
        }

        //添加日志子表信息
        logRecordDetailService.addDetail(logRecord.getId(), "作业信息、识别任务验证通过", "web", 0);
        AnalysisHistoryWorkDto dto = mapper.map(model, AnalysisHistoryWorkDto.class);
        dto.setOrgCode(orgCode);
        dto.setOrgName(orgName);
        dto.setUserId(getUserId());
        if (!historyWorkService.add(dto, logRecord.getId())) {
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "下发历史识别失败", "web", 1);
            return badRequest("下发任务失败！");
        }
        return ok("下发历史任务成功！");
    }

    /**
     * 历史任务取消
     *
     * @param model 参数实体
     * @return Json提示信息
     */
    @PostMapping("/cancelHistory")
    @SysLog("历史任务取消")
    public ActionResult cancelHistoryAnalysis(@RequestBody AnalysisHistoryWorkCancelModel model) {
        if (!historyWorkService.anyById(model.getAnalysisHistoryWorkId())) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }

        HoleDetail holeDetail = holeDetailService.getById(model.getHoleDetailId());
        if (holeDetail == null) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        //添加日志操作主表
        LogRecordDto logRecordDto = new LogRecordDto(OperationType.HISTORY_TASK_CENEL.getValue(), holeDetail.getWorkCode(),
                holeDetail.getWorkName(), holeDetail.getSurveyWaterMileage(), holeDetail.getHoleNo(), "", true);
        LogRecord logRecord = logRecordService.add(logRecordDto);
        DrainAccountDto drainAccountDto = accountService.getById(holeDetail.getDrainId());
        AnalysisHistoryWorkDto analysisHistoryWorkDto = historyWorkService.getById(model.getAnalysisHistoryWorkId());
        AnalysisHistoryCancelJob analysisCancelJob = new AnalysisHistoryCancelJob();
        analysisCancelJob.setChannel(String.valueOf(analysisHistoryWorkDto.getChannel()));
        analysisCancelJob.setHkOrgCode(analysisHistoryWorkDto.getHkOrgCode());
        analysisCancelJob.setOrgCode(analysisHistoryWorkDto.getOrgCode());
        analysisCancelJob.setSurveyWaterMileage(String.valueOf(drainAccountDto.getSurveyWaterMileage()));
        analysisCancelJob.setWorkCode(drainAccountDto.getWorkCode());
        analysisCancelJob.setHoleNumber(holeDetail.getHoleNo());
        if (!service.cancelHistoryReleaseTask(analysisCancelJob, logRecord.getId())) {
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), TfsMessageConstants.AI_INTERACTIVE_FAIL, "web", 1);
            return badRequest("下发取消任务失败！");
        }
        //添加日志子表信息
        logRecordDetailService.addDetail(logRecord.getId(), TfsMessageConstants.AI_INTERACTIVE_SUCCESS, "web", 0);
        // 3 正在识别 4下发取消任务等待ai反馈 5取消任务成功 1.正常结束 2.异常中断 0 下发成功等待ai开始识别
        boolean updateStatus = historyWorkService.updateStatus(model.getAnalysisHistoryWorkId(), AnalysisStatus.CancelAwait.getValue());
        if (!updateStatus) {
            //添加日志子表信息
            logRecordDetailService.addDetail(logRecord.getId(), "更新历史任务表基本信息失败", "web", 1);
            return badRequest(TfsMessageConstants.UPDATE_HISTORY_WORK_FAIL);
        }

        //添加日志子表信息
        logRecordDetailService.addDetail(logRecord.getId(), "更新历史任务表基本信息成功", "web", 0);
        return ok("取消任务成功！");
    }

    /**
     * 接收取消返回结果 实时
     *
     * @param aiProcessState 数据实体
     * @return Json数据
     */
    @PostMapping("/sendProcessState")
    @SysLog("接收中断返回结果 实时")
    public ActionResult sendProcessState(@Validated @RequestBody AIProcessState aiProcessState) {
        logger.info(MessageFormat.format("sendProcessState方法进入{0}", JSON.toJSONString(aiProcessState)));
        //添加日志子表信息
        logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈取消实时任务进入", 0,
                aiProcessState.getMineCode(), Double.valueOf(aiProcessState.getWorkfaceLocation()), "", "", OperationType.REAL_TIME_TASK_CENEL.getValue(),
                aiProcessState.getWorkfaceId(), "AI"));
        String orgCode = aiProcessState.getMineCode();
        Double workfaceLocation = Double.parseDouble(aiProcessState.getWorkfaceLocation());
        int channel = Integer.parseInt(aiProcessState.getChannelNumber());
        List<DrainAccountDto> drainAccountDto = accountService.getByCodeAndMileage(orgCode,
                aiProcessState.getWorkfaceId(), workfaceLocation);
        if (drainAccountDto.isEmpty()) {
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈取消实时任务（计划任务未找到）中止", 1,
                    orgCode, workfaceLocation, "", "", OperationType.REAL_TIME_TASK_CENEL.getValue(),
                    aiProcessState.getWorkfaceId(), "AI"));
            return notFound("数据不存在");
        }
        String drainId = drainAccountDto.get(0).getId();
        AnalysisWorkDto analysisWorkDto = service.getByDrainIdAndChannel(drainId, channel);
        // 3 正在识别 4下发取消任务等待ai反馈 5取消任务成功 1.正常结束 2.异常中断 6.中断失败
        if (aiProcessState.getMsg() == AnalysisStatus.NormalFinish.getValue()) {
            boolean updateStatus = service.updateStatus(analysisWorkDto.getId(), AnalysisStatus.CancelFinish.getValue());
            if (!updateStatus) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈取消实时任务_NormalFinish（实时任务基本信息更新失败）", 1,
                        orgCode, workfaceLocation, "", "", OperationType.REAL_TIME_TASK_CENEL.getValue(),
                        aiProcessState.getWorkfaceId(), "AI"));
                return badRequest("更新实时任务表状态失败！！");
            }
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈取消实时任务_NormalFinish(实时任务基本信息更新成功)", 0,
                    orgCode, workfaceLocation, "", "", OperationType.REAL_TIME_TASK_CENEL.getValue(),
                    aiProcessState.getWorkfaceId(), "AI"));
            McMessageNormalDTO message = MessageUtils.createMessageNoToken("任务提示", "分析任务取消成功。", analysisWorkDto.getUserId(),
                    analysisWorkDto.getUserId());
            messageNormalService.sendMessageNormalNoUser(message);

            return ok("发送成功");
        } else if (aiProcessState.getMsg() == AnalysisStatus.SendAwait.getValue()) {
            boolean updateStatus = service.updateStatus(analysisWorkDto.getId(), AnalysisStatus.CancelFail.getValue());
            if (!updateStatus) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈取消实时任务_SendAwait(实时任务基本信息更新失败)", 1,
                        orgCode, workfaceLocation, "", "", OperationType.REAL_TIME_TASK_CENEL.getValue(),
                        aiProcessState.getWorkfaceId(), "AI"));
                return badRequest("更新实时任务表状态失败！！");
            }

            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈取消实时任务_SendAwait(实时任务基本信息更新成功)", 0,
                    orgCode, workfaceLocation, "", "", OperationType.REAL_TIME_TASK_CENEL.getValue(),
                    aiProcessState.getWorkfaceId(), "AI"));
            McMessageWarnDTO message = MessageUtils.createWarnMessageNoToken("任务提示", "分析任务取消失败。", analysisWorkDto.getUserId(),
                    analysisWorkDto.getUserId());
            messageWarnService.sendMessageWarnNoUser(message);
            return ok("发送成功");
        } else {
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈取消实时任务（指令未识别）", 1,
                    orgCode, workfaceLocation, "", "", OperationType.REAL_TIME_TASK_CENEL.getValue(),
                    aiProcessState.getWorkfaceId(), "AI"));
            return badRequest(TfsMessageConstants.INSTRUCTION_NOT_RECOGNIZED);
        }
    }

    /**
     * 接收取消返回结果 历史
     *
     * @param aiHistoryProcessState 参数实体
     * @return Json数据
     */
    @PostMapping("/sendHistoryProcessState")
    @SysLog("接收取消返回结果 历史")
    public ActionResult sendHistoryProcessState(@Validated @RequestBody AIHistoryProcessState aiHistoryProcessState) {
        logger.info(MessageFormat.format("sendHistoryProcessState方法进入{0}", JSON.toJSONString(aiHistoryProcessState)));
        String mineCode = aiHistoryProcessState.getMineCode();
        String workfaceId = aiHistoryProcessState.getWorkfaceId();
        Double workfaceLocation = Double.parseDouble(aiHistoryProcessState.getWorkfaceLocation());
        //添加日志子表信息
        logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史取消进入", 0, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
        // 查询任务是否存在
        List<DrainAccountDto> drainAccountDtos = accountService.getByCodeAndMileage(mineCode, workfaceId,
                workfaceLocation);
        if (drainAccountDtos.isEmpty()) {
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈取消历史任务（计划任务未找到）中止", 1, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                    "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
            return notFound(TfsMessageConstants.JOB_NOT_FOUND);
        }
        HoleDetailDto holeDetail = holeDetailService.getByAnalysisParams(mineCode, workfaceId, workfaceLocation,
                aiHistoryProcessState.getHoleNumber(), aiHistoryProcessState.getHoleNumberAttach());
        if (holeDetail == null) {
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史取消进入(未找到正确的孔任务)", 1, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                    "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
            return badRequest("没有找到对应的孔！");
        }
        List<AnalysisHistoryWorkDto> analysisHistoryWorkDtos = historyWorkService.getByHoleDetailId(holeDetail.getId());
        if (analysisHistoryWorkDtos.isEmpty()) {
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史取消进入(未找到正确的识别任务)", 1, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                    "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
            return notFound("信息错误");
        }
        // 找到正在等待反馈的数据
        Optional<AnalysisHistoryWorkDto> analysisOptional = analysisHistoryWorkDtos.stream()
                .filter(a -> a.getStatus().equals(AnalysisStatus.CancelAwait)).findFirst();
        if (!analysisOptional.isPresent()) {
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史取消进入(识别任务状态错误)", 1, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                    "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
            return badRequest("信息错误");
        }
        //添加日志子表信息
        logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史取消进入(识别任务信息、作业信息验证通过)", 0, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
        String userId = analysisOptional.get().getUserId();

        // 3 正在识别 4下发取消任务等待ai反馈 5取消任务成功 1.正常结束 2.异常中断 6.中断失败
        if (aiHistoryProcessState.getMsg() == AnalysisStatus.NormalFinish.getValue()) {
            boolean updateStatus = historyWorkService.updateStatus(analysisOptional.get().getId(), AnalysisStatus.CancelFinish.getValue());
            if (!updateStatus) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史取消进入(更新历史任务表基本信息失败)", 1, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                        "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
                return badRequest(TfsMessageConstants.UPDATE_HISTORY_WORK_FAIL);
            }
            boolean status = holeDetailService.updateStatus(holeDetail.getId(), HoleDetailStatus.COMPLETE.getValue());
            if (!status) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史取消进入(更新当前作业基本信息失败)", 1, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                        "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
                return badRequest("更新当前孔状态失败！！");
            }
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史取消进入(更新历史任务表、作业基本信息成功)", 0, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                    "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
            McMessageNormalDTO message = MessageUtils.createMessageNoToken("任务提示", "录像识别任务取消成功。", userId, userId);
            messageNormalService.sendMessageNormal(message);
            return ok("发送成功");
        } else if (aiHistoryProcessState.getMsg() == AnalysisStatus.SendAwait.getValue()) {
            boolean updateStatus = historyWorkService.updateStatus(analysisOptional.get().getId(), AnalysisStatus.CancelFail.getValue());
            if (!updateStatus) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史取消进入(更新历史任务表基本信息失败)", 1, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                        "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
                return badRequest(TfsMessageConstants.UPDATE_HISTORY_WORK_FAIL);
            }
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史取消进入(更新历史任务表基本信息成功)", 1, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                    "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
            McMessageWarnDTO message = MessageUtils.createWarnMessageNoToken("任务提示", "录像识别任务取消失败!", userId, userId);
            messageWarnService.sendMessageWarnNoUser(message);
            return ok("发送成功");
        } else {
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel(TfsMessageConstants.INSTRUCTION_NOT_RECOGNIZED, 1, mineCode, workfaceLocation, aiHistoryProcessState.getHoleNumber(),
                    "", OperationType.HISTORY_TASK_CENEL.getValue(), workfaceId, "AI"));
            return badRequest(TfsMessageConstants.INSTRUCTION_NOT_RECOGNIZED);
        }
    }

    /**
     * 获取详细信息 实时任务
     *
     * @return Json数据
     */
    @PostMapping("/sendEquipmentMSG")
    @SysLog("获取详细信息 实时任务")
    public ActionResult sendEquipmentMSG(@Validated @RequestBody AIEquipmentMSG aiEquipmentMSG) {
        logger.info(MessageFormat.format("aiEquipmentMSG方法进入{0}", JSON.toJSONString(aiEquipmentMSG)));
        int channelNumber = Integer.parseInt(aiEquipmentMSG.getChannelNumber());
        String mineCode = aiEquipmentMSG.getMineCode();
        String workfaceId = aiEquipmentMSG.getWorkfaceId();
        Double workfaceLocation = Double.parseDouble(aiEquipmentMSG.getWorkfaceLocation());
        List<DrainAccountDto> drainAccountDtos = accountService.getByCodeAndMileage(mineCode, workfaceId,
                workfaceLocation);
        if (drainAccountDtos.isEmpty()) {
            return notFound(TfsMessageConstants.JOB_NOT_FOUND);
        }

        String userId = drainAccountDtos.get(0).getUserId();
        AnalysisWorkDto analysisWorkDto = service.getByDrainIdAndChannel(drainAccountDtos.get(0).getId(),
                channelNumber);
        if (aiEquipmentMSG.getStatus() == AnalysisStatus.NormalFinish.getValue()) {
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈实时任务结束进入", 1, mineCode, workfaceLocation, "",
                    "", OperationType.REAL_TIME_TASK_FINISH.getValue(), workfaceId, "AI"));
            //添加日志子表信息
            boolean updateStatus = service.updateStatus(analysisWorkDto.getId(), AnalysisStatus.NormalFinish.getValue());
            if (!updateStatus) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈实时任务结束（实时任务基本信息更新失败）", 1, mineCode, workfaceLocation, "",
                        "", OperationType.REAL_TIME_TASK_FINISH.getValue(), workfaceId, "AI"));
                return badRequest(TfsMessageConstants.UPDATE_REAL_WORK_FAIL);
            }
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈实时任务结束（实时任务基本信息更新成功）", 0, mineCode, workfaceLocation, "",
                    "", OperationType.REAL_TIME_TASK_FINISH.getValue(), workfaceId, "AI"));
            McMessageNormalDTO message = MessageUtils.createMessageNoToken("任务提示", "识别已结束。", userId, userId);
            messageNormalService.sendMessageNormalNoUser(message);
        } else if (aiEquipmentMSG.getStatus() == AnalysisStatus.AbnormalFinsih.getValue()) {
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈异常中断进入", 1, mineCode, workfaceLocation, "",
                    "", OperationType.REAL_TIME_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
            if (analysisWorkDto.getStatus().getValue() != AnalysisStatus.Analyzing.getValue()) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈异常中断（该任务已经结束）中止", 1, mineCode, workfaceLocation, "",
                        "", OperationType.REAL_TIME_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
                return notFound("该任务已经结束");
            }
            boolean updateStatus = service.updateStatus(analysisWorkDto.getId(), AnalysisStatus.AbnormalFinsih.getValue());
            if (!updateStatus) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈异常中断（实时任务基本信息更新失败）", 1, mineCode, workfaceLocation, "",
                        "", OperationType.REAL_TIME_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
                return badRequest(TfsMessageConstants.UPDATE_REAL_WORK_FAIL);
            }
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈异常中断（实时任务基本信息更新成功）", 0, mineCode, workfaceLocation, "",
                    "", OperationType.REAL_TIME_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
            McMessageWarnDTO message = MessageUtils.createWarnMessageNoToken("任务提示", "识别异常中断！", userId, userId);
            messageWarnService.sendMessageWarnNoUser(message);
        } else if (aiEquipmentMSG.getStatus() == AnalysisStatus.Analyzing.getValue()) {
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈启动进入", 0, mineCode, workfaceLocation, "",
                    "", OperationType.REAL_TIME_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
            boolean updateStatus = service.updateStatus(analysisWorkDto.getId(), AnalysisStatus.Analyzing.getValue());
            if (!updateStatus) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈启动进入（实时任务基本信息更新失败）", 1, mineCode, workfaceLocation, "",
                        "", OperationType.REAL_TIME_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
                return badRequest(TfsMessageConstants.UPDATE_REAL_WORK_FAIL);
            }
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈启动进入（实时任务基本信息更新成功）", 0, mineCode, workfaceLocation, "",
                    "", OperationType.REAL_TIME_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
            McMessageNormalDTO messageNormalDTO = MessageUtils.createMessageNoToken("任务提示", "识别任务下发成功。", userId, userId);
            messageNormalService.sendMessageNormalNoUser(messageNormalDTO);
        } else {
            return badRequest("指令为识别");
        }
        return ok("发送成功");
    }

    /**
     * 获取详细信息 历史任务
     *
     * @return Json数据
     */
    @PostMapping("/sendHistoryEquipmentMSG")
    @SysLog("获取详细信息 历史任务")
    public ActionResult sendHistoryEquipmentMSG(@Validated @RequestBody AIHistoryEquipmentMSG aiHistoryEquipmentMSG) {
        logger.info(MessageFormat.format("sendHistoryEquipmentMSG方法进入{0}", JSON.toJSONString(aiHistoryEquipmentMSG)));
        String mineCode = aiHistoryEquipmentMSG.getMineCode();
        String workfaceId = aiHistoryEquipmentMSG.getWorkfaceId();
        Double workfaceLocation = Double.parseDouble(aiHistoryEquipmentMSG.getWorkfaceLocation());
        List<DrainAccountDto> drainAccountDtos = accountService.getByCodeAndMileage(mineCode, workfaceId,
                workfaceLocation);
        if (drainAccountDtos.isEmpty()) {
            return notFound(TfsMessageConstants.JOB_NOT_FOUND);
        }

        HoleDetail holeDetail = holeDetailService.getByHoleNo(drainAccountDtos.get(0).getId(),
                aiHistoryEquipmentMSG.getHoleNumber(), aiHistoryEquipmentMSG.getCodeAttach());
        if (holeDetail == null) {
            return notFound("参数错误！");
        }
        List<AnalysisHistoryWorkDto> analysisHistoryWorkDtos = historyWorkService.getByHoleDetailId(holeDetail.getId());
        if (analysisHistoryWorkDtos.isEmpty()) {
            return notFound("信息错误!");
        }
        String userId = analysisHistoryWorkDtos.get(0).getUserId();
        if (aiHistoryEquipmentMSG.getStatus() == AnalysisStatus.NormalFinish.getValue()) {
            logger.info("正常结束进入");
            //添加日志主表
            LogRecordDto logRecordDto = new LogRecordDto(OperationType.HISTORY_TASK_FINISH.getValue(), holeDetail.getWorkCode(),
                    holeDetail.getWorkName(), holeDetail.getSurveyWaterMileage(), holeDetail.getHoleNo(), "", true);
            logRecordDto.setOrgCode(mineCode);
            logRecordDto.setOrgName(analysisHistoryWorkDtos.get(0).getOrgName());
            LogRecord logRecord = logRecordService.addNoUser(logRecordDto);
            // 3 正在识别 4下发取消任务等待ai反馈 5取消任务成功 1.正常结束 2.异常中断 6.中断失败
            Optional<AnalysisHistoryWorkDto> analysisOptional = analysisHistoryWorkDtos.stream()
                    .filter(a -> a.getStatus().equals(AnalysisStatus.Analyzing)).findFirst();
            if (!analysisOptional.isPresent()) {
                //添加日志子表信息
                logRecordDetailService.addNoOrgCode(logRecord.getId(), "没有正在识别的任务", "AI", 1, mineCode);
                return badRequest("信息错误");
            }
            //添加日志子表信息
            logRecordDetailService.addNoOrgCode(logRecord.getId(), "识别任务验证通过", "AI", 0, mineCode);

            JsonResponseGeneric normalFinish = historyWorkService.NormalFinish(analysisOptional.get().getId(), AnalysisStatus.NormalFinish.getValue(), mineCode,
                    workfaceId, workfaceLocation, aiHistoryEquipmentMSG.getHoleNumber(), aiHistoryEquipmentMSG.getCodeAttach());
            if (normalFinish.getStatusCode() != HttpStatus.OK.value()) {
                //添加日志子表信息
                logRecordDetailService.addNoOrgCode(logRecord.getId(), "正常结束失败", "AI", 1, mineCode);
                return badRequest(normalFinish.getMessage());
            }
            //添加日志子表信息
            logRecordDetailService.addNoOrgCode(logRecord.getId(), "正常结束成功", "AI", 0, mineCode);
            McMessageNormalDTO message = MessageUtils.createMessageNoToken("任务提示", "识别结束。", userId, userId);
            messageNormalService.sendMessageNormalNoUser(message);
            logger.info("识别结束");

        } else if (aiHistoryEquipmentMSG.getStatus() == AnalysisStatus.AbnormalFinsih.getValue()) {
            logger.info("异常中断进入");
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史任务异常中断进入", 1, mineCode, workfaceLocation, holeDetail.getHoleNo(),
                    "", OperationType.HISTORY_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
            boolean status = historyWorkService.updateStatus(analysisHistoryWorkDtos.get(0).getId(), AnalysisStatus.AbnormalFinsih.getValue());
            if (!status) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("更新历史任务表基本信息失败", 1, mineCode, workfaceLocation, holeDetail.getHoleNo(),
                        "", OperationType.HISTORY_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
                return badRequest("更新历史任务表失败！！");
            }
            boolean updateStatus = holeDetailService.updateStatus(holeDetail.getId(), HoleDetailStatus.COMPLETE.getValue());
            if (!updateStatus) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("更新当前孔基本信息失败", 1, mineCode, workfaceLocation, holeDetail.getHoleNo(),
                        "", OperationType.HISTORY_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
                return badRequest("更新当前孔状态失败！！");
            }
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("更新历史任务表、当前孔基本信息成功", 0, mineCode, workfaceLocation, holeDetail.getHoleNo(),
                    "", OperationType.HISTORY_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
            McMessageWarnDTO message = MessageUtils.createWarnMessageNoToken("任务提示", "录像识别中断，请处理！", userId, userId);
            messageWarnService.sendMessageWarnNoUser(message);
            logger.info("录像识别中断，请处理！");
        } else if (aiHistoryEquipmentMSG.getStatus() == AnalysisStatus.Analyzing.getValue()) {
            logger.info("正在识别进入");
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史任务启动进入", 0, mineCode, workfaceLocation, holeDetail.getHoleNo(),
                    "", OperationType.HISTORY_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
            Optional<AnalysisHistoryWorkDto> analysisOptional = analysisHistoryWorkDtos.stream()
                    .filter(a -> a.getStatus().equals(AnalysisStatus.SendAwait)).findFirst();
            if (!analysisOptional.isPresent()) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史任务启动进入(识别任务不存在)", 1, mineCode, workfaceLocation, holeDetail.getHoleNo(),
                        "", OperationType.HISTORY_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
                return badRequest("信息错误");
            }
            boolean updateStatus = historyWorkService.updateStatus(analysisOptional.get().getId(), AnalysisStatus.Analyzing.getValue());
            if (!updateStatus) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史任务启动进入(更新历史任务表基本信息失败)", 1, mineCode, workfaceLocation, holeDetail.getHoleNo(),
                        "", OperationType.HISTORY_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
                return badRequest(TfsMessageConstants.UPDATE_HISTORY_WORK_FAIL);
            }
            boolean updateStartTime = holeDetailService.updateStartTime(mineCode, workfaceId, workfaceLocation,
                    aiHistoryEquipmentMSG.getHoleNumber(), aiHistoryEquipmentMSG.getCodeAttach(), LocalDateTime.now().toDate());
            if (!updateStartTime) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史任务启动进入(更新当前孔的开始时间失败)", 1, mineCode, workfaceLocation, holeDetail.getHoleNo(),
                        "", OperationType.HISTORY_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
                return badRequest("更新当前孔的开始时间失败！！");
            }
            //添加日志子表信息
            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈历史任务启动进入(更新历史任务、当前孔基本信息成功)", 0, mineCode, workfaceLocation, holeDetail.getHoleNo(),
                    "", OperationType.HISTORY_TASK_DISTRIBUTION.getValue(), workfaceId, "AI"));
            McMessageNormalDTO message = MessageUtils.createMessageNoToken("任务提示", "录像识别成功。", userId, userId);
            messageNormalService.sendMessageNormalNoUser(message);
            logger.info("录像识别成功。");
        } else {
            return badRequest(TfsMessageConstants.INSTRUCTION_NOT_RECOGNIZED);
        }

        return ok("发送成功");
    }

    /**
     * 获取Ocr检测信息
     *
     * @param ocrMessage 参数实体
     * @return Json数据
     */
    @PostMapping("/sendOcrMessage")
    @SysLog("获取Ocr检测信息")
    public ActionResult sendOcrMessage(@Validated @RequestBody OcrMessage ocrMessage) {
        logger.info(MessageFormat.format("AIAnalysisController类的sendOcrMessage{0}", JSON.toJSONString(ocrMessage)));
        String mineCode = ocrMessage.getMineCode();
        String workFaceId = ocrMessage.getWorkfaceId();
        String workfaceLocation = ocrMessage.getWorkfaceLocation();
        Double location = Double.parseDouble(workfaceLocation);

        if ("1".equals(ocrMessage.getIsSuccess())) {
            if (StringUtils.isEmpty(ocrMessage.getHoleNo())) {
                return badRequest("孔号为空");
            }
            String holenoMessage = ocrMessage.getHoleNo();
            int first = holenoMessage.indexOf(":");
            String holeNo = holenoMessage.substring(0, first);
            String isStart = holenoMessage.substring(first + 1);
            if ("START".equals(isStart)) {
                // 记录开始时间
                logger.info("已开始");
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈孔开始进入", 0, mineCode, location, holeNo, ocrMessage.getCodeAttach(), OperationType.HOLE_START.getValue(), workFaceId, "AI"));
                boolean addAnalysis = holeDetailService.addAnalysis(mineCode, workFaceId, location, holeNo, ocrMessage.getCodeAttach());
                if (!addAnalysis) {
                    //添加日志子表信息
                    logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈孔开始进入(添加作业信息失败)", 1, mineCode, location,
                            holeNo, ocrMessage.getCodeAttach(), OperationType.HOLE_START.getValue(), workFaceId, "AI"));
                    return badRequest("添加分析信息失败！！");
                }
                boolean updateStartTime = holeDetailService.updateStartTime(mineCode, workFaceId, location, holeNo, ocrMessage.getCodeAttach(), LocalDateTime.now().toDate());
                if (!updateStartTime) {
                    //添加日志子表信息
                    logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈孔开始进入(更新当前孔开始时间失败)", 1, mineCode, location, holeNo, ocrMessage.getCodeAttach(), OperationType.HOLE_START.getValue(), workFaceId, "AI"));
                    return badRequest("更新当前孔开始时间失败！！");
                }
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈孔开始进入(更新当前孔开始时间成功)", 0, mineCode, location, holeNo, ocrMessage.getCodeAttach(), OperationType.HOLE_START.getValue(), workFaceId, "AI"));
                logger.info("开始成功");

                // 进度条信息
                List<DrainAccountDto> accountDtos = accountService.getByCodeAndMileage(mineCode, workFaceId,
                        location);
                if (accountDtos != null && !accountDtos.isEmpty()) {
                    HoleDetail holeDetail = holeDetailService.getByHoleNo(accountDtos.get(0).getId(), holeNo, ocrMessage.getCodeAttach());
                    if (holeDetail != null) {
                        //更新分析孔深
                        boolean updateAnalysisHoleDistance = holeDetailService.updateAnalysisHoleDistance(holeDetail.getId(), 0);
                        if (!updateAnalysisHoleDistance) {
                            //添加日志子表信息
                            logRecordDetailService.addSubTable(new LogRecordDetailAddModel("更新分析孔深失败", 1, mineCode, location, holeNo, ocrMessage.getCodeAttach(), OperationType.HOLE_START.getValue(), workFaceId, "AI"));
                            return badRequest("更新分析孔深失败！！");
                        }
                    }
                }
            } else if ("END".equals(isStart)) {
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈孔结束进入", 0, mineCode, location, holeNo, ocrMessage.getCodeAttach(), OperationType.HOLE_END.getValue(), workFaceId, "AI"));
                boolean setAnalysisResult = holeDetailService.setAnalysisResult(mineCode, workFaceId, location, holeNo, ocrMessage.getCodeAttach());
                if (!setAnalysisResult) {
                    //添加日志子表信息
                    logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈孔结束进入(获取AI结果失败)", 1, mineCode, location, holeNo, ocrMessage.getCodeAttach(), OperationType.HOLE_END.getValue(), workFaceId, "AI"));
                    return badRequest("获取AI结果失败！！");
                }
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈孔结束进入(获取AI结果成功)", 0, mineCode, location, holeNo, ocrMessage.getCodeAttach(), OperationType.HOLE_END.getValue(), workFaceId, "AI"));
                //更新当前孔结束时间和状态为已完成
                boolean updateEndTime = holeDetailService.updateEndTime(mineCode, workFaceId, location, holeNo, ocrMessage.getCodeAttach(), LocalDateTime.now().toDate());
                if (!updateEndTime) {
                    //添加日志子表信息
                    logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈孔结束进入(更新当前孔的结束时间失败)", 1, mineCode, location, holeNo, ocrMessage.getCodeAttach(), OperationType.HOLE_END.getValue(), workFaceId, "AI"));
                    return badRequest("更新当前孔的结束时间失败！！");
                }
                //添加日志子表信息
                logRecordDetailService.addSubTable(new LogRecordDetailAddModel("AI反馈孔结束进入(更新当前孔的结束时间成功)", 0, mineCode, location, holeNo, ocrMessage.getCodeAttach(), OperationType.HOLE_END.getValue(), workFaceId, "AI"));
                logger.info("结束成功");
            } else {
                return badRequest("数据错误");
            }
        }
        return ok("发送成功");
    }

    /**
     * 获取Ocr检测信息 获取杆数
     *
     * @param ocrMessage 参数实体
     * @return Json数据
     */
    @PostMapping("/sendOcrHoleMessage")
    @SysLog("获取Ocr检测信息 获取杆数")
    public ActionResult sendOcrHoleMessage(@Validated @RequestBody OcrHoleMessage ocrMessage) {
        String workfaceLocation = ocrMessage.getWorkfaceLocation();
        Double location = Double.parseDouble(workfaceLocation);
        String orgCode = ocrMessage.getMineCode();
        String workfaceId = ocrMessage.getWorkfaceId();
        String holeNumber = ocrMessage.getHoleNumber();
        String poleNumber = ocrMessage.getPoleNumber();
        String codeAttach = ocrMessage.getCodeAttach();
        if ("".equals(ocrMessage.getHoleNumber())) {
            return badRequest("孔号为空");
        }
        if ("".equals(ocrMessage.getPoleNumber())) {
            return badRequest("杆数为空");
        }
        String sendMessage = "孔号:" + ocrMessage.getHoleNumber() + "副孔号：" + ocrMessage.getCodeAttach() + "杆数:"
                + ocrMessage.getPoleNumber();
        logger.info(sendMessage);

        // 将孔号和对应的数据存储到redis缓存中

        List<DrainAccountDto> accountDtos = accountService.getByCodeAndMileage(orgCode, workfaceId, location);
        if (accountDtos == null || accountDtos.isEmpty()) {
            return badRequest("未找到台账信息！");
        }
        HoleDetail holeDetail = holeDetailService.getByHoleNo(accountDtos.get(0).getId(), holeNumber, codeAttach);
        if (holeDetail == null) {
            return badRequest("未找到孔信息！");
        }
        // 杆长
        double poleLength = tunnelDesignDao.getById(accountDtos.get(0).getTunnelId()).getPoleLength();
        // 已打孔深
        double completeHoleDistance = poleLength * Double.parseDouble(poleNumber);
        //更新分析孔深
        boolean updateAnalysisHoleDistance = holeDetailService.updateAnalysisHoleDistance(holeDetail.getId(), completeHoleDistance);
        if (!updateAnalysisHoleDistance) {
            return badRequest("更新分析孔深失败！！");
        }
        return ok();
    }

    /**
     * 获取实时直播的Title 显示设计杆数、填报、实时杆数
     *
     * @param tunnelId           工作面编号
     * @param surveyWaterMileage 探水里程
     * @return 提示的title
     */
    @GetMapping("/getOcrTitle")
    @SysLog("获取实时直播的Title 显示设计杆数、填报、实时杆数")
    public ActionResult getTitle(@RequestParam("tunnelId") String tunnelId, @RequestParam("surveyWaterMileage") String surveyWaterMileage,@RequestParam("orgCode") String orgCode) {
        Double waterMileage = Double.parseDouble(surveyWaterMileage);
        List<DrainAccountDto> accountDtos = accountService.getByCodeAndMileage(orgCode, tunnelId, waterMileage);
        if (accountDtos == null || accountDtos.isEmpty()) {
            return badRequest("参数传递错误！");
        }
        if (accountDtos.size() > 1) {
            return badRequest("无法准确定位！");
        }
        AIOcrTitleResult r = new AIOcrTitleResult();
        r.setPoleNumber("0");
        List<HoleDetail> holeDetails = holeDetailService.getByDrainIdAndStatus(orgCode, accountDtos.get(0).getId(), HoleDetailStatus.IN_PROGRESS);
        if (holeDetails == null || holeDetails.isEmpty()) {
            return json(r);
        }

        // 从ai服务器获取对应的标题
        /*String key = "cpds:" + getOrgCode() + ":" + workCode + ":" + surveyWaterMileage;
        RedisUtils redis = new RedisUtils(redisTemplate);
        AIOcrTitleResult result = redis.get(key, AIOcrTitleResult.class);*/

        r.setCodeAttach(holeDetails.get(0).getViceHoleNo());
        r.setHoleNumber(holeDetails.get(0).getHoleNo());
        r.setHoleDistance(holeDetails.get(0).getHoleDistance() == null ? 0 : holeDetails.get(0).getHoleDistance());
        r.setCompleteHoleDistance(holeDetails.get(0).getAnalysisHoleDistance() == null ? 0 : holeDetails.get(0).getAnalysisHoleDistance());
        return json(r);
    }


    /**
     * GIS进度条
     *
     * @param checkId
     * @return
     */
    @GetMapping("/gisOcrTitle")
    @SysLog("GIS进度条")
    public ActionResult getTitle(@RequestParam("id") String checkId) {
        AcceptanceCheckDto acceptanceCheck = acceptanceCheckService.getById(checkId, getOrgCode());
        if (acceptanceCheck == null) {
            return badRequest(TfsMessageConstants.ID_NOT_FOUND);
        }
        AIOcrTitleResult r = new AIOcrTitleResult();
        HoleDetail holeDetail = holeDetailService.getById(acceptanceCheck.getHoleDetailId());
        if (holeDetail == null || holeDetail.getStatus() != 1) {
            return json(r);
        }
        r.setPoleNumber("0");
        r.setCodeAttach(holeDetail.getViceHoleNo());
        r.setHoleNumber(holeDetail.getHoleNo());
        r.setHoleDistance(holeDetail.getHoleDistance() == null ? 0 : holeDetail.getHoleDistance());
        r.setCompleteHoleDistance(holeDetail.getAnalysisHoleDistance() == null ? 0 : holeDetail.getAnalysisHoleDistance());
        return json(r);
    }
}
