package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import com.github.dozermapper.core.Mapper;
import jylink.cpds.common.DozerUtils;
import jylink.cpds.common.TfsMessageConstants;
import jylink.cpds.config.CheckOrgCode;
import jylink.cpds.domain.TunnelDesign;
import jylink.cpds.service.*;
import jylink.cpds.serviceModel.*;
import jylink.cpds.serviceModel.dto.*;
import jylink.cpds.serviceModel.params.*;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设计变更控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/designAlter")
@Scope("prototype")
public class DesignAlterController extends BaseController {
    /**
     * 探水设计
     */
    private final ITunnelDesignService tunnelDesignService;

    /**
     * 设计变更dao服务
     */
    private final IDesignAlterService alterService;

    /**
     * 钻孔设计服务
     */
    private final IHoleDesignService holeDesignService;

    /**
     * 文件表服务
     */
    private final IFileService fileService;

    /**
     * 钻孔变更表
     */
    private final IHoleDesignAlterService holeDesignAlterService;

    /**
     * dozer帮助类
     */
    private final DozerUtils mapperUtils;

    /**
     * dozer类
     */
    private final Mapper mapper;

    /**
     * 日志实例
     */
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    public DesignAlterController(@Qualifier("tunnelDesignService")ITunnelDesignService tunnelDesignService,
                                 IDesignAlterService alterService,
                                 @Qualifier("holeDesignService") IHoleDesignService holeDesignService,
                                 IFileService fileService,
                                 IHoleDesignAlterService holeDesignAlterService,
                                 DozerUtils mapperUtils,
                                 Mapper mapper) {
        this.tunnelDesignService = tunnelDesignService;
        this.alterService = alterService;
        this.holeDesignService = holeDesignService;
        this.fileService = fileService;
        this.holeDesignAlterService = holeDesignAlterService;
        this.mapperUtils = mapperUtils;
        this.mapper = mapper;
    }

    /**
     * 根据设计Id获取变更数据
     *
     * @param tunnelId 设计Id
     * @return json数据
     */
    @GetMapping("/list/{tunnelId}")
    @SysLog("根据设计Id获取变更数据")
    public ActionResult get(@PathVariable String tunnelId) {
        // 判断Id是否正确
        if (!tunnelDesignService.anyById(tunnelId, getOrgCode())) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }

        List<DesignAlterViewDto> viewModels = new ArrayList<>();

        // 查询所有的变更数据
        List<DesignAlterDto> alters = alterService.getByTunnelId(getOrgCode(), tunnelId);
        if (!alters.isEmpty()) {
            List<String> alterIds = alters.stream().map(DesignAlterDto::getId).collect(Collectors.toList());
            List<HoleDesignAlterDto> holeDesignAlterDtos = holeDesignAlterService.getByAlterIds(getOrgCode(), alterIds);
            List<FileDto> alterDesignFiles = fileService.getByDataIds(alterIds);
            alters.forEach(alterModel -> {
                DesignAlterViewDto viewModel = new DesignAlterViewDto();
                viewModel.setDesignAlter(alterModel);
                List<FileDto> currentFiles = alterDesignFiles.stream()
                        .filter(f -> f.getDataId().equals(alterModel.getId())).collect(Collectors.toList());
                viewModel.setDocuments(currentFiles.stream().filter(f -> f.getModuleName().equals(TunnelDesign.DESIGN_DOCUMENT)).collect(Collectors.toList()));
                viewModel.setPictures(currentFiles.stream().filter(f -> f.getModuleName().equals(TunnelDesign.DESIGN_PICTURE)).collect(Collectors.toList()));
                List<HoleDesignAlterDto> currentHoles = holeDesignAlterDtos.stream()
                        .filter(h -> h.getTunnelAlterId().equals(alterModel.getId()))
                        .sorted(Comparator.comparing(HoleDesignAlterDto::getSortOrder))// 升序
                        .collect(Collectors.toList());
                List<HoleDesignDto> designDtos = mapperUtils.mapList(currentHoles, HoleDesignDto.class);
                viewModel.setHoles(designDtos);
                viewModels.add(viewModel);
            });
        } else {
            return badRequest("没有变更数据");
        }

        return ok(viewModels);
    }

    /**
     * 获取当前变更数据
     *
     * @param tunnelId 设计Id
     * @return json数据
     */
    @GetMapping("/alters/{tunnelId}")
    @SysLog("获取当前变更数据")
    public ActionResult getAlterId(@PathVariable String tunnelId) {
        if (!tunnelDesignService.anyById(tunnelId, getOrgCode())) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }

        DesignAlterViewDto model = new DesignAlterViewDto();
        DesignAlterDto alter;
        TunnelDesignDto design = tunnelDesignService.getById(tunnelId, getOrgCode());

        alter = mapper.map(design, DesignAlterDto.class);
        alter.setFlows(mapperUtils.list2ListList(design.getFlows(),ApprovalWorkflowDto.class));
        model.setDesignAlter(alter);

        List<FileDto> files = fileService.getByDataId(design.getAlterId());
        List<String> alertIds = new ArrayList<>();
        alertIds.add(design.getAlterId());
        List<HoleDesignAlterDto> holeDesignAlterDtos = holeDesignAlterService.getByAlterIds(getOrgCode(), alertIds);
        List<HoleDesignDto> holes = mapperUtils.mapList(holeDesignAlterDtos, HoleDesignDto.class);

        model.setDocuments(files.stream().filter(f -> f.getModuleName().equals(TunnelDesign.DESIGN_DOCUMENT)).collect(Collectors.toList()));
        model.setPictures(files.stream().filter(f -> f.getModuleName().equals(TunnelDesign.DESIGN_PICTURE)).collect(Collectors.toList()));
        model.setHoles(holes);

        return ok(model);
    }

    /**
     * 保存变更数据
     *
     * @param model 实体对象
     * @return json提示信息
     */
    @PostMapping
    @SysLog("保存变更数据")
    public ActionResult post(@Validated @RequestBody DesignAlterParamModel model) {
        String orgCode = getOrgCode();
        String orgName = getOrgName();
        // 判断Id是否正确
        if (!tunnelDesignService.anyById(model.getTunnelId(), orgCode)) {
            return notFound("设计Id错误！");
        }
        if (model.getHoles() == null || model.getHoles().isEmpty()) {
            return badRequest("孔参数不可为空！");
        }

        // 判断钻孔Id是否全部正确
        List<HoleDesignDto> holes = holeDesignService.getByTunnelId(orgCode, model.getTunnelId());

        // 判断状态是否正确
        TunnelDesignDto tunnelDesign = tunnelDesignService.getById(model.getTunnelId(), getOrgCode());
        WorkFlowStatus designStatus = tunnelDesign.getStatus();
        List<WorkFlowStatus> statuses = new ArrayList<>();
        statuses.add(WorkFlowStatus.NoApproved);
        statuses.add(WorkFlowStatus.Approving);
        statuses.add(WorkFlowStatus.AlterDesign);
        if (statuses.stream().anyMatch(w -> w == designStatus)) {
            return badRequest("数据状态错误，不应进行此操作！");
        }

        // 用户变更时新增的孔补充其它参数
        List<HoleDesignDto> newList = mapperUtils.mapList(model.getHoles(), HoleDesignDto.class);
        newList.forEach(h -> {
            h.setOrgCode(orgCode);
            h.setOrgName(orgName);
        });

        SaveAlterMethodParamsModel paramModel = new SaveAlterMethodParamsModel();
        List<FileDto> documents = model.getDocuments();
        for (FileDto picture : model.getPictures()) {
            documents.add(picture);
        }
        DesignAlterDto designAlterDto = mapper.map(model, DesignAlterDto.class);
        designAlterDto.setOrgCode(orgCode);
        designAlterDto.setOrgName(orgName);
        designAlterDto.setCreateUser(getUserId());
        paramModel.setDesignAlter(designAlterDto);
        paramModel.setHoleDesigns(holes);
        paramModel.setNewHoleDesigns(newList);
        paramModel.setFlowDtoList(model.getFlows());
        paramModel.setDocuments(documents);

        Tuple<Boolean, String> result = holeDesignService.saveAlter(paramModel);
        if (!result.getFirstItem()) {
            return badRequest("更新失败！");
        }

        return ok((Object) result.getSecondItem());
    }

    /**
     * 审批通过
     *
     * @param model 参数实体
     * @return json提示信息
     */
    @PutMapping("/approved")
    @SysLog("变更审批通过")
    public ActionResult approved(@Validated @RequestBody ApprovalReasonEditModel model) {
        //状态判断
        if (!alterService.anyById(getOrgCode(), model.getId())) {
            return notFound("Id错误！");
        }
        //审批
        if (!alterService.approved(model)) {
            return badRequest("审批失败！");
        }

        return ok();
    }

    /**
     * 上传文件
     *
     * @param id    数据Id
     * @param files PDF文件
     * @return Json提示信息
     */
    @PostMapping("/fileupload/{id}")
    @SysLog("变更上传文件")
    public ActionResult uploadFile(@PathVariable String id, @RequestParam("files") MultipartFile[] files, String module) {
        if (!alterService.anyById(getOrgCode(), id)) {
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }

        DesignAlterDto dto = alterService.getById(getOrgCode(), id);
        if (dto.getApprovedStatus() != WorkFlowStatus.AlterDesign) {
            return badRequest("状态错误，不允许更改设计文件！");
        }
        if (files.length <= 0) {
            return badRequest("文件为空！");
        }
        //判断module参数是否正确
        if (!module.equals(TunnelDesign.DESIGN_PICTURE) && !module.equals(TunnelDesign.DESIGN_DOCUMENT)) {
            return badRequest("module参数错误！");
        }

        for (MultipartFile file : files) {
            String fileName = file.getOriginalFilename();
            assert fileName != null;
            String fileType = fileName.substring(fileName.lastIndexOf("."));
            try {
                byte[] fileBytes = file.getBytes();

                FileSaveParamsModel saveModel = new FileSaveParamsModel();
                saveModel.setDataId(id);
                saveModel.setOrgCode(getOrgCode());
                saveModel.setOrgName(getOrgName());
                saveModel.setFile(fileBytes);
                saveModel.setFileName(fileName);
                saveModel.setFileType(fileType);
                saveModel.setModuleName(module);

                boolean saveResult = fileService.saveFile(saveModel);
                if (!saveResult) {
                    return badRequest("文件保存失败！");
                }
            } catch (Exception e) {
                logger.error(e.toString());
                return internalServerError("文件保存失败！");
            }
        }

        return ok("保存成功。");
    }

    /**
     * 删除文件
     *
     * @param id 数据Id
     * @return json提示信息
     */
    @DeleteMapping("/file/{id}")
    @SysLog("变更删除文件")
    public ActionResult deleteFile(@PathVariable String id) {
        fileService.deleteById(id);
        return ok();
    }
    /**
     * 根据机构编码查询数据
     *
     * @return 探水设计数据
     */
    @GetMapping("/{tunnelId}")
    @SysLog("根据设计Id获取变更数据")
    public ActionResult getByTunnelId( @PathVariable String tunnelId) {
         if (!tunnelDesignService.anyById(tunnelId, getOrgCode())) {
             return notFound(TfsMessageConstants.ID_NOT_FOUND);
         }
         List<DesignAlterDto> byTunnelId = alterService.getByTunnelId(getOrgCode(), tunnelId);
         return ok(byTunnelId);
    }



    @GetMapping("/detail/{alterId}")
    @SysLog("查看")
    public ActionResult getDataByAlterId(@PathVariable String alterId) {
        DesignAlterDto alters = alterService.getDataByAlterId(getOrgCode(), alterId);
        List<String> alterIds = new ArrayList<>();
        alterIds.add(alterId);
        List<HoleDesignAlterDto> holeDesignAlterDtos = holeDesignAlterService.getByAlterIds(getOrgCode(), alterIds);
        List<FileDto> alterDesignFiles = fileService.getByDataIds(alterIds);
        DesignAlterViewDto viewModel = new DesignAlterViewDto();
        viewModel.setDesignAlter(alters);
        List<FileDto> currentFiles = alterDesignFiles.stream()
                .filter(f -> f.getDataId().equals(alters.getId())).collect(Collectors.toList());
        viewModel.setDocuments(currentFiles.stream().filter(f -> f.getModuleName().equals(TunnelDesign.DESIGN_DOCUMENT)).collect(Collectors.toList()));
        viewModel.setPictures(currentFiles.stream().filter(f -> f.getModuleName().equals(TunnelDesign.DESIGN_PICTURE)).collect(Collectors.toList()));
        List<HoleDesignAlterDto> currentHoles = holeDesignAlterDtos.stream()
                .filter(h -> h.getTunnelAlterId().equals(alters.getId()))
                .sorted(Comparator.comparing(HoleDesignAlterDto::getSortOrder))// 升序
                .collect(Collectors.toList());
        List<HoleDesignDto> designDtos = mapperUtils.mapList(currentHoles, HoleDesignDto.class);
        viewModel.setHoles(designDtos);
        return ok(viewModel);
    }
    @GetMapping("/edit/detail")
    @SysLog("查看")
    public ActionResult getDataByAlterIdForEdit(@RequestParam("dataId") String alterId,@RequestParam("moduleName") String moduleName) {
        DesignAlterDto alters = alterService.getDataByAlterId(getOrgCode(), alterId);
        List<String> alterIds = new ArrayList<>();
        alterIds.add(alterId);
        List<HoleDesignAlterDto> holeDesignAlterDtos = holeDesignAlterService.getByAlterIds(getOrgCode(), alterIds);
        List<FileDto> alterDesignFiles = fileService.getByDataIds(alterIds);
        DesignAlterViewDto viewModel = new DesignAlterViewDto();
        List<List<ApprovalUserDto>> lists = tunnelDesignService.qryApproveForUp(alterId, moduleName);
        alters.setUserFlows(lists);
        viewModel.setDesignAlter(alters);
        List<FileDto> currentFiles = alterDesignFiles.stream()
                .filter(f -> f.getDataId().equals(alters.getId())).collect(Collectors.toList());
        viewModel.setDocuments(currentFiles.stream().filter(f -> f.getModuleName().equals(TunnelDesign.DESIGN_DOCUMENT)).collect(Collectors.toList()));
        viewModel.setPictures(currentFiles.stream().filter(f -> f.getModuleName().equals(TunnelDesign.DESIGN_PICTURE)).collect(Collectors.toList()));
        List<HoleDesignAlterDto> currentHoles = holeDesignAlterDtos.stream()
                .filter(h -> h.getTunnelAlterId().equals(alters.getId()))
                .sorted(Comparator.comparing(HoleDesignAlterDto::getSortOrder))// 升序
                .collect(Collectors.toList());
        List<HoleDesignDto> designDtos = mapperUtils.mapList(currentHoles, HoleDesignDto.class);
        viewModel.setHoles(designDtos);

        return ok(viewModel);
    }
    /**
    *  //提交审批  --- 回退或者拒绝的情况下允许
    * <AUTHOR>
    * @date 2020/1/7 18:10
    * @return  jylink.cpds.serviceModel.ActionResult
    */
    @PutMapping("/approving/{alterId}")
    @SysLog("提交审批")
    @CheckOrgCode
    public ActionResult approving( @PathVariable String alterId) {
        DesignAlterDto designAlterDto = alterService.getById(getOrgCode(), alterId);
       if (!(designAlterDto.getApprovedStatus() == WorkFlowStatus.Refused || designAlterDto.getApprovedStatus() == WorkFlowStatus.Revoke )){
           return notFound(TfsMessageConstants.ID_NOT_FOUND);
       }
        //添加状态验证
        String tunnelId = designAlterDto.getTunnelId();
        List<DesignAlterDto> designAlterDtoList = alterService.getByTunnelId(getOrgCode(), tunnelId);
        for (DesignAlterDto alterDto : designAlterDtoList) {
            if (alterDto.getApprovedStatus() == WorkFlowStatus.AlterDesign || alterDto.getApprovedStatus() == WorkFlowStatus.Approving ){
                return notFound("已有变更中的设计！");
            }
        }
        if (!alterService.approving(alterId, getUserName(), WorkFlowStatus.AlterDesign, LocalDateTime.now().toDate())) {
            return notFound("状态不允许！");
        }
        return ok();
    }
    /**
     *  删除        --- 回退或者拒绝的情况下允许
     * <AUTHOR>
     * @date 2020/1/7 18:10
     * @return  jylink.cpds.serviceModel.ActionResult
     */
    @DeleteMapping("/{alterId}")
    @SysLog("删除")
    @CheckOrgCode
    public ActionResult delete( @PathVariable String alterId) {
        DesignAlterDto designAlterDto = alterService.getById(getOrgCode(), alterId);
        if (!(designAlterDto.getApprovedStatus() == WorkFlowStatus.Refused || designAlterDto.getApprovedStatus() == WorkFlowStatus.Revoke )){
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        alterService.update(alterId, getUserName(), WorkFlowStatus.AlterDesign, LocalDateTime.now().toDate());
        alterService.deleteDesignAlter(alterId,getOrgCode());
        return ok();
    }
    /**
     * 撤回
     *
     * @param model 参数模型
     * @return Json提示信息
     */
    @PutMapping("/revoke")
    @SysLog("变更   撤回")
    @CheckOrgCode
    public ActionResult revoke(@RequestBody RevokeParamsModel model) {
        DesignAlterDto designAlterDto = alterService.getById(getOrgCode(), model.getId());
        if (!(designAlterDto.getApprovedStatus() == WorkFlowStatus.Approving || designAlterDto.getApprovedStatus() == WorkFlowStatus.AlterDesign )){
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }

        try {
            if (!alterService.revoke(model.getId(), getUserId(), getOrgCode(), model.getReason(),model.getPassword())) {
                return badRequest("撤回失败！");
            }
        } catch (Exception e) {
            return badRequest(e.getMessage());
        }

        return ok("撤回成功。");
    }

    /**
     * 修改数据
     *
     * @param model 实体对象
     * @return Json提示信息
     */
    @PutMapping("{isApproving}")
    @SysLog("探水设计变更修改")
    @CheckOrgCode
    public ActionResult update(@PathVariable boolean isApproving, @Validated @RequestBody TunnelDesignEditModel model) {
        DesignAlterDto designAlterDto = alterService.getById(getOrgCode(), model.getId());
        if (!(designAlterDto.getApprovedStatus() == WorkFlowStatus.Refused || designAlterDto.getApprovedStatus() == WorkFlowStatus.Revoke )){
            return notFound(TfsMessageConstants.ID_NOT_FOUND);
        }
        //添加状态验证
        String tunnelId = designAlterDto.getTunnelId();
        List<DesignAlterDto> designAlterDtoList = alterService.getByTunnelId(getOrgCode(), tunnelId);
        for (DesignAlterDto alterDto : designAlterDtoList) {
            if (alterDto.getApprovedStatus() == WorkFlowStatus.AlterDesign || alterDto.getApprovedStatus() == WorkFlowStatus.Approving ){
                return notFound("已有变更中的设计！");
            }
        }


        TunnelDesignDto dto = mapper.map(model, TunnelDesignDto.class);
        dto.setOrgCode(getOrgCode());
        dto.setOrgName(getOrgName());

        try {
            if (alterService.updateAlterData(isApproving, dto, model.getFlows())) {
                return ok("修改成功。");
            }
        } catch (Exception e) {
            return badRequest(e.getMessage());
        }

        return badRequest("数据修改失败！");
    }

}
