package jylink.cpds.controller;

import com.ada.jykjcloudx.sdk.syslog.annotation.SysLog;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 华为严选controller
 * @date 2020/4/1 10:58
 * @version: V1.0
 */
@RestController
@RequestMapping
@Scope("prototype")
public class HuaweiController {
    @Value("${sass.frontUrl}")
    private String frontUrl;
    @Value("${sass.adminUrl}")
    private String adminUrl;
    @Value("${sass.userName}")
    private String userName;
    @Value("${sass.password}")
    private String password;

    @GetMapping("/getAppInfo")
    @SysLog("华为严选controller")
    public Map<String,String> getByDrainId() {
        Map<String,String> map = new HashMap<>();
        map.put("frontUrl",frontUrl);
        map.put("adminUrl",adminUrl);
        return map;
    }
    /**
     * 返回测试账号
     * <AUTHOR>
     * @date 2020/4/16 15:09
     * @param
     * @return  java.util.Map<java.lang.String,java.lang.String>
     */
    @GetMapping("/getTrialUser")
    @SysLog("华为严选controller")
    public Map<String,String> getTrialUser() {
        Map<String,String> map = new HashMap<>();
        map.put("userName",userName);
        map.put("password",password);
        return map;
    }
}
