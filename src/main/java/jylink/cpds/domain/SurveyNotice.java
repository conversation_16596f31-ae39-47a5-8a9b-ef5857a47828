package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * 探水通知单
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SurveyNotice extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 计划Id
     */
    private String planId;

    /**
     * 工作面名称
     */
    private String workName;

    /**
     * 探水里程
     */
    private Double surveyWaterMileage;

    /**
     * 通知日期
     */
    private Date notificationDate;

    /**
     * 编号
     */
    private String number;

    /**
     * 工程名称
     */
    private String projectName;

    /**
     * 施工地点
     */
    private String detectionPlace;

    /**
     * 水害情况简要说明
     */
    private String waterHarmfulConditions;

    /**
     * 探水要求
     */
    private String surveyWaterRequirement;

    /**
     * 总工程师意见
     */
    private String engineerOpinion;

    /**
     * 总工签字
     */
    private String engineerSign;

    /**
     * 状态
     */
    private int status;

    /**
     * 上报时间
     */
    private Date uploadTime;

    /**
     * 掘进队
     */
    private String tunnelingTeam;
    private String tunnelingTeamId;

    /**
     * 最近探水日期
     */
    private Date recentExplorationTime;

    /**
     * 最近探水班次
     */
    private String recentExplorationClass;

    /**
     *掘进截止时间
     */
    private Date drivingDeadlineTime;

    /**
     *掘进截止班次
     */
    private String drivingDeadlineClass;

    /**
     *累计掘进米数
     */
        private Double cumulativeDrivingMetres;

    /**
     *预计探水开始时间
     */
    private Date estimatedDetectionStartTime;

    /**
     *预计探水开始班次
     */
    private String estimatedDetectionStartClass;

    /**
     *预计探水结束时间
     */
    private Date estimatedDetectionEndTime;

    /**
     *预计探水结束班次
     */
    private String estimatedDetectionEndClass;

    /**
     *地测科科长姓名
     */
    private String geodesySectionChiefName;

    /**
     *接收时间
     */
    private Date receivingTime;

    /**接受人姓名
     *
     */
    private String receivingName;

    /**
     *下发单位
     */
    private String issuedOrg;

    /**
     *通知人
     */
    private String issuedName;
    private String allowableSurvey;
    private String drivingDistance;
}
