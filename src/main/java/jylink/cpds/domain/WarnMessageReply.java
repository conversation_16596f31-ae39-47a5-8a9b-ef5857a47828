package jylink.cpds.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tfs_warn_message_reply")
public class WarnMessageReply extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报警信息id
     */
    @TableField("warn_id")
    private String warnId;

    /**
     * 回复消息
     */
    @TableField("reply_message")
    private String replyMessage;

    /**
     * 回复机构编码
     */
    @TableField("reply_org_code")
    private String replyOrgCode;

    /**
     * 回复机构名称
     */
    @TableField("reply_org_name")
    private String replyOrgName;

    /**
     * 回复人id
     */
    @TableField("reply_user_id")
    private String replyUserId;

    /**
     * 回复人名称
     */
    @TableField("reply_user_name")
    private String replyUserName;

    /**
     * 回复类型
     */
    @TableField("reply_type")
    private Integer replyType;

    /**
     * 扩展字段
     */
    @TableField("extended_fields")
    private String extendedFields;

    /**
     * 备用字段
     */
    @TableField("diy_column")
    private String diyColumn;

    public WarnMessageReply() {
        this.setDelFlag(false);
        this.setCreateTime(new Date());
    }
}
