package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * quartz表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QuartzJobInfo extends BaseEntity{

    private static final long serialVersionUID = 1L;

    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务组
     */
    private String jobGroup;

    /**
     * 任务类型job_class_name
     */
    private String jobClassName;

    /**
     * 触发器名tigger_name
     */
    private String tiggerName;

    /**
     * 触发器组名称
     */
    private String tiggerGroupName;

    /**
     * 探水设计id
     */
    private String checkPlanId;

    /**
     * 是否进行探水
     */
    private Boolean isGround;

}
