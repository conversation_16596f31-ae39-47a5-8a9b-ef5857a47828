package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * 贯通工作面关联表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RelevanceWork extends BaseEntity{

    private static final long serialVersionUID = 1L;

    /**
     * 关联贯通工作面设计id   one
     */
    private String designIdOrdinaryOne;

    /**
     * 关联贯通工作面设计id   two
     */
    private String designIdOrdinaryTwo;

    /**
     * 关联贯通工作面设计id   切眼
     */
    private String designIdThroughCut;
}
