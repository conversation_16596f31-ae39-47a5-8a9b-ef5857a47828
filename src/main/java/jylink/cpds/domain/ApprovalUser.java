package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户、职务表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApprovalUser  extends  BaseEntity{

    private static final long serialVersionUID = 1L;

    /**
     * 配置表主键id
     */
    private String processId;

    /**
     * 用户id
     */
    private String customValue;

    /**
     * 用户名
     */
    private String customName;

    /**
     * 职务类型
     */
    private String customType;

    /**
     * 审批顺序 (0表示发起人)
     */
    private int approvalOrder;

    /**
     * 显示顺序
     */
    private int displayOrder;



}
