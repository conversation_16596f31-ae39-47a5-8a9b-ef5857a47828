package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class OperationsTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;
    private String mainCategory;
    private String secondCategory;
    private String content;
    private String type;
    private String orgCode;

}
