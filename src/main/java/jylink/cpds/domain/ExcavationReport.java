package jylink.cpds.domain;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
    * 掘进汇报表
    * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ExcavationReport  extends BaseEntity{

    private static final long serialVersionUID = 1L;

    /**
     * 模块名称
     */
    public static final String MODULE_NAME = "excavationReport";
    /**
     * 探水设计ID
     */

     private String tunnelId;

    /**
     * 工作面名称
     */

     private String workName;

    /**
     * 汇报人
     */

     private String reportUserName;

    /**
     * 汇报时间
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
     private Date reportTime;

    /**
     * 作业日期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
     private Date workDate;

    /**
     * 作业班次ID
     */

     private String workClassNumKey;

    /**
     * 作业班次名称
     */

     private String workClassNumValue;

    /**
     * 累计进尺
     */

     private Double accumulatedFootage;
    /**
     * 当日累计
     */
    private Double accumulativeTotal;

    /**
     * 当班进尺
     */

     private Double dutyFootage;

    /**
     * 普通汇报矫正汇报标识（0普通汇报   1 矫正汇报）
     */

     private String isCorrectReport;
    /**
     * 审批状态
     */
    private Integer status;
}
