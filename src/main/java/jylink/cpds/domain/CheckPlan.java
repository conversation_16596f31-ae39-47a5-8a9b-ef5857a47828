package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * 探水计划
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CheckPlan extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 模块名称
     */
    public static final String MODULE_NAME = "checkPlan";

    /**
     * 三视图
     */
    public static final String DRAWING_VIEW = "three_view_drawing";
    /**
     * 施工组织
     */
    public static final String ORGANIZATION_OF_CONSTRUCTION = "organiz_of_con";
    /**
     * 技术交底
     */
    public static final String TECHNICAL_DISCLOSURE = "technical_disclosure";

    /**
     * 巷道数据Id
     */
    private String tunnelId;

    /**
     * 巷道名称
     */
    private String workName;

    /**
     * 施工单位
     */
    private String workOrgName;

    /**
     * 探水地点
     */
    private String detectionPlace;

    /**
     * 探测位置
     */
    private Double checkPosition;

    /**
     * 预计探水时间
     */
    private Date predictionDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否查看(监管单位)
     */
    private Boolean isViewed;

    /**
     * 状态
     */
    private int status;

    /**
     * 上报时间
     */
    private Date uploadTime;

    /**
     * 提交审批人
     */
    private String approvalName;

    /**
     * 提交审批时间
     */
    private Date approvalTime;

    /**
     * 创建人Id
     */
    private String createUserId;
    /**
     * 施工单位Id
     */
    private String workOrgId;
    /**
     * 施工班次Id
     */
    private String classId;
    /**
     * 施工班次
     */
    private String classNoName;

    /**
     * 是否添加停止掘进通知单
     */
    private boolean isAddStopNotices;

    /**
     * 是否添加探水通知单
     */
    private boolean isAddSurveyNotices;

    /**
     * 是否第一次探水
     */
    private boolean isFirst;

    /**
     * 停止掘进通知单内容
     */
    private String stopNoticeContent;

    /**
     * 探水通知单内容
     */
    private String surveyNoticeContent;

    /**
     * 钻探目的
     */
    private Integer drillingItem;

    /**
     * 是否加密钻孔
     */
    private Integer encryptedDrilling;

    /**
     * 是否钻探异常
     */
    private Integer drillingAnomaly;

    /**
     * 物探管理id
     */
    private String geophysicalId;

    /**
     * 探水次数
     */
    private Integer explorationWaterNumber;

}
