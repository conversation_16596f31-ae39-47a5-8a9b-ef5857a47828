package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class AnalysisHistoryWork extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 孔表Id
     */
    private String holeDetailId;

    /**
     * 海康9800平台机构编码
     */
    private String hkOrgCode;

    /**
     * 海康9800平台设备编码
     */
    private String indexCode;

    /**
     * 通道号
     */
    private Integer channel;

    /**
     * 摄像机名称
     */
    private String cameraName;

    /**
     * 开始分析时间
     */
    private Date startTime;

    /**
     * 结束分析时间
     */
    private Date endTime;

    /**
     * 下发任务用户id
     */
    private String userId;

    /**
     * 任务状态
     */
    private int status;

    /**
     * 历史分析视频流地址
     */
    private String diyColumn;

}
