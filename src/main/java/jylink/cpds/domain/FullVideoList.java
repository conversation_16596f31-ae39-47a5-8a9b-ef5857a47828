package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @DESCRIPTION: 大视频分段表
 * @author: hanpt
 * @DATE: 2021/4/30 15:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FullVideoList extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * history_video 表uuid
     */
    private String fullVideoId;
    /**
     * 是否剪辑成功
     */
    private boolean succFlag;
    /**
     * 视频地址
     */
    private String videoUrl;
    /**
     * 排序
     */
    private int sort;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
}
