package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 技术交底书同意记录(AgreeLog)实体类
 *
 * <AUTHOR>
 * @since 2020-03-13 16:27:58
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AgreeLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
    * 台账id
    */
    private String drainId;
    /**
    * 用户id
    */
    private String userId;
    /**
    * 用户名
    */
    private String userName;
    
    private Date aggreeTime;











}