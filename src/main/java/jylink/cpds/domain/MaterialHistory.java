package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * (MaterialHistory)实体类
 *
 * <AUTHOR>
 * @since 2020-06-24 10:19:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MaterialHistory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
    * 材料id
    */
    private String materialId;
    /**
    * 材料名称
    */
    private String materialName;
    /**
    * 更新人id
    */
    private String updateUserId;
    /**
    * 更新人姓名
    */
    private String updateUserName;
    /**
    * 更新时间
    */
    private Date updateTime;

    /**
     * 周期说明
     */
    private String materialCycleDesc;

}