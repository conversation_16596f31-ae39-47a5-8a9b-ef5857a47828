package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * 审批数据历史表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApprovalHistoryWorkflow extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * tfs_approval_historys表Id
     */
    private String historyId;

    /**
     * 用户Id
     */
    private String userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 职务名称
     */
    private String dutyName;

    /**
     * 审批顺序 (0表示发起人)
     */
    private int approvalOrder;

    /**
     * 显示顺序
     */
    private int displayOrder;

    /**
     * 审批时间
     */
    private Date approvedTime;

    /**
     * 审批状态(记录每位审批人的状态)
     */
    private int approvedStatus;

    /**
     * 同意审批原因
     */
    private String approvalReason;
}
