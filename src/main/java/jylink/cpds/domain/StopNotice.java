package jylink.cpds.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * 停止掘进通知单
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StopNotice extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 计划Id
     */
    private String planId;

    /**
     * 编号
     */
    private String number;

    /**
     * 施工单位(接受单位)
     */
    private String workOrgName;
    private String workOrgId;

    /**
     * 停止掘进的工作面名称
     */
    private String stopWorkName;

    /**
     * 停止掘进处
     */
    private Double stopPosition;

    /**
     * 下发单位
     */
    private String issuedOrg;

    /**
     * 下发人
     */
    private String issuedName;

    /**
     * 接受人
     */
    private String receiveName;

    /**
     * 下发时间
     */
    private Date issuedDate;

    /**
     * 状态
     */
    private int status;

    /**
     * 上报时间
     */
    private Date uploadTime;

    /**
     * 探水时间  根据探水台账表tfs_drain_accounts  根据ID查找
     */
    private Date surveyWaterDate;

    /**
     * 上次探水时间
     */
    private Date lastExplorationTime;

    /**
     * 上次探水班次
     */
    private String lastExplorationClass;

    /**
     * 停止掘进时间
     */
    private Date stopDrillingTime;

    /**
     * 停止掘进班次
     */
    private String stopDrillingClass;

    /**
     * 累计掘进米数
     */
    private Double cumulativeDrivingMetres;
    /**
     * 预计探水时间
     */
    private Date estimatedDetectionTime;

    /**
     * 预计探水班次
     */
    private String estimatedDetectionClass;

    /**
     * 接收时间
     */
    private Date receivingTime;

    /**
     * 地测科科长姓名
     */
    private String geodesySectionChiefName;

    /**
     * 总工程师姓名
     */
    private String chiefEngineerName;

    /**
     * 上次探水班次Id
     */
    private String lastExplorationClassId;

    /**
     * 停止掘进班次Id
     */
    private String stopDrillingClassId;

    /**
     * 预计探水班次Id
     */
    private String estimatedDetectionClassId;

    private String lastCheckPosition;
    private String allowableSurvey;
    private String checkPosition;
    private String drivingDistance;
}
