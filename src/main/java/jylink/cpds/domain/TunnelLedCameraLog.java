package jylink.cpds.domain;

import lombok.Data;

import java.util.Date;


/**
 * (TunnelLedCameraLog)实体类
 *
 * <AUTHOR>
 * @since 2023-06-09 14:22:03
 */
@Data
public class TunnelLedCameraLog extends BaseEntity {
   /**
    * 扩展字段
    */
    private String extendedFields;
   /**
    * 摄像机IP
    */
    private String cameraIp;
   /**
    * 摄像机名称
    */
    private String cameraName;
   /**
    * 工作面id
    */
    private String workFaceId;
   /**
    * 工作面名称
    */
    private String workName;
   /**
    * led  id
    */
    private String ledId;
   /**
    * 煤矿大脑id
    */
    private String configId;
   /**
    * 绑定时间
    */
    private Date bindTime;
   /**
    * 绑定用户id
    */
    private String bindUserId;
   /**
    * 绑定用户名称
    */
    private String bindUserName;
   /**
    * 解绑时间
    */
    private Date unbindTime;
   /**
    * 解绑用户id
    */
    private String unbindUserId;
   /**
    * 解绑用户名称
    */
    private String unbindUserName;

    /**
     * 扩展字段
     */
    private String diyColumn;

    /**
     * 海康编码
     */
    private String indexCode;

    /**
     * 通道号
     */
    private Integer channel;

}
