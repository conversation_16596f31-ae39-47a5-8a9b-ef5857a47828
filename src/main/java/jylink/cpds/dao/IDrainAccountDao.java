package jylink.cpds.dao;

import jylink.cpds.domain.DrainAccount;
import jylink.cpds.serviceModel.BaseItem;
import jylink.cpds.serviceModel.dto.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 探水台账
 */
@Mapper
public interface IDrainAccountDao {
    /**
     * 根据机构编码查询
     *
     * @param orgCode 机构编码
     * @return 对象实例
     */
    List<DrainAccount> getOrgCode(@Param("orgCode") String orgCode);

    /**
     * 根据id查询
     *
     * @param id uuid
     * @return 对象实例
     */
    DrainAccount getById(@Param("id") String id);

    /**
     * 根据探水计划id查询
     *
     * @param checkPlanId checkPlanId
     * @return 对象实例
     */
    DrainAccount getBycheckPlanId(@Param("checkPlanId") String checkPlanId, @Param("orgCode") String orgCode);

    /**
     * 根据id查询
     *
     * @param ids uuid
     * @return 对象实例
     */
    List<DrainAccount> getByListId(@Param("ids") List<String> ids, @Param("orgCode") String orgCode);

    /**
     * 根据id查询
     *
     * @param ids uuid
     * @return 对象实例
     */
    List<DrainAccount> getByIds(@Param("ids") List<String> ids);

    /**
     * 根据工作面名称和探水时间和状态查询
     *
     * @param startDate    探水时间
     * @param workName           根据工作面名称
     * @param orgCode            机构编码
     * @param status             状态
     * @param endDate 是否完成台账填写
     * @return 对象实例
     */
    List<DrainAccount> getByWorkAndDate(@Param("workName") String workName,
                                        @Param("startDate") String  startDate,
                                        @Param("endDate") String  endDate,
                                        @Param("orgCode") String orgCode,
                                        @Param("status") Integer status);

    /**
     * 根据探水时间获取所有的探水工作面
     *
     * @param orgCode 机构编码
     * @return 对应的工作面
     */
    List<DrainAccount> getSurveyWaterMileageTree(@Param("orgCode") String orgCode);

    /**
     * 根据工作面查询所有的探水里程
     *
     * @param orgCode  机构编码
     * @param workName 工作面名称
     * @return 对应的工作面所有的探水里程surveyWaterMileage
     */
    List<DrainAccount> getByWorkForMileage(@Param("workName") String workName, @Param("orgCode") String orgCode);

    /**
     * 更新对应的子表
     *
     * @param id                     台账id
     * @param isAddStopNotice        停止掘进
     * @param isAddSurveyNotice      探水通知单
     * @param isAddAcceptanceCheck   探放水钻孔验收表
     * @param isAddTransferDetection 探放水安全确认移交表
     * @param isAddAllowableDriving  允许掘进通知单
     * @param isAddSummaryNotice     探访水钻空验收表
     * @return 是否修改成功
     */
    Boolean updateSublist(@Param("id") String id, @Param("isAddStopNotice") boolean isAddStopNotice,
            @Param("isAddSurveyNotice") boolean isAddSurveyNotice,
            @Param("isAddAcceptanceCheck") boolean isAddAcceptanceCheck,
            @Param("isAddTransferDetection") boolean isAddTransferDetection,
            @Param("isAddAllowableDriving") boolean isAddAllowableDriving,
            @Param("isAddSummaryNotice") boolean isAddSummaryNotice);

    /**
     * 添加数据
     *
     * @param instance 实体对象
     * @return 是否添加成功
     */
    Boolean add(@Param("instance") DrainAccount instance);

    /**
     * 更新数据
     *
     * @param instance 实体对象
     * @return 是否修改成功
     */
    Boolean update(@Param("instance") DrainAccount instance);

    /**
     * 删除数据
     *
     * @param id 数据id
     * @return 是否删除成功
     */
    Boolean delete(@Param("id") String id, @Param("orgCode") String orgCode);

    /**
     * 上报
     *
     * @param id         数据id
     * @param orgCode    机构编码
     * @param uploadTime 上报时间
     * @return 是否上报成功
     */
    boolean upload(@Param("id") String id, @Param("orgCode") String orgCode, @Param("uploadTime") Date uploadTime);

    /**
     * 是否完成上传
     *
     * @param id      数据id
     * @param orgCode 机构编码
     * @return 是否生成成功
     */
    boolean anyUploaded(@Param("id") String id, @Param("orgCode") String orgCode);

    /**
     * 生成台账
     *
     * @param id      数据id
     * @param orgCode 机构编码
     * @return 是否生成成功
     */
    boolean uploadDrainid(@Param("id") String id, @Param("orgCode") String orgCode,
            @Param("isAccomplish") Boolean isAccomplish);

    /**
     * 查看
     *
     * @param id      数据id
     * @return 是否上报成功
     */
    boolean view(@Param("id") String id);

    /**
     * 判断数据是否存在
     *
     * @param id      数据Id
     * @return 数据是否存在
     */
    boolean anyById(@Param("id") String id);

    /**
     * 判断数据是否查看
     *
     * @param id      数据Id
     * @param orgCode 机构编码
     * @return 数据是否查看
     */
    boolean isViewed(@Param("id") String id, @Param("orgCode") String orgCode);

    /**
     * 根据机构编码查询所有工作面名称
     *
     * @param orgCode 机构编码
     * @return 该机构下的所有工作面名称
     */
    List<String> getWorkName(@Param("orgCode") String orgCode);

    /**
     * 根据机构编码和工作面名称 查询数据
     *
     * @param orgCode  机构编码
     * @param workName 工作面名称
     * @return 数据
     */
    List<DrainAccount> getByWorkName(@Param("orgCode") String orgCode, @Param("workName") String workName);

    /**
     * 根据机构编码查询所有探水时间
     *
     * @param orgCode 机构编码
     * @return 该机构下的所有探水时间
     */
    List<String> getSurveyWaterDate(@Param("orgCode") String orgCode);

    /**
     * 根据机构编码查询和工作面名称 查询探水里程
     *
     * @param orgCode  机构编码
     * @param workName 工作面名称
     * @return 该机构下的所有探水里程
     */
    List<Integer> getSurveyWaterMileage(@Param("orgCode") String orgCode, @Param("workName") String workName);

    /**
     * 查询数据条数
     *
     * @param orgCode         机构编码
     * @param workName        工作面名称
     * @param surveyWaterDate 年份
     * @return 数据条数
     */
    long getCount(@Param("orgCode") String orgCode, @Param("workName") String workName,
            @Param("surveyWaterDate") int surveyWaterDate);

    /**
     * 查询数据条数 生成台账的
     *
     * @param orgCode            机构编码
     * @param workName           工作面名称
     * @param startDate    年份
     * @param status             数据状态
     * @param endDate
     * @return 数据条数
     */
    long getCountByStatus(@Param("orgCode") String orgCode, @Param("workName") String workName,
                          @Param("startDate") String  startDate,
                          @Param("endDate") String  endDate, @Param("status") Integer status);

    /**
     * 查询表单数据条数
     *
     * @param orgCode                机构编码
     * @param workName               工作面名称
     * @param surveyWaterDate        年份
     * @param isAddStopNotice        停止掘进
     * @param isAddSurveyNotice      探水通知单
     * @param isAddAcceptanceCheck   探放水钻孔验收表
     * @param isAddTransferDetection 探放水安全确认移交表
     * @param isAddAllowableDriving  允许掘进通知单
     * @param isAddSummaryNotice     探访水钻空验收表
     * @return 数据条数
     */
    long getFormCount(@Param("orgCode") String orgCode, @Param("workName") String workName,
            @Param("surveyWaterDate") int surveyWaterDate, @Param("isAddStopNotice") boolean isAddStopNotice,
            @Param("isAddSurveyNotice") boolean isAddSurveyNotice,
            @Param("isAddAcceptanceCheck") boolean isAddAcceptanceCheck,
            @Param("isAddTransferDetection") boolean isAddTransferDetection,
            @Param("isAddAllowableDriving") boolean isAddAllowableDriving,
            @Param("isAddSummaryNotice") boolean isAddSummaryNotice);

    /**
     * 查询表单数据 根据工作面名称和探水时间和状态查询
     *
     * @param surveyWaterDate        探水时间
     * @param workName               根据工作面名称
     * @param orgCode                机构编码
     * @param isAddStopNotice        停止掘进
     * @param isAddSurveyNotice      探水通知单
     * @param isAddAcceptanceCheck   探放水钻孔验收表
     * @param isAddTransferDetection 探放水安全确认移交表
     * @param isAddAllowableDriving  允许掘进通知单
     * @param isAddSummaryNotice     探访水钻空验收表
     * @return 对象实例
     */
    List<DrainAccount> getForm(@Param("orgCode") String orgCode, @Param("workName") String workName,
            @Param("surveyWaterDate") int surveyWaterDate, @Param("isAddStopNotice") boolean isAddStopNotice,
            @Param("isAddSurveyNotice") boolean isAddSurveyNotice,
            @Param("isAddAcceptanceCheck") boolean isAddAcceptanceCheck,
            @Param("isAddTransferDetection") boolean isAddTransferDetection,
            @Param("isAddAllowableDriving") boolean isAddAllowableDriving,
            @Param("isAddSummaryNotice") boolean isAddSummaryNotice);

    /**
     * 数据状态查询
     *
     * @param id      数据Id
     * @param orgCode 机构编码
     * @return 数据状态枚举
     */
    int getDataStatus(@Param("id") String id, @Param("orgCode") String orgCode);

    /**
     * 查询探水概括所需要的信息
     *
     * @param orgCode 机构编码
     * @return 该机构下的探水时间、工作面名称、id、探水设计表id
     */
    DrainAccount getTransferGeneralByWorkName(@Param("workName") String workName, @Param("orgCode") String orgCode);

    /**
     * 根据机构编码查询探水概括所需要的信息
     *
     * @param orgCode 机构编码
     * @return 探水概括所需要的信息
     */
    List<DrainAccount> getByIdTransferGeneral(@Param("orgCode") String orgCode);

    /**
     * 根据探水设计id查询探水概括所需要的信息
     *
     * @param tunnelId 探水设计id
     * @param orgCode  机构编码
     * @return 探水概括所需要的信息
     */
    DrainAccount getTransferGeneralById(@Param("tunnelId") String tunnelId, @Param("orgCode") String orgCode);

    /**
     * 修改验收表的添加状态
     *
     * @param acceptStatus 状态
     * @param id           数据Id
     * @param orgCode      机构编码
     * @return 是否修改成功
     */
    boolean updateAcceptStatus(@Param("acceptStatus") boolean acceptStatus, @Param("id") String id,
            @Param("orgCode") String orgCode);

    /**
     * 根据探水设计Id查询数据
     *
     * @param tunnelId 探水设计Id
     * @return DrainAccount对象集合
     */
    List<DrainAccount> getByTunnelId( @Param("tunnelId") String tunnelId ,@Param("cameraIp") String cameraIp);

    /**
     * 根据探水设计id和是否完成查询数据
     *
     * @param orgCode  机构编码
     * @param tunnelId 探水设计id
     * @return
     */
    List<DrainAccount> getByTunnelIdAndAccomplish(@Param("orgCode") String orgCode, @Param("tunnelId") String tunnelId);

    /**
     * 根据探水设计Id查询数据 倒叙排列
     *
     * @param orgCode  机构编码
     * @param tunnelId 探水设计Id
     * @return DrainAccount对象集合
     */
    List<DrainAccount> getByTunnelIdDesc(@Param("orgCode") String orgCode, @Param("tunnelId") String tunnelId);

    /**
     * 查询探水里程是否重复
     *
     * @param tunnelId 探水设计id
     * @param orgCode  机构编码
     * @return 查询结果
     */
    boolean anyBySurveyWaterMileage(@Param("tunnelId") String tunnelId,
            @Param("surveyWaterMileage") Double surveyWaterMileage, @Param("orgCode") String orgCode);

    /**
     * 判断该工作面探水时间是否重复
     *
     * @param tunnelId        探水设计id
     * @param surveyWaterDate 探水时间
     * @param orgCode         机构编码
     * @return 查询结果
     */
    boolean anyBySurveyWaterDate(@Param("tunnelId") String tunnelId, @Param("surveyWaterDate") Date surveyWaterDate,
            @Param("orgCode") String orgCode);

    /**
     * 数据查询
     *
     * @param orgCode            机构编码
     * @param workName           工作面名称
     * @param surveyWaterMileage 探水里程
     * @return 探水台账实体对象集合
     */
    DrainAccount getByWorkNameAndSurveyWaterMileage(@Param("orgCode") String orgCode,
            @Param("workName") String workName, @Param("surveyWaterMileage") int surveyWaterMileage);

    /**
     * 获取一个月内煤矿的探水次数
     *
     * @param orgCode 机构编码
     * @param year    年份
     * @param month   月份
     * @return 次数
     */
    Integer getTimes(@Param("orgCode") String orgCode, @Param("year") int year, @Param("month") int month);

    /**
     * 根据工作面编码和里程查询数据
     * 
     * @param orgCode            机构编码
     * @param tunnelId           工作面编码
     * @param surveyWaterMileage 探水里程
     * @return 查询结果
     */
    List<DrainAccount> getByCodeAndMileage(@Param("orgCode") String orgCode, @Param("tunnelId") String tunnelId,
            @Param("surveyWaterMileage") Double surveyWaterMileage);

    /**
     * 修改探水台帐是否添加总结表状态
     *
     * @param id      主键id
     * @param orgcode 机构编码
     * @return 是否成功
     */
    boolean updateSummaryState(@Param("id") String id, @Param("orgcode") String orgcode, @Param("flag") boolean flag);

    /**
     * 根据设计Id集合查询数据
     *
     * @param orgCode 机构编码
     * @param ids     设计Id集合
     * @return 查询结果
     */
    List<DrainAccount> getByTunnelIds(@Param("orgCode") String orgCode, @Param("ids") List<String> ids);

    /**
     * 修改探水台账的用户id
     * 
     * @param id
     * @param orgCode
     * @param userId
     * @return
     */
    boolean updateUserId(@Param("id") String id, @Param("orgCode") String orgCode, @Param("userId") String userId);

    /**
     * @auth yyy 根据探水里程，计划ID,机构编码查询台账数据
     * @param surveyWaterMileage 探水里程
     * @param orgCode            机构编码
     * @param tunnelId           设计Id
     * @return 查询结果
     */
    List<DrainAccount> getByTunnalIdAndSurveyWaterMileage(@Param("surveyWaterMileage") Double surveyWaterMileage,
            @Param("orgCode") String orgCode, @Param("tunnelId") String tunnelId);

    /**
     * 获取最后一次探水的数据
     * @param orgCode 机构编码
     * @return 查询结果
     */
    DrainAccount getLatestWaterDateAccount(@Param("orgCode")String orgCode);

    /**
     * 更新台账表状态
     * @param id
     * @return
     */
    boolean updateDrainStatus(@Param("id") String id);

    /**
     * 更新煤层倾角
     * @param coalAngle  煤层倾角
     * @param id
     * @return
     */
    boolean updateCoalAngle(@Param("coalAngle") double coalAngle,@Param("id") String id);

    /**
     * 根据台账id修改钻孔内容
     * @param id  台账id
     * @param orgCode  机构编码
     * @param allowableNoticeContent  允许掘进通知单内容
     * @return  是否修改成功
     */
    boolean updateAllowNoticeNoticeContent(@Param("id") String id, @Param("orgCode") String orgCode,@Param("allowableNoticeContent") String allowableNoticeContent);

    /**
     * 查询允许掘进通知单内容
     * @param id  台账id
     * @return  允许掘进通知单内容
     */
    Map<String, String> getAllowNoticeNoticeContent(@Param("id") String id);

    /**
    * 根据用户id 查询台账记录
    * <AUTHOR>
    * @date 2020/3/16 15:27
    * @return  java.util.List<jylink.cpds.domain.DrainAccount>
    */
    List<DrainAccount> getTunnelInfoByUser(@Param("workUserId") String workUserId, @Param("orgCode") String orgCode);

    /**
     * 获取正在识别的任务
     * @param orgCodes  机构编码集合
     * @return  查询结果
     */
    List<OrgCameraTreeDto> getAnalysisNum(@Param("orgCodes") List<String> orgCodes);

    /**
     * 获取存在探水设计的机构
     * @param orgCodes  机构编码
     * @return  查询结果
     */
    List<String> getTunnelOrgCodes(@Param("orgCodes") List<String> orgCodes);

    /**
     * 获取钻探次数信息
     * @param orgCodes
     * @param type
     * @return
     */
    PlanDrillInfoDto getPlanDrillNum(@Param("orgCodes") List<String> orgCodes , @Param("type")  Integer type);

    /**
     * 根据类型获取异常情况
     * @param orgCodes  机构编码列表
     * @param type  时间类型
     * @return  查询结果
     */
    List<BaseItem> getAbnormalNumInfo(@Param("orgCodes") List<String> orgCodes , @Param("type")  Integer type , @Param("orgName") String orgName , @Param("startDate") String startDate ,@Param("endDate")  String endDate);

    /**
     * 获取近几天且未开始任务的计划
     * @param orgCodes  机构编码集合
     * @param day  日期
     * @return
     */
    Integer getNoDrillPlanNum(@Param("orgCodes") List<String> orgCodes , @Param("day")  Integer day);

    /**
     * 获取全部正在探水的台账列表
     * @param orgCodes  机构编码列表
     * @return
     */
    List<DrainAccount> getAnalysisDrainList(@Param("orgCodes") List<String> orgCodes , @Param("orgName") String orgName);

    /**
     *
     * @param orgCodes
     * @param orgName
     * @return
     */
    List<RealWaterDetectionDetailDto> getRealWaterDetection(@Param("orgCodes") List<String> orgCodes , @Param("orgName") String orgName);

    /**
     *
     * @param orgCodes
     * @param orgName
     * @param day
     * @param orderType
     * @return
     */
    List<NoAnalysisPlanListDto> getNoAnalysisList(@Param("orgCodes") List<String> orgCodes , @Param("orgName") String orgName, @Param("day")  Integer day , @Param("orderType") Integer orderType);

    /**
     *
     * @param orgCodes
     * @param orgName
     * @param type
     * @param analysisType
     * @return
     */
    RealWaterDetectionDto getDrillPlanNum(@Param("orgCodes") List<String> orgCodes , @Param("orgName") String orgName, @Param("type")  Integer type , @Param("analysisType") Integer analysisType ,@Param("orgCode") String orgCode);

    /**
     *
     * @param orgCodes
     * @param orgName
     * @param type
     * @param analysisType
     * @return
     */
    List<DrillPlanListDto> getDrillPlanList(@Param("orgCodes") List<String> orgCodes , @Param("orgName") String orgName, @Param("type")  Integer type , @Param("analysisType") Integer analysisType , @Param("tunnelId") String tunnelId,@Param("orgCode") String orgCode,@Param("result") Boolean result);

    /**
     * 获取钻探管理计划个数
     * @param orgCodes
     * @param orgName
     * @param type
     * @param analysisType
     * @param tunnelId
     * @param orgCode
     * @return
     */
    long getDrillPlanCount(@Param("orgCodes") List<String> orgCodes , @Param("orgName") String orgName, @Param("type")  Integer type , @Param("analysisType") Integer analysisType , @Param("tunnelId") String tunnelId,@Param("orgCode") String orgCode,@Param("result") Boolean result);

    /**
     *
     * @param orgCode
     * @return
     */
    DrillPlanListDto getLastDrillInfo(@Param("orgCode") String orgCode);

    /**
     * 获取杆异常个数
     * @param orgCodes
     * @param type
     * @param abnormalType
     * @param orgName
     * @param startDate
     * @param endDate
     * @return
     */
    long getPoleAbnormalNum(@Param("orgCodes") List<String> orgCodes , @Param("type")  Integer type , @Param("abnormalType")  Integer abnormalType , @Param("orgName") String orgName , @Param("startDate") String startDate ,@Param("endDate")  String endDate, @Param("orgCode") String orgCode , @Param("tunnelId") String tunnelId);

    /**
     *
     * @param orgCodes
     * @param type
     * @param orgName
     * @param startDate
     * @param endDate
     * @return
     */
    List<PoleAbnormalInfoDto> getPoleAbnormalList(@Param("orgCodes") List<String> orgCodes , @Param("type")  Integer type , @Param("abnormalType")  Integer abnormalType , @Param("orgName") String orgName , @Param("startDate") String startDate ,@Param("endDate")  String endDate , @Param("orgCode") String orgCode , @Param("tunnelId") String tunnelId);

    /**
     * 获取最新的正在识别的台账数据
     * @param orgCode
     * @return
     */
    LastDrillInfoDto getAnalysisDrain(@Param("orgCode") String orgCode);

    /**
     * 获取未来尚未识别的台账数据
     * @param orgCode
     * @return
     */
    LastDrillInfoDto getExpectPlan(@Param("orgCode") String orgCode);

    /**
     * 获取已完成识别，且有视频地址的台账数据
     * @param orgCode
     * @return
     */
    LastDrillInfoDto getFinishDrill(@Param("orgCode") String orgCode);

    /**
     * 获取app统计数据
     * @param orgCode
     * @param year
     * @param month
     * @return
     */
    APPDataOverviewDto getAPPDataOverview(@Param("orgCode") String orgCode , @Param("year") Integer year , @Param("month") Integer month);

    /**
     * 获取探水总览详情
     * @param orgCode  机构编码
     * @param year  年份
     * @param month  月份
     * @return
     */
    List<DrillOverviewDetailDto> getDrillOverviewDetail(@Param("orgCode") String orgCode , @Param("year") Integer year , @Param("month") Integer month);

}
