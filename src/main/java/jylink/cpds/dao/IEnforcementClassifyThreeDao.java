package jylink.cpds.dao;

import jylink.cpds.domain.EnforcementClassifyThree;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (EnforcementClassifyThree)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-10-12 17:20:35
 */
public interface IEnforcementClassifyThreeDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    EnforcementClassifyThree queryById(@Param("id") String id, @Param("orgCode") String orgCode);

    
    /**
     * 通过实体作为筛选条件查询
     *
     * @param enforcementClassifyThree 实例对象
     * @return 对象列表
     */
    List<EnforcementClassifyThree> queryAll(EnforcementClassifyThree enforcementClassifyThree);
    /**
     * 通过实体作为筛选条件查询 count
     *
     * @param enforcementClassifyThree 实例对象
     * @return count
     */
    long queryAllCount(EnforcementClassifyThree enforcementClassifyThree);

    /**
     * 新增数据
     *
     * @param enforcementClassifyThree 实例对象
     * @return 影响行数
     */
    int insert(EnforcementClassifyThree enforcementClassifyThree);

    /**
     * 修改数据
     *
     * @param enforcementClassifyThree 实例对象
     * @return 影响行数
     */
    int update(EnforcementClassifyThree enforcementClassifyThree);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 根据二级编码和机构名称查询数据信息
     * @param twoCode  机构编码
     * @param orgCode  机构名称
     * @return  查询结果
     */
    List<EnforcementClassifyThree> getByCode(@Param("twoCode") String twoCode,@Param("orgCode")String orgCode);

    /**
     * 根据一级编码查询全部三级数据信息
     * @param oneCode 一级编码
     * @param orgCode  机构编码
     * @return  查询结果
     */
    List<EnforcementClassifyThree> getByOneCode(@Param("oneCode") String oneCode,@Param("orgCode")String orgCode);

}