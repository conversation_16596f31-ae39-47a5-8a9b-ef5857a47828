package jylink.cpds.dao;

import jylink.cpds.domain.OperationsDing;
import jylink.cpds.domain.OperationsMonitor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @DESCRIPTION: 运维监控dao
 * @author: hanpt
 * @DATE: 2021/9/3 10:49
 */
@Mapper
public interface IOperationsMonitorDao {

    /**
     *  查询所有数据
     * <AUTHOR>
     * @date 2021/9/3 11:03
     * @param
     * @return java.util.List<jylink.cpds.domain.OperationsMonitor>
     */
    List<OperationsMonitor> getAll();

    /**
     *  删除数据
     * <AUTHOR>
     * @date 2021/9/3 13:12
     * @param id 主键id
     * @return int
     */
    int delMonitor(@Param("id")String id);

    /**
     *  根据机构编码获取要推送的钉钉群 和人员
     * <AUTHOR>
     * @date 2021/11/4 11:07
     * @param orgCode 机构编码
     * @return jylink.cpds.domain.OperationsDing
     */
    List<OperationsDing> getPushSetting();
    /**
     *  根据类型区分发的矿
     * <AUTHOR>
     * @date 2022/7/18 9:49
     * @param dingType 钉钉类型
     * @return jylink.cpds.domain.OperationsDing
     */
    OperationsDing selInfoByDingType(@Param("dingType")String dingType);

    /**
     *  批量入库 监控消息
     * <AUTHOR>
     * @date 2022/7/18 15:40
     * @param messages
     * @return int
     */
    int addMonitorMess(@Param("messages")List<String> messages);

    /**
     * 新增数据
     *
     * @param operationsMonitor 实例对象
     * @return 影响行数
     */
    int insert(OperationsMonitor operationsMonitor);

}
