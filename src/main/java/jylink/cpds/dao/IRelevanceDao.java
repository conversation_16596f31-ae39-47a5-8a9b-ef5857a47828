package jylink.cpds.dao;

import jylink.cpds.domain.RelevanceWork;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * 工作面关联Dao
 */
@Mapper
public interface IRelevanceDao {

    /**
     * 根据机构编码查询数据
     *
     * @param orgCode 机构编码
     * @return MineInfo对象实例
     */
    List<RelevanceWork> getByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 添加数据
     *
     * @param instance 实体对象
     * @return 是否添加成功
     */
    Boolean add(@Param("instance") RelevanceWork instance);

    /**
     * 批量添加
     *
     * @param list 实体集合
     * @return 添加结果
     */
    boolean batchAdd(@Param("list") List<RelevanceWork> list);
}
