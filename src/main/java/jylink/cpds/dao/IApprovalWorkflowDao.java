package jylink.cpds.dao;

import jylink.cpds.domain.ApprovalWorkflow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 审批流程表
 */
@Mapper
public interface IApprovalWorkflowDao {
    /**
     * 添加数据
     *
     * @param list 数据集合
     * @return 是否添加成功
     */
    boolean add(@Param("list") List<ApprovalWorkflow> list);

    /**
     * 同意审批
     *
     * @param dataId 数据Id
     * @param userId 用户Id
     * @param status 数据状态
     * @param reason 审批通过原因
     * @param time 用户审批时间
     * @return 是否成功
     */
    boolean approval(@Param("dataId") String dataId, @Param("userId") String userId,
                     @Param("status") int status, @Param("reason") String reason,
                     @Param("time") Date time);

    /**
     * 根据数据Id集合获取审批流程数据
     *
     * @param idList 数据Id集合
     * @return 审批数据集合
     */
    List<ApprovalWorkflow> getByDataIds(@Param("idList") List<String> idList);

    /**
     * 根据数据Id获取审批流程数据
     *
     * @param dataId 数据Id
     * @return 审批数据集合
     */
    List<ApprovalWorkflow> getByDataId(@Param("dataId") String dataId);

    /**
     * 根据数据Id和数据状态获取审批流程数据
     *
     * @param dataId 数据Id
     * @return 审批数据集合
     */
    List<ApprovalWorkflow> getByDataIdAndStatus(@Param("dataId") String dataId,@Param("status") int status);
    /**
     * 判断用户是否在审批队列中
     *
     * @param dataId 数据Id
     * @param userId 用户Id
     * @param status 数据状态
     * @return 查询结果
     */
    boolean anyByUserId(@Param("dataId") String dataId, @Param("userId") String userId, @Param("status") int status);

    /**
     * 查询批次中是否存在未完成的审批
     *
     * @param dataId 数据Id
     * @param userId 用户Id
     * @param status 数据状态
     * @return 查询结果
     */
    boolean hasApproved(@Param("dataId") String dataId, @Param("userId") String userId, @Param("status") int status);

    /**
     * 查询用户是否已审批
     *
     * @param dataId 数据Id
     * @param userId 用户Id
     * @param status 数据状态
     * @return 查询结果
     */
    boolean hasBeenApproved(@Param("dataId") String dataId, @Param("userId") String userId, @Param("status") int status);

    /**
     * 获取下一批次的Id
     *
     * @param dataId 数据Id
     * @param userId 用户Id
     * @return Id集合
     */
    List<String> getNextApprovalIds(@Param("dataId") String dataId, @Param("userId") String userId);

    /**
     * 获取下一批次的用户Id
     *
     * @param dataId 数据Id
     * @param userId 用户Id
     * @return Id集合
     */
    List<String> getNextApprovalusers(@Param("dataId") String dataId, @Param("userId") String userId);

    /**
     * 修改下一批次审批者的状态
     *
     * @param ids    数据Id集合
     * @param status 数据状态
     * @return 是否更新成功
     */
    boolean updateApprove(@Param("ids") List<String> ids, @Param("status") int status);

    /**
     * 将第一批次的审批数据修改为未审批
     *
     * @param dataId 数据Id集合
     * @param status 数据状态
     * @return 是否更新成功
     */
    boolean updateApproveForFirst(@Param("dataId") String dataId, @Param("status") int status);

    /**
     * 根据数据Id删除数据
     *
     * @param dataId 数据Id
     * @return 是否删除成功
     */
    boolean deleteByDataId(@Param("dataId") String dataId);

    /**
     * 重置审批状态
     *
     * @param dataId          数据Id
     * @param noApproveStatus 未审批状态
     * @param approveStatus   可审批的状态
     * @return 是否修改成功
     */
    boolean reset(@Param("dataId") String dataId,
                  @Param("noApproveStatus") int noApproveStatus,
                  @Param("approveStatus") int approveStatus);

    /**
     * 退回或打回 重置审批状态
     *
     * @param dataId 数据Id
     * @return 是否修改成功
     */
    boolean resetNoApproved(@Param("dataId") String dataId);

    /**
     * 根据用户id查询审批个数
     *
     * @param userId     用户id
     * @param status     审批状态
     * @param moduleName 模块名称
     * @return 探水计划实体对象集合
     */
    int getApprovalNum(@Param("userId") String userId, @Param("status") int status, @Param("moduleName") String moduleName);

    /**
     * 根据状态获取数据
     *
     * @param userId     用户Id
     * @param status     状态
     * @param moduleName 模块名称
     * @return 实体集合
     */
    List<ApprovalWorkflow> getApproving(@Param("userId") String userId, @Param("status") int status, @Param("moduleName") String moduleName);

    /**
     * 通过数据id查询数据
     *
     * @param dataId
     * @return json数据
     * @date 20190426
     */
    ApprovalWorkflow getByDataIdAndUserId(@Param("dataId") String dataId, @Param("userId")String userId);


}
