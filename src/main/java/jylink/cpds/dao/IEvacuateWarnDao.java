package jylink.cpds.dao;

import jylink.cpds.domain.EvacuateWarn;
import jylink.cpds.domain.EvacuateWarnPlus;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * (EvacuateWarn)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-16 11:28:21
 */
public interface IEvacuateWarnDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    EvacuateWarn queryById(String id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<EvacuateWarn> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param evacuateWarn 实例对象
     * @return 对象列表
     */
    List<EvacuateWarn> queryAll(EvacuateWarn evacuateWarn);

    /**
     * 新增数据
     *
     * @param evacuateWarn 实例对象
     * @return 影响行数
     */
    int insert(EvacuateWarn evacuateWarn);

    /**
     * 修改数据
     *
     * @param evacuateWarn 实例对象
     * @return 影响行数
     */
    int update(EvacuateWarn evacuateWarn);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    List<EvacuateWarn> queryAllByPage();
    long queryCount();

    List<EvacuateWarnPlus> queryWarnInfo(EvacuateWarnPlus evacuateWarnPlus);
}