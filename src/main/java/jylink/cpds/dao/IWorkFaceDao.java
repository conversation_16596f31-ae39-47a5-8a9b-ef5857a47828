package jylink.cpds.dao;

import jylink.cpds.domain.WorkFace;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作面管理(WorkFace)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-07-30 16:12:15
 */
public interface IWorkFaceDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    WorkFace queryById(@Param("id") String id, @Param("orgCode") String orgCode);

    List<WorkFace> queryBy(@Param("orgCode") String orgCode, @Param("workNameKeyWord") String workNameKeyWord);

    /**
     * 通过实体作为筛选条件查询cccccccccccccccccc
     *
     * @param workFace 实例对象
     * @return 对象列表
     */
    List<WorkFace> queryAll(WorkFace workFace);

    /**
     * 通过实体作为筛选条件查询 count
     *
     * @param workFace 实例对象
     * @return count
     */
    long queryAllCount(WorkFace workFace);

    /**
     * 新增数据
     *
     * @param workFace 实例对象
     * @return 影响行数
     */
    int insert(WorkFace workFace);

    /**
     * 修改数据
     *
     * @param workFace 实例对象
     * @return 影响行数
     */
    int update(WorkFace workFace);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 获取所有尚未添加设计的工作面
     * @param orgCode  机构编码
     * @return  查询结果
     */
    List<WorkFace> getList(@Param("orgCode")String orgCode);

    /**
     * 物理删除
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  是否删除成功
     */
    boolean delete(@Param("id") String id ,@Param("orgCode") String orgCode);

    /**
     * 根据工作面名称查询数据是否存在
     * @param workName  工作面名称
     * @param orgCode  机构编码
     * @return  查询结果
     */
    boolean anyByName(@Param("id") String id ,@Param("workName") String workName ,@Param("orgCode") String orgCode);

    /**
     * 根据设计id查询工作面管理相关数据
     * @param tunnelId  探水设计id
     * @param orgCode  机构编码
     * @return  查询结果
     */
    WorkFace getByTunnelId(@Param("tunnelId") String tunnelId ,@Param("orgCode") String orgCode);

    /**
     * 根据机构编码和工作面id修改工作面名称
     * @param orgCode  机构编码
     * @param workName  要修改的工作面名称
     * @param workFaceId  工作面id
     * @return  是否修改成功
     */
    void updateWorkName(@Param("orgCode") String orgCode,@Param("workName") String workName,@Param("workFaceId") String workFaceId);

}