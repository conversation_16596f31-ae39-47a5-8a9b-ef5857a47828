package jylink.cpds.dao;

import jylink.cpds.domain.SpecialOrgs;

import java.util.List;

public interface ISpecialOrgsDao {
    int insert(SpecialOrgs record);

    int insertSelective(SpecialOrgs record);

    List<SpecialOrgs> selListPage(SpecialOrgs record);
    long getCount(SpecialOrgs record);

    SpecialOrgs selSpecialOrgDetail(SpecialOrgs record);

    SpecialOrgs selSpecialOrgDetailAll(SpecialOrgs record);

    int delSpecialOrgs(String id);

    int updateSpecialOrgs(SpecialOrgs record);

    int updateSpecialOrgsByInsert(SpecialOrgs record);


}