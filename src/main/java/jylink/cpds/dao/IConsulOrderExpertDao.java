package jylink.cpds.dao;

import jylink.cpds.domain.ConsulOrderExpert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 订单对应的专家信息(ConsulOrderExpert)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-17 11:23:14
 */
@Mapper
public interface IConsulOrderExpertDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ConsulOrderExpert queryById(@Param("id") String id, @Param("orgCode") String orgCode);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param consulOrderExpert 实例对象
     * @return 对象列表
     */
    List<ConsulOrderExpert> queryAll(ConsulOrderExpert consulOrderExpert);

    /**
     * 通过实体作为筛选条件查询 count
     *
     * @param consulOrderExpert 实例对象
     * @return count
     */
    long queryAllCount(ConsulOrderExpert consulOrderExpert);

    /**
     * 新增数据
     *
     * @param consulOrderExpert 实例对象
     * @return 影响行数
     */
    int insert(ConsulOrderExpert consulOrderExpert);

    /**
     * 修改数据
     *
     * @param consulOrderExpert 实例对象
     * @return 影响行数
     */
    int update(ConsulOrderExpert consulOrderExpert);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);
    List<ConsulOrderExpert> selByOrderId(@Param("orderId") String orderId);

}