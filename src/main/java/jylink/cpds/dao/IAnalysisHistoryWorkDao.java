package jylink.cpds.dao;

import jylink.cpds.domain.AnalysisHistoryWork;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 历史分析表Dao服务
 */
@Mapper
public interface IAnalysisHistoryWorkDao {
    /**
     * 添加数据
     *
     * @param item 实体对象
     * @return 是否添加成功
     */
    boolean add(@Param("item") AnalysisHistoryWork item);

    /**
     * 根据验收表Id查询数据
     *
     * @param holeDetailId 孔表数据Id
     * @return 查询结果
     */
    List<AnalysisHistoryWork> getByHoleDetailId(@Param("holeDetailId") String holeDetailId);

    /**
     * 根据数据Id集合查询数据
     *
     * @param holeDetailIds 孔表数据Id集合
     * @return 数据对象集合
     */
    List<AnalysisHistoryWork> getByHoleDetailIds(@Param("holeDetailIds") List<String> holeDetailIds);

    /**
     * 判断详情id是否存在
     * 
     * @param holeDetailId
     * @return
     */
    boolean anyByHoleDetailId(@Param("holeDetailId") String holeDetailId);

    /**
     * 根据Id查询数据
     *
     * @param id 数据Id
     * @return 查询结果
     */
    AnalysisHistoryWork getById(@Param("id") String id);

    /**
     * 根据Id删除数据
     *
     * @param id      数据Id
     * @param orgCode 机构编码
     * @return 是否删除成功
     */
    boolean deleteById(@Param("id") String id, @Param("orgCode") String orgCode);

    /**
     * 判断数据是否存在
     *
     * @param id 数据Id
     * @return 查询结果
     */
    boolean anyById(@Param("id") String id);

    /**
     * 更新数据状态
     *
     * @param id     任务id
     * @param status 任务状态
     * @return 是否更新成功
     */
    boolean updateStatus(@Param("id") String id, @Param("status") int status);

    /**
     * 查询是否有正常结束的
     *
     * @param holeDetailId 钻孔Id
     * @param status       状态（正常结束）
     * @return 查询结果
     */
    boolean anyNormalFinish(@Param("holeDetailId") String holeDetailId, @Param("status") int status);

    /**
     * 根据id修改详情id
     *
     * @param id           主键id
     * @param holeDetailId 详情id
     * @return 是否更新成功
     */
    boolean updateHoleDetailId(@Param("id") String id, @Param("holeDetailId") String holeDetailId);

    /**
     * 根据孔表id集合删除数据
     * 
     * @param holeDetailIds 孔表id集合
     * @return 执行结果
     */
    boolean deleteByHoleDetailIds(@Param("holeDetailIds") List<String> holeDetailIds);
}
