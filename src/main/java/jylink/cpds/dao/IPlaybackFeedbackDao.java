package jylink.cpds.dao;

import jylink.cpds.domain.PlaybackFeedback;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 视频播放反馈表(PlaybackFeedback)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-24 18:35:20
 */
public interface IPlaybackFeedbackDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    PlaybackFeedback queryById(@Param("id") String id, @Param("orgCode") String orgCode);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param playbackFeedback 实例对象
     * @return 对象列表
     */
    List<PlaybackFeedback> queryAll(PlaybackFeedback playbackFeedback);

    /**
     * 通过实体作为筛选条件查询 count
     *
     * @param playbackFeedback 实例对象
     * @return count
     */
    long queryAllCount(PlaybackFeedback playbackFeedback);

    /**
     * 新增数据
     *
     * @param playbackFeedback 实例对象
     * @return 影响行数
     */
    int insert(PlaybackFeedback playbackFeedback);

    /**
     * 修改数据
     *
     * @param playbackFeedback 实例对象
     * @return 影响行数
     */
    int update(PlaybackFeedback playbackFeedback);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

}
