package jylink.cpds.dao;

import jylink.cpds.domain.InspectionConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (InspectionConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-22 18:59:48
 */
public interface IInspectionConfigDao {

    /**
     * 通过实体作为筛选条件查询
     *
     * @param inspectionConfig 实例对象
     * @return 对象列表
     */
    List<InspectionConfig> queryAll(InspectionConfig inspectionConfig);

}
