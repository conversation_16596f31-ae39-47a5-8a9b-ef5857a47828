package jylink.cpds.dao;

import jylink.cpds.domain.FormTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IFormTemplateDao {

    /**
     * 根据表单编码查询
     * @param formCode  表单编码
     * @return  查询结果
     */
    FormTemplate getByFormCode(@Param("formCode") int formCode, @Param("orgCode") String orgCode);

    /**
     * 根据id查询
     * @param id  表单编码
     * @param orgCode  机构编码
     * @return  查询结果
     */
    FormTemplate getById(@Param("id") String id, @Param("orgCode") String orgCode);

    /**
     * 添加数据
     * @param instance  数据实体
     * @return  是否添加成功
     */
    boolean add(@Param("instance") FormTemplate instance);

    /**
     * 修改数据
     * @param instance  数据实体
     * @return  是否添加成功
     */
    boolean update(@Param("instance") FormTemplate instance);

    /**
     * 根据id查询数据是否存在
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  是否存在
     */
    boolean anyById(@Param("id") String id,@Param("orgCode") String orgCode);

    /**
     * 根据表单编码查询数据是否存在
     * @param formCode  表单编码
     * @return  是否存在
     */
    boolean anyByFormCode(@Param("formCode") int formCode);
}
