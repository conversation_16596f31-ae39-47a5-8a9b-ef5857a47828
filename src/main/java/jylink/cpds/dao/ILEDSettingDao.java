package jylink.cpds.dao;

import jylink.cpds.domain.LEDSetting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ILEDSettingDao {
    /**
     * 根据机构编码查询数据
     *
     * @param orgCode 机构编码
     * @return 查询结果
     */
    List<LEDSetting> getByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 添加数据
     *
     * @param instance 数据实体
     * @return 是否添加成功
     */
    boolean add(@Param("instance") LEDSetting instance);

    /**
     * 查询数据是否存在
     *
     * @param orgCode 机构编码
     * @param id      数据Id
     * @return 查询结果
     */
    boolean anyById(@Param("orgCode") String orgCode, @Param("id") String id);

    /**
     * 更新数据
     *
     * @param instance 实体对象
     * @return 更新结果
     */
    boolean update(@Param("instance") LEDSetting instance);

    /**
     * 根据数据Id查询数据
     *
     * @param orgCode 机构编码
     * @param id      数据Id
     * @return 查询结果
     */
    LEDSetting getById(@Param("orgCode") String orgCode, @Param("id") String id);

    /**
     * 删除数据
     *
     * @param orgCode 机构编码
     * @param id      数据Id
     * @return 查询结果
     */
    boolean delete(@Param("orgCode") String orgCode, @Param("id") String id);
}
