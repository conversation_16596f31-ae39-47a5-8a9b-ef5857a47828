package jylink.cpds.dao;

import jylink.cpds.domain.AbnormalPushConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (AbnormalPushConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-10 16:52:20
 */
public interface IAbnormalPushConfigDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AbnormalPushConfig queryById(@Param("id") String id, @Param("orgCode") String orgCode);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param abnormalPushConfig 实例对象
     * @return 对象列表
     */
    List<AbnormalPushConfig> queryAll(AbnormalPushConfig abnormalPushConfig);

    /**
     * 通过实体作为筛选条件查询 count
     *
     * @param abnormalPushConfig 实例对象
     * @return count
     */
    long queryAllCount(AbnormalPushConfig abnormalPushConfig);

    /**
     * 新增数据
     *
     * @param abnormalPushConfig 实例对象
     * @return 影响行数
     */
    int insert(AbnormalPushConfig abnormalPushConfig);

    /**
     * 批量添加
     * @param list  添加实体
     * @return  是否添加成功
     */
    boolean addAll(@Param("list") List<AbnormalPushConfig> list);

    /**
     * 修改数据
     *
     * @param abnormalPushConfig 实例对象
     * @return 影响行数
     */
    int update(AbnormalPushConfig abnormalPushConfig);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 根据用户ID和机构删除配置信息
     * @param orgCode  机构编码
     * @param userId  用户ID
     * @return
     */
    boolean deleteByUserId(@Param("orgCode") String orgCode , @Param("userId") String userId);

    /**
     * 通过用户ID获取配置信息
     * @param orgCode  机构编码
     * @param userId  用户ID
     * @param openFlag 开启状态
     * @return  查询结果
     */
    List<AbnormalPushConfig> getByUserId(@Param("orgCode") String orgCode , @Param("userId") String userId , @Param("openFlag") Integer openFlag);

}
