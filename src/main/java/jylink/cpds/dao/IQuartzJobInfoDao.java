package jylink.cpds.dao;

import jylink.cpds.domain.QuartzJobInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * 定时任务模块
 */
@Mapper
public interface IQuartzJobInfoDao {
    /**
     * 添加数据
     *
     * @param instance 实体对象
     * @return 是否添加成功
     */
    boolean add(@Param("instance") QuartzJobInfo instance);

    /**
     * 更新数据
     * @param checkPlanId  设计id
     * @param orgCode       机构编码
     * @param isGround      是否完成探水
     * @return
     */
    boolean update(@Param("checkPlanId") String checkPlanId, @Param("orgCode") String orgCode, @Param("isGround") boolean isGround);
}
