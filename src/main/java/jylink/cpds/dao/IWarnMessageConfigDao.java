package jylink.cpds.dao;

import jylink.cpds.domain.WarnMessageConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (WarnMessageConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-25 17:33:41
 */
public interface IWarnMessageConfigDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    WarnMessageConfig queryById(@Param("id") String id, @Param("orgCode") String orgCode);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param warnMessageConfig 实例对象
     * @return 对象列表
     */
    List<WarnMessageConfig> queryAll(WarnMessageConfig warnMessageConfig);

    /**
     * 通过实体作为筛选条件查询 count
     *
     * @param warnMessageConfig 实例对象
     * @return count
     */
    long queryAllCount(WarnMessageConfig warnMessageConfig);

    /**
     * 获取全部配置
     * @return
     */
    List<WarnMessageConfig> getList();

}
