package jylink.cpds.dao;

import jylink.cpds.domain.DrawingExchangesConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图纸交换管理设置(DrawingExchangesConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-31 11:39:25
 */
public interface IDrawingExchangesConfigDao {

    /**
     * 通过ID查询单条数据
     * @param orgCode
     * @param id 主键
     * @return 实例对象
     */
    DrawingExchangesConfig queryById(@Param("id") String id, @Param("orgCode") String orgCode);

    /**
     * 通过ID查询单条数据
     * @param orgCode
     * @return 实例对象
     */
    DrawingExchangesConfig getByOrgCode( @Param("orgCode") String orgCode);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param drawingExchangesConfig 实例对象
     * @return 对象列表
     */
    List<DrawingExchangesConfig> queryAll(DrawingExchangesConfig drawingExchangesConfig);

    /**
     * 通过实体作为筛选条件查询 count
     *
     * @param drawingExchangesConfig 实例对象
     * @return count
     */
    long queryAllCount(DrawingExchangesConfig drawingExchangesConfig);

    /**
     * 新增数据
     *
     * @param drawingExchangesConfig 实例对象
     * @return 影响行数
     */
    int insert(DrawingExchangesConfig drawingExchangesConfig);

    /**
     * 批量添加
     * @param list
     * @return
     */
    boolean addAll(@Param("list") List<DrawingExchangesConfig> list);

    /**
     * 修改数据
     *
     * @param drawingExchangesConfig 实例对象
     * @return 影响行数
     */
    int update(DrawingExchangesConfig drawingExchangesConfig);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 根据配置管理机构删除下属全部煤矿配置
     * @param orgCode  机构编码
     * @return  查询结果
     */
    boolean deleteByConfigOrgCode(@Param("orgCode") String orgCode);

}
