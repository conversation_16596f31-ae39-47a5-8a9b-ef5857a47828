package jylink.cpds.dao;

import jylink.cpds.domain.EnforcementDetail;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 执法子表(EnforcementDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-09-21 14:24:39
 */
public interface IEnforcementDetailDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    EnforcementDetail queryById(@Param("id") String id, @Param("orgCode") String orgCode);

    
    /**
     * 通过实体作为筛选条件查询
     *
     * @param enforcementDetail 实例对象
     * @return 对象列表
     */
    List<EnforcementDetail> queryAll(EnforcementDetail enforcementDetail);
    /**
     * 通过实体作为筛选条件查询 count
     *
     * @param enforcementDetail 实例对象
     * @return count
     */
    long queryAllCount(EnforcementDetail enforcementDetail);

    /**
     * 新增数据
     *
     * @param enforcementDetail 实例对象
     * @return 影响行数
     */
    int insert(EnforcementDetail enforcementDetail);

    /**
     * 修改数据
     *
     * @param enforcementDetail 实例对象
     * @return 影响行数
     */
    int update(EnforcementDetail enforcementDetail);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);
    /**
     * 新增数据
     *
     * @param list 实例列表
     * @return 影响行数
     */
    boolean inserts(@Param("list") List<EnforcementDetail> list);

    /**
     * 根据执法人员id
     * @param enforcementMainUserId  执法人员id
     * @return  是否删除成功
     */
    boolean deleteByUserId(@Param("enforcementMainUserId") String enforcementMainUserId);

    /**
     * 根据执法主表id，机构编码，是否符合要求
     * @param mainId  执法主表id
     * @param accordWith  是否符合要求
     * @return  查询结果
     */
    List<EnforcementDetail> getByMainId(@Param("mainId") String mainId,@Param("accordWith") int accordWith);

}