package jylink.cpds.dao;

import jylink.cpds.domain.ExcavationReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * <p>
    * 掘进汇报表 Mapper 接口
    * </p>
 *
 * <AUTHOR>
 * @since 2019-10-14
 */
@Mapper
public interface IExcavationReportDao {
    /**
     * 添加数据
     *
     * @param instance 实体对象集合
     * @return 是否添加成功
     */
    boolean add(@Param("instance") ExcavationReport instance);

    /**
     * 查询数据
     * @param tunnelId
     * @param orgCode
     * @return
     */
    List<ExcavationReport> getData(@Param("tunnelId") String tunnelId, @Param("orgCode") String orgCode, @Param("startDate") String startDate, @Param("endDate") String endDate);
    List<ExcavationReport> getDataBySupervise(@Param("tunnelId") String tunnelId, @Param("orgCode") String orgCode, @Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
            * 根据设计ID查询数据
     * @param tunnelId
     * @param orgCode
     * @return
             */
    List<ExcavationReport> getByTunnelId(@Param("tunnelId") String tunnelId, @Param("orgCode") String orgCode);

    /**
     * 根据设计ID查询数据和汇报状态查询数据
     *
     * @param tunnelId
     * @param orgCode
     * @return
     */
    List<ExcavationReport> getByTunnelIdAndIsCorrect(@Param("tunnelId")String tunnelId, @Param("orgCode") String orgCode,@Param("isCorrect") int isCorrect);
    /**
     * 根据ID查询数据
     *
     * @param id
     * @param orgCode
     * @return
     */
    ExcavationReport getById(@Param("id") String id, @Param("orgCode") String orgCode);


    /**
     * 删除数据
     * @param id
     * @param orgCode
     * @return
     */
    boolean delete(@Param("id") String id, @Param("orgCode") String orgCode);

    /**
     * 修改数据
     * @param instance
     * @return
     */
    boolean update(@Param("instance") ExcavationReport instance);

    /**
     * 查询数据条数
     * @param orgCode
     * @param tunnelId
     * @param startDate
     * @param endDate
     * @return
     */
    long getCount(@Param("orgCode") String orgCode,
                  @Param("tunnelId") String tunnelId,
                  @Param("startDate") String startDate,
                  @Param("endDate") String endDate);
    long getCountBySupervise(@Param("orgCode") String orgCode,
                  @Param("tunnelId") String tunnelId,
                  @Param("startDate") String startDate,
                  @Param("endDate") String endDate);


    /**
     * 根据设计ID查询数据
     * @param tunnelId
     * @param orgCode
     * @return
     */
    List<ExcavationReport> getByClassAndTunnelIdAndWorkDate(@Param("tunnelId") String tunnelId, @Param("orgCode") String orgCode, @Param("workClassNumKey") String workClassNumKey, @Param("workDate") String workDate,@Param("isCorrect")String isCorrect);


    /**
     * 根据设计ID查询数据
     * @param tunnelId
     * @param orgCode
     * @return
     */
    List<ExcavationReport> getByTunnelIdAndWorkDate( @Param("tunnelId") String tunnelId,@Param("orgCode") String orgCode, @Param("workDate")String workDate,@Param("isCorrect") String isCorrect) throws ParseException;

    /**
     * 根据ids查询
     * @param ids
     * @param orgCode
     * @return
     */
    List<ExcavationReport> getByIds(@Param("ids") List<String> ids,@Param("orgCode") String orgCode);

    /**
     * 查询数据是否存在
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  是否存在
     */
    boolean anyById(@Param("id") String id, @Param("orgCode") String orgCode);

    /**
     * 根据设计ID查询数据
     * @param tunnelId  设计id
     * @param orgCode  机构编码
     * @return  查询结果
     */
    ExcavationReport getPosition(@Param("tunnelId") String tunnelId, @Param("orgCode") String orgCode);

    /**
     * 修改审批状态
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  是否成功
     */
    boolean updateStatus(@Param("id") String id,@Param("orgCode") String orgCode,@Param("status") Integer status);

    /**
     * 获取掘进汇报数据
     * @param tunnelId  设计id
     * @param predictionDate  计划施工日期
     * @param classStartTime  计划施工班次开始点
     * @return  查询结果
     */
    List<ExcavationReport> getExploreInfo(@Param("tunnelId") String tunnelId,@Param("predictionDate") Date predictionDate,@Param("classStartTime") Integer classStartTime);

    long getExploreCount(@Param("tunnelId") String tunnelId,@Param("predictionDate") Date predictionDate,@Param("classStartTime") Integer classStartTime);

    /**
     * 获取每个月的掘进汇报数据
     * @param orgCode
     * @param year
     * @param month
     * @return
     */
    Double getAppSumDutyFootage(@Param("orgCode") String orgCode , @Param("year") Integer year , @Param("month") Integer month);

    /**
     * 根据月份获取信息
     * @param orgCode  机构编码
     * @param year  年份
     * @param month  月份
     * @return
     */
    List<ExcavationReport> getInfoByMonth(@Param("orgCode") String orgCode , @Param("year") Integer year , @Param("month") Integer month);

}
