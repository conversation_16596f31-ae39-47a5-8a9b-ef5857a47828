package jylink.cpds.dao;

import jylink.cpds.domain.OrgLogicTree;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IOrgLogicTreeDao {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tfs_user_tree_test
     *
     * @mbg.generated Tue Mar 03 14:00:04 CST 2020
     */
    int insert(OrgLogicTree record);


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tfs_user_tree_test
     *
     * @mbg.generated Tue Mar 03 14:00:04 CST 2020
     */
    int insertSelective(OrgLogicTree record);

    List<OrgLogicTree> selectResult(@Param("treeType")String treeType, @Param("pid")String pid);

    int insertBatch(@Param("list")List<OrgLogicTree> list);

    long selectMaxSort(@Param("treeType")String treeType, @Param("pid")String pid);

    List<OrgLogicTree> selectTreeTypeByOrgCode(@Param("orgCode")String orgCode);
    List<OrgLogicTree> selectResultById(@Param("treeType")String treeType, @Param("id")String id);
    List<OrgLogicTree> selectResultByPid(@Param("id")String pid);
    List<OrgLogicTree> getOrgTreeByOrgCodeAndTreeType(@Param("treeType")String treeType,@Param("orgCode")String orgCode);

    int delByParentId(@Param("pid") String pid,@Param("treeType") String treeType);
    int delById(@Param("id") String id,@Param("treeType") String treeType);
    int delByTreeType(@Param("treeType") String treeType);
}