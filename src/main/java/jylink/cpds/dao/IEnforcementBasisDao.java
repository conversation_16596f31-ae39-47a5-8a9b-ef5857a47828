package jylink.cpds.dao;

import jylink.cpds.domain.EnforcementBasis;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * (EnforcementBasis)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-09-22 17:13:43
 */
public interface IEnforcementBasisDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    EnforcementBasis queryById(@Param("id") String id, @Param("orgCode") String orgCode);

    
    /**
     * 通过实体作为筛选条件查询
     *
     * @param enforcementBasis 实例对象
     * @return 对象列表
     */
    List<EnforcementBasis> queryAll(EnforcementBasis enforcementBasis);
    /**
     * 通过实体作为筛选条件查询 count
     *
     * @param enforcementBasis 实例对象
     * @return count
     */
    long queryAllCount(EnforcementBasis enforcementBasis);

    /**
     * 新增数据
     *
     * @param enforcementBasis 实例对象
     * @return 影响行数
     */
    int insert(EnforcementBasis enforcementBasis);

    /**
     * 修改数据
     *
     * @param enforcementBasis 实例对象
     * @return 影响行数
     */
    int update(EnforcementBasis enforcementBasis);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 根据执法类型查询执法文件名称
     * @param enforcementType  执法类型
     * @return  查询结果
     */
    List<EnforcementBasis> getName(@Param("enforcementType") String enforcementType);

}