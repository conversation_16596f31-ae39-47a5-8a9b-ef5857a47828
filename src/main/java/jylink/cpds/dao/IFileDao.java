package jylink.cpds.dao;

import jylink.cpds.domain.File;
import jylink.cpds.domain.FileReplenish;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * 文件表Dao
 */
@Mapper
public interface IFileDao {
    /**
     * 添加数据
     *
     * @param instance 实体对象集合
     * @return 是否添加成功
     */
    boolean add(@Param("instance") File instance);

    /**
     * 根据真实数据表Id删除数据
     *
     * @param dataId 数据表数据Id
     * @return 是否删除成功
     */
    boolean deleteByDataId(@Param("dataId") String dataId);

    /**
     * 删除数据
     *
     * @param id 数据Id
     * @return 是否删除成功
     */
    boolean deleteById(@Param("id") String id);

    /**
     * 根据真实数据表数据Id获取文件数据
     *
     * @param dataId 数据表数据Id
     * @return 文件数据集合
     */
    List<File> getByDataId(@Param("dataId") String dataId);

    /**
     * 根据真实数据表数据Id获取文件数据列表
     *
     * @param ids 数据表数据Id
     * @return 文件数据集合
     */
    List<File> getByDataIds(@Param("ids") List<String> ids);


    /**
     * 根据真实数据表数据Id获取文件数据列表
     *
     * @param ids 数据表数据Id
     * @return 文件数据集合
     */
    long getByDataCount(@Param("ids") List<String> ids);

    /**
     * 查询数据是否存在
     *
     * @param id     数据Id
     * @param dataId 业务表数据Id
     * @return 查询结果
     */
    boolean anyById(@Param("id") String id, @Param("dataId") String dataId);

    boolean any(@Param("id") String id);

    /**
     * 根据真实数据表数据Id获取文件数据
     *
     * @param dataId 数据表数据Id
     * @return 文件数据集合
     */
    List<File> getByDataIdAndModuleName(@Param("dataId") String dataId,@Param("moduleName") String moduleName);

    int addByDataIdCopy(@Param("id") String id,@Param("newUuid") String newUuid,@Param("dataId") String dataId);

    boolean deleteByDataIdAndModuleName(@Param("dataId") String dataId, @Param("moduleName")String moduleName);

    /**
     * 根据数据id和模块名称查询数据是否存在
     * @param dataId  数据id
     * @param moduleName  模块名称
     * @return  查询结果
     */
    boolean anyByDataIdAndModuleName(@Param("dataId") String dataId, @Param("moduleName")String moduleName);

    /**
     * 根据dataId 和 moduleName 获取数据  资料文件专用  返回对应replenish信息
     * <AUTHOR>
     * @date 2021/3/2 14:16
     * @param dataId
     * @param moduleName
     * @return java.util.List<jylink.cpds.domain.FileReplenish>
     */
    List<FileReplenish> getFileReplenish(@Param("dataId") String dataId, @Param("moduleName") String moduleName);
    /**
     *  资料文件专用  返回对应replenish信息
     * <AUTHOR>
     * @date 2021/3/2 14:49
     * @param ids
     * @return java.util.List<jylink.cpds.domain.FileReplenish>
     */
    List<FileReplenish> getReplenishByDataIds(@Param("ids") List<String> ids);
}
