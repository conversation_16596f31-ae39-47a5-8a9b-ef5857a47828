package jylink.cpds.dao;

import jylink.cpds.domain.CoalLayer;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 煤层配置表(CoalLayer)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-07-16 15:57:58
 */
public interface ICoalLayerDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    CoalLayer queryById(@Param("id") String id, @Param("orgCode") String orgCode);

    /**
     * 通过ID查询单条数据
     *
     * @param coalLayerKey 煤层key
     * @return 实例对象
     */
    CoalLayer getByKey(@Param("coalLayerKey") String coalLayerKey, @Param("orgCode") String orgCode);

    /**
     * 通过实体作为筛选条件查询
     * @param coalLayer 实例对象
     * @return 对象列表
     */
    List<CoalLayer> queryAll(CoalLayer coalLayer);
    /**
     * 通过实体作为筛选条件查询 count
     * @param coalLayer 实例对象
     * @return count
     */
    long queryAllCount(CoalLayer coalLayer);

    /**
     * 新增数据
     * @param coalLayer 实例对象
     * @return 影响行数
     */
    int insert(CoalLayer coalLayer);

    /**
     * 修改数据
     * @param coalLayer 实例对象
     * @return 影响行数
     */
    int update(CoalLayer coalLayer);

    /**
     * 通过主键删除数据
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    /**
     * 批量添加
     * @param list  添加实体
     * @return  是否添加成功
     */
    boolean addAll(@Param("list") List<CoalLayer> list);

    /**
     * 根据煤矿基本信息id删除煤层管理数据信息
     * @param orgCode  机构编码
     * @return  查询结果
     */
    boolean deleteByMineInfoId(@Param("orgCode") String orgCode);

    /**
     * 根据机构编码查询数据信息
     * @param orgCode  机构编码，
     * @return  查询结果
     */
    List<CoalLayer> getByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 根据机构编码列表查询数据信息
     * @param orgCode  机构编码列表
     * @return  查询结果
     */
    List<CoalLayer> getByOrgCodes(@Param("orgCodes") List<String> orgCode);

}