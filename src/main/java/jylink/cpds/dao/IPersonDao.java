package jylink.cpds.dao;

import jylink.cpds.annotation.DS;
import jylink.cpds.serviceModel.person.InterfaceRealResult;
import jylink.cpds.serviceModel.person.PersonInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper

public interface IPersonDao {

    /**
     * 根据机构编码和人员定位卡信息查询人员数据
     * @param orgCode
     * @param personCards
     * @param startTime
     * @param endTime
     * @return
     */
    @DS("person")
    List<InterfaceRealResult> getPersonInfo(@Param("orgCode") String orgCode , @Param("personCards") List<String> personCards , @Param("startTime") Date startTime , @Param("endTime") Date endTime);

    /**
     * 根据机构编码和人员定位卡信息查询人员数据
     * @param orgCode
     * @param personCards
     * @param startTime
     * @param endTime
     * @return
     */
    @DS("person")
    List<InterfaceRealResult> getHistoryPersonInfo(@Param("orgCode") String orgCode , @Param("personCards") List<String> personCards , @Param("startTime") Date startTime , @Param("endTime") Date endTime);

    /**
     * 获取人员轨迹
     * @param orgCode
     * @param personCard
     * @param mineEnterTime
     * @return
     */
    @DS("person")
    List<InterfaceRealResult> getPersonTrack(@Param("orgCode") String orgCode , @Param("personCard") String personCard , @Param("mineEnterTime") Date mineEnterTime);

    /**
     * 获取人员定位卡数据
     * @param orgCode
     * @param personCards
     * @return
     */
    @DS("person")
    List<PersonInfo> getPersonCardInfo(@Param("orgCode") String orgCode , @Param("personCards") List<String> personCards);

    /**
     * 获取轨迹数据
     * @param orgCode
     * @param personCards
     * @param startTime
     * @param endTime
     * @return
     */
    @DS("person")
    List<InterfaceRealResult> getRealInfoAndTrack(@Param("orgCode") String orgCode , @Param("personCards") List<String> personCards , @Param("startTime") Date startTime , @Param("endTime") Date endTime);

    /**
     * 获取轨迹数据
     * @param orgCode
     * @param personCards
     * @param startTime
     * @param endTime
     * @return
     */
    @DS("person")
    List<InterfaceRealResult> getHistoryInfoAndTrack(@Param("orgCode") String orgCode , @Param("personCards") List<String> personCards , @Param("startTime") Date startTime , @Param("endTime") Date endTime);

    /**
     * 根据机构编码和人员定位卡信息查询入井和出井时间段与指定时间范围有交集的实时人员数据
     * @param orgCode
     * @param personCards
     * @param startTime
     * @param endTime
     * @return
     */
    @DS("person")
    List<InterfaceRealResult> getPersonInfoByTimeIntersection(@Param("orgCode") String orgCode , @Param("personCards") List<String> personCards , @Param("startTime") Date startTime , @Param("endTime") Date endTime);

    /**
     * 根据机构编码和人员定位卡信息查询入井和出井时间段与指定时间范围有交集的历史人员数据
     * @param orgCode
     * @param personCards
     * @param startTime
     * @param endTime
     * @return
     */
    @DS("person")
    List<InterfaceRealResult> getHistoryPersonInfoByTimeIntersection(@Param("orgCode") String orgCode , @Param("personCards") List<String> personCards , @Param("startTime") Date startTime , @Param("endTime") Date endTime);

}
