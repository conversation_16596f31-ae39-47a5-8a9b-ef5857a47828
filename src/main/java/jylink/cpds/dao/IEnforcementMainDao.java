package jylink.cpds.dao;

import jylink.cpds.domain.EnforcementMain;
import jylink.cpds.serviceModel.params.EnforcementMainQueryModel;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 执法主表(EnforcementMain)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-09-21 14:24:21
 */
public interface IEnforcementMainDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    EnforcementMain queryById(@Param("id") String id, @Param("orgCode") String orgCode);

    
    /**
     * 通过实体作为筛选条件查询
     *
     * @param enforcementMain 实例对象
     * @return 对象列表
     */
    List<EnforcementMain> queryAll(EnforcementMainQueryModel enforcementMain);
    /**
     * 通过实体作为筛选条件查询 count
     *
     * @param enforcementMain 实例对象
     * @return count
     */
    long queryAllCount(EnforcementMainQueryModel enforcementMain);

    /**
     * 新增数据
     *
     * @param enforcementMain 实例对象
     * @return 影响行数
     */
    int insert(EnforcementMain enforcementMain);

    /**
     * 修改数据
     *
     * @param enforcementMain 实例对象
     * @return 影响行数
     */
    int update(EnforcementMain enforcementMain);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);
    /**
     * 通过主表id所有明细删除
     *
     * @param id 主表id
     * @return 影响行数
     */
    int deleteDetailByMainId(String id);

    /**
     * 根据id判断数据是否存在
     * @param id  主键id
     * @return  查询结果
     */
    boolean anyById(String id);

    /**
     * 根据主键id，状态和机构编码更新数据状态
     * @param id  主键id
     * @param status  状态
     * @param orgCode  机构编码
     * @return  是否更新成功
     */
    boolean updateStatus(@Param("id") String id,@Param("status") int status,@Param("orgCode") String orgCode);

    /**
     * 根据机构编码及用户id查询执法信息
     * @param orgCode  机构编码
     * @param userId  用户id
     * @return  查询结果
     */
    long getCount(@Param("orgCode") String orgCode,@Param("userId") String userId,@Param("orgName") String orgName,
                  @Param("startDate") String startDate,@Param("endDate") String endDate,@Param("mineCityZoneCode") String mineCityZoneCode,
                  @Param("mineCountyIdCode") String mineCountyIdCode, @Param("mineProvzoneCode") String mineProvzoneCode);

    /**
     * 根据机构编码和用户id查询数据信息
     * @param orgCode  机构编码
     * @param userId  用户id
     * @return  查询结果
     */
    List<EnforcementMain> getByUser(@Param("orgCode") String orgCode,@Param("userId") String userId,@Param("orgName") String orgName,
                                    @Param("startDate") String startDate,@Param("endDate") String endDate,@Param("mineCityZoneCode") String mineCityZoneCode,
                                    @Param("mineCountyIdCode") String mineCountyIdCode, @Param("mineProvzoneCode") String mineProvzoneCode);

}