package jylink.cpds.dao;

import jylink.cpds.domain.CoalInfoScreen;
import jylink.cpds.domain.DcmMineInfoMj;
import jylink.cpds.serviceModel.BasicOrgTree;
import jylink.cpds.serviceModel.dto.OrgMessageDto;
import jylink.cpds.serviceModel.dto.ZoneMineInfoDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (DcmMineInfoMj)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-26 10:08:40
 */
@Mapper
public interface IDcmMineInfoMjDao {

    /**
     * 通过县级编码 返回
     *
     * @param cityCode 县级编码
     * @return 对象列表
     */
    List<DcmMineInfoMj> queryBySityCode(String cityCode);
    /**
     * 通过县级编码和orgCodes 返回
     *
     * @param cityCode 县级编码
     * @return 对象列表
     */
    List<DcmMineInfoMj> queryBySityCodeAndOrgCodes(@Param("cityCode") String cityCode, @Param("orgCodes") List<String> orgCodes );

    /**
     * 通过机构编码返回
     * <AUTHOR>
     * @date 2020/5/6 18:11
     * @param orgCode
     * @return  jylink.cpds.domain.DcmMineInfoMj
     */
    DcmMineInfoMj queryBySityOrgCode(String orgCode);

    /**
     * 煤矿信息
     * <AUTHOR>
     * @date 2020/6/11 13:46
     * @return  jylink.cpds.domain.CoalInfoScreen
     */
    CoalInfoScreen getCoalInfo(@Param("orgCodes") List<String> orgCodes );

    /**
     * 根据区域编码查询煤矿信息
     * @param code  区域编码
     * @return  查询结果
     */
    List<ZoneMineInfoDto> getCoal(@Param("code") String code,@Param("orgName") String orgName);
    List<ZoneMineInfoDto> getCoalByOrgCodes(@Param("orgCodes") List<String> orgCodes);
    List<ZoneMineInfoDto> getCoalGroup(@Param("code") String code,@Param("orgName") String orgName,@Param("orgCodes") List<String> orgCodes);
    /**
     * 根据水文地质类型获取行政图矿点
     * <AUTHOR>
     * @date 2020/7/30 8:53
     * @param type
     * @return  java.util.List<jylink.cpds.domain.DcmMineInfoMj>
     */
    List<DcmMineInfoMj> queryBySityCodeAndType(@Param("orgCodes") List<String> orgCodes,@Param("type")String type );

    /**
     * 根据水文地质类型获取行政图矿点
     * <AUTHOR>
     * @date 2020/7/30 8:53
     * @param type
     * @return  java.util.List<jylink.cpds.domain.DcmMineInfoMj>
     */
    List<DcmMineInfoMj> queryByTypeAndCode(@Param("type")String type,@Param("orgName")String orgName,@Param("orgCodes") List<String> orgCodes );

    long queryByTypeAndCodeCount(@Param("orgCodes") List<String> orgCodes ,@Param("type")String type,@Param("orgName")String orgName );

    /**
     * 获取当前机构下全部的煤矿基本数据信息
     * @param orgCodes  机构编码列表
     * @return  查询结果
     */
    List<OrgMessageDto> getBaseInfo(@Param("orgCodes") List<String> orgCodes,@Param("orgName") String orgName);

    /**
     * 根据市区编码查询机构树
     * @param code  市区编码
     * @return  查询结果
     */
    List<BasicOrgTree> getOrgTree(@Param("code") String code);

    /**
     * 根据市区编码查询机构树
     * @param code  市区编码
     * @param parseSql
     * @return  查询结果
     */
    List<BasicOrgTree> getOrgTreeByCode(@Param("code") String code,@Param("parseSql") String parseSql);

    /**
     * 根据市区编码查询是否是城市编码
     * @param code  编码
     * @return  查询结果
     */
    boolean anyByCode(@Param("code") String code);

    /**
     * 根据区域编码查询煤矿个数
     * @param code  机构编码
     * @return  查询结果
     */
    CoalInfoScreen getCoalInfoByCode(@Param("code") String code);

    /**
     * 根据区域编码查询主体个数
     * @param code  区域编码
     * @return  查询结果
     */
    List<String> getMainBody(@Param("code") String code);

    /**
     * 根据附属集团查询煤矿基本数据信息
     * @param affiliatedGroup
     * @return
     */
    List<ZoneMineInfoDto> getCoalByAffiliatedGroup(@Param("affiliatedGroup")String affiliatedGroup);

}