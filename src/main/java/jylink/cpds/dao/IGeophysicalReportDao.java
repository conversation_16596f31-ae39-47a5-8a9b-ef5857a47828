package jylink.cpds.dao;

import jylink.cpds.domain.GeophysicalReport;
import jylink.cpds.serviceModel.ListItem;
import jylink.cpds.serviceModel.dto.BaseAbnormalItem;
import jylink.cpds.serviceModel.dto.LastGeophysicalDto;
import jylink.cpds.serviceModel.dto.OrgGeophysicalDto;
import jylink.cpds.serviceModel.dto.OrgMessageDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IGeophysicalReportDao {

    /**
     * 根据id查询
     * @param id  主键id
     * @return  查询结果
     */
    GeophysicalReport getById(@Param("id") String id);

    /**
     * 分页查询
     * @param orgCodes  机构编码
     * @param endDate  设计id
     * @param result  物探结果
     * @return  查询结果
     */
    List<GeophysicalReport> getByOrgCode(@Param("orgCodes") List<String> orgCodes,@Param("type") Integer type , @Param("orgName") String orgName,
                                         @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("result") String result,
                                         @Param("method") Integer method, @Param("mineCityZoneCode")String mineCityZoneCode,
                                         @Param("mineCountyIdCode") String  mineCountyIdCode, @Param("mineProvzoneCode") String mineProvzoneCode,
                                         @Param("orgCode") String orgCode , @Param("workFaceId") String workFaceId);

    /**
     * 分页查询
     * @param orgCode  机构编码
     * @param workName  设计id
     * @param result  物探结果
     * @return  查询结果
     */
    List<GeophysicalReport> getOrgCode(@Param("orgCode") String orgCode,@Param("workName") String workName,@Param("result") String result);

    /**
     * 查询数据个数
     * @param orgCode  机构编码
     * @param workName  设计id
     * @param result  物探结果
     * @return  个数
     */
    long getNum(@Param("orgCode") String orgCode,@Param("workName") String workName,@Param("result") String result);

    /**
     * 查询数据个数
     * @param orgName  机构名称
     * @param orgCodes 机构编码列表
     * @param result  物探结果
     * @return  个数
     */
    long getCount(@Param("orgCodes") List<String> orgCodes,@Param("type") Integer type , @Param("orgName") String orgName,
                  @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("result") String result,
                  @Param("method") Integer method, @Param("mineCityZoneCode")String mineCityZoneCode, @Param("mineCountyIdCode") String  mineCountyIdCode,
                  @Param("mineProvzoneCode") String mineProvzoneCode ,  @Param("orgCode") String orgCode , @Param("workFaceId") String workFaceId);

    /**
     * 添加数据
     * @param instance  数据实体
     * @return  是否添加成功
     */
    boolean add(@Param("instance") GeophysicalReport instance);

    /**
     * 修改数据
     * @param instance  数据实体
     * @return  是否添加成功
     */
    boolean update(@Param("instance") GeophysicalReport instance);

    /**
     * 根据id查询数据是否存在
     * @param id  主键id
     * @param orgCode  机构编码
     * @return  是否存在
     */
    boolean anyById(@Param("id") String id,@Param("orgCode") String orgCode);

    /**
     * 删除
     * @param id  主键id
     * @return  是否删除成功
     */
    boolean delete(@Param("id") String id,@Param("orgCode") String orgCode);

    /**
     * 根据设计id查询数据信息
     * @param tunnelId  设计id
     * @param orgCode  机构编码
     * @return  查询结果
     */
    List<GeophysicalReport> getByTunnelId(@Param("tunnelId") String tunnelId,@Param("orgCode") String orgCode);

    /**
     * 查询物探正常异常个数
     * @param orgCodes  机构编码
     * @param orgName  机构名称
     * @param month  查询月数
     * @return  查询结果
     */
    OrgGeophysicalDto getAllCount(@Param("orgCodes") List<String> orgCodes, @Param("orgName") String orgName,
                                          @Param("month") Integer month);

    /**
     * 查询物探正常异常个数
     * @param orgCodes  机构编码
     * @param orgName  机构名称
     * @param endDate
     * @return  查询结果
     */
    OrgGeophysicalDto getAllGeophysicalCount(@Param("orgCodes") List<String> orgCodes, @Param("orgName") String orgName, @Param("type") Integer type,
                                             @Param("startDate") String startDate, @Param("endDate") String endDate,
                                             @Param("result") String result,@Param("method") Integer method,
                                             @Param("orgCode") String orgCode , @Param("workFaceId") String workFaceId);

    /**
     * 查询物探正常异常个数
     * @param orgCodes  机构编码
     * @param orgName  机构名称
     * @param month  查询月数
     * @return  查询结果
     */
    List<OrgGeophysicalDto> getGeophysical(@Param("orgCodes") List<String> orgCodes, @Param("orgName") String orgName,
                                           @Param("month") Integer month);

    /**
     * 查询物探正常异常个数
     * @param orgCodes  机构编码
     * @param orgName  机构名称
     * @param month  查询月数
     * @return  查询结果
     */
    long getGeophysicalCount(@Param("orgCodes") List<String> orgCodes, @Param("orgName") String orgName,
                             @Param("month") Integer month);

    /**
     * 获取物探管理个数
     * @param orgCodes  机构编码列表
     * @return  查询结果
     */
    long getGeophysicalNum(@Param("orgCodes") List<String> orgCodes);

    /**
     * 获取近一年的物探次数
     * @param orgCodes  机构编码
     * @return  查询结果
     */
    long getGeophysicalNumByYear(@Param("orgCodes") List<String> orgCodes,@Param("startDate") String startDate, @Param("endDate") String endDate,
                                 @Param("orgName") String orgName , @Param("mineCityZoneCode") String mineCityZoneCode,
                                 @Param("mineCountyIdCode") String mineCountyIdCode, @Param("mineProvzoneCode") String mineProvzoneCode);

    /**
     *
     * @param orgCodes
     * @param orgName
     * @return
     */
    List<OrgMessageDto> getOrgGeophysicalNumByYear(@Param("orgCodes") List<String> orgCodes, @Param("orgName") String orgName);

    ListItem  getGeophysicalResultByPlanId(@Param("orgCode")String orgCode,@Param("planId")String planId);

    /**
     * 根据时间类型查询物探异常
     * @param orgCodes  机构列表
     * @param type  时间类型
     * @return  查询结果
     */
    BaseAbnormalItem getNumByType(@Param("orgCodes") List<String> orgCodes , @Param("type")  Integer type);

    /**
     *
     * @param orgCode
     * @param ids
     * @return
     */
    List<LastGeophysicalDto> getLastGeophysical(@Param("orgCode")String orgCode , @Param("ids") List<String> ids);

}
