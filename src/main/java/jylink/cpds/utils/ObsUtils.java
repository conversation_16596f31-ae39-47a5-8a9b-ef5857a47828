package jylink.cpds.utils;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.obs.services.ObsClient;
import com.obs.services.ObsConfiguration;
import com.obs.services.exception.ObsException;
import com.obs.services.model.*;
import jylink.cpds.config.ObsProConfig;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.*;

@SuppressWarnings("all")
@Slf4j
@Component
public class ObsUtils {

    private final static Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
            .retryIfResult(r -> !r)
            .retryIfExceptionOfType(ObsException.class)
            .withStopStrategy(StopStrategies.stopAfterAttempt(5))
            .withWaitStrategy(WaitStrategies.fixedWait(5, TimeUnit.SECONDS))
            .build();
    private final static Retryer<Long> longRetryer = RetryerBuilder.<Long>newBuilder()
            .retryIfResult(r -> r == -1)
            .retryIfExceptionOfType(ObsException.class)
            .withStopStrategy(StopStrategies.stopAfterAttempt(5))
            .withWaitStrategy(WaitStrategies.fixedWait(5, TimeUnit.SECONDS))
            .build();

    @FunctionalInterface
    public interface StreamingProgressCallback { void exec(ProgressStatus status);}
    @FunctionalInterface
    public interface StreamingAsyncWorkDoneCallback { void exec(boolean operateResult);}


    @FunctionalInterface
    public interface LargeFileProgressCallback { void exec(int curPart, long allPart, ProgressStatus status);}
    @FunctionalInterface
    public interface LargeFileAsyncWorkDoneCallback { void exec(boolean operateResult);}


    @FunctionalInterface
    public interface AppendProgressCallback { void exec(long curPosition, ProgressStatus status);}
    @FunctionalInterface
    public interface AppendAsyncWorkDoneCallback { void exec(long operateResult);}


    @FunctionalInterface
    public interface CheckpointProgressCallback { void exec(ProgressStatus status);}
    @FunctionalInterface
    public interface CheckpointAsyncWorkDoneCallback { void exec(boolean operateResult);}


    private final ObsProConfig obsConfig;
    private final ObsClient obsClient;
    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            10, 20, 60,
            TimeUnit.SECONDS, new ArrayBlockingQueue<>(100),
            new ThreadFactory() {
                private int count = 0;

                @Override
                public Thread newThread(@NotNull Runnable r) {
                    return new Thread(r, "obs-upload-thread-" + count++);
                }
            });
    public ObsUtils(ObsProConfig obsConfig) {
        this.obsConfig = obsConfig;
        ObsConfiguration config = new ObsConfiguration();
        config.setEndPoint(obsConfig.getEndPoint());
        obsClient = new ObsClient(obsConfig.getAccessKeyId(), obsConfig.getSecretAccessKey(), config);
    }


    private boolean streamingUpload(String objKey, File file, StreamingProgressCallback cb) {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(file.length());
        PutObjectRequest request = new PutObjectRequest();
        request.setBucketName(obsConfig.getBucketName());
        request.setObjectKey(objKey);
        request.setFile(file);
        request.setProgressListener(status -> {if (cb != null) {cb.exec(status);}});
        request.setProgressInterval(1024 * 1024L);
        request.setMetadata(metadata);
        PutObjectResult putObjectResult = obsClient.putObject(request);
        log.info("putObjectResult: {}", putObjectResult);
        int statusCode = putObjectResult.getStatusCode();
        return statusCode >= 200 && statusCode < 300;
    }
    public boolean streamingUploadSync(String objKey, File file, StreamingProgressCallback cb) {
        try {
            return streamingUpload(objKey, file, cb);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
    public Future<Boolean> streamingUploadAsync(String objKey, File file, StreamingProgressCallback cb, StreamingAsyncWorkDoneCallback acb) {
        return executor.submit(() -> {
            try {
                retryer.call(() -> streamingUploadSync(objKey, file, cb));
                if (acb != null) {acb.exec(true);}
                return true;
            } catch (Exception e) {
                log.error(e.getMessage());
                if (acb != null) {acb.exec(false);}
                return false;
            }
        });
    }


    private boolean largeFileUpload(String objKey, File localFile, LargeFileProgressCallback cb) {
        ExecutorService executorService = Executors.newFixedThreadPool(20);
        InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(obsConfig.getBucketName(), objKey);
        InitiateMultipartUploadResult result = obsClient.initiateMultipartUpload(request);
        final String uploadId = result.getUploadId();
        log.info("uploadId: {}", uploadId);
        long partSize = 100 * 1024 * 1024L;
        long fileSize = localFile.length();
        long partCount = fileSize % partSize == 0 ? fileSize / partSize : fileSize / partSize + 1;
        final List<PartEtag> partEtags = Collections.synchronizedList(new ArrayList<>());
        for (int i = 0; i < partCount; i++) {
            final long offset = i * partSize;
            final long currPartSize = (i + 1 == partCount) ? fileSize - offset : partSize;
            final int partNumber = i + 1;
            int finalI = i;
            executorService.execute(() -> {
                        UploadPartRequest uploadPartRequest = new UploadPartRequest();
                        uploadPartRequest.setBucketName(obsConfig.getBucketName());
                        uploadPartRequest.setObjectKey(objKey);
                        uploadPartRequest.setUploadId(uploadId);
                        uploadPartRequest.setFile(localFile);
                        uploadPartRequest.setPartSize(currPartSize);
                        uploadPartRequest.setOffset(offset);
                        uploadPartRequest.setPartNumber(partNumber);
                        uploadPartRequest.setProgressListener(progressStatus -> {
                            if (cb != null) {cb.exec(finalI + 1, partCount, progressStatus);}
                        });
                        uploadPartRequest.setProgressInterval(1024 * 1024L);
                        UploadPartResult uploadPartResult;
                        try {
                            uploadPartResult = obsClient.uploadPart(uploadPartRequest);
                            log.info("Part#{} done\n", partNumber);
                            partEtags.add(new PartEtag(uploadPartResult.getEtag(), uploadPartResult.getPartNumber()));
                        } catch (ObsException e) {
                            log.info(e.getMessage(), e);
                        }
                    }
            );
        }
        executorService.shutdown();
        while (!executorService.isTerminated()) {
            try {
                log.info("awaitTermination: {}", executorService.awaitTermination(5, TimeUnit.SECONDS));
            } catch (InterruptedException e) {
                log.info(e.getMessage(), e);
                Thread.currentThread().interrupt();
            }
        }
        CompleteMultipartUploadRequest completeMultipartUploadRequest =
                new CompleteMultipartUploadRequest(obsConfig.getBucketName(), objKey, uploadId, partEtags);
        CompleteMultipartUploadResult completeMultipartUploadResult = obsClient.completeMultipartUpload(completeMultipartUploadRequest);
        int statusCode = completeMultipartUploadResult.getStatusCode();
        return statusCode >= 200 && statusCode < 300;
    }
    public boolean largeFileUploadSync(String objKey, File file, LargeFileProgressCallback cb) {
        try {
            return largeFileUpload(objKey, file, cb);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
    public Future<Boolean> largeFileUploadAsync(String objKey, File file, LargeFileProgressCallback cb, LargeFileAsyncWorkDoneCallback acb) {
        return executor.submit(() -> {
            try {
                retryer.call(() -> largeFileUploadSync(objKey, file, cb));
                if (acb != null) {acb.exec(true);}
                return true;
            } catch (Exception e) {
                log.error(e.getMessage());
                if (acb != null) {acb.exec(false);}
                return false;
            }
        });
    }


    private long appendUpload(long position, String objKey, File file, AppendProgressCallback cb) {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(file.length());
        AppendObjectRequest request = new AppendObjectRequest();
        request.setBucketName(obsConfig.getBucketName());
        request.setObjectKey(objKey);
        request.setPosition(position);
        request.setMetadata(metadata);
        request.setFile(file);
        request.setProgressListener(status -> {if (cb != null) {cb.exec(position, status);}});
        request.setProgressInterval(1024 * 1024L);
        AppendObjectResult result = obsClient.appendObject(request);
        int statusCode = result.getStatusCode();
        if (statusCode >= 200 && statusCode < 300) {
            log.info("appendObject successfully");
            log.info("NextPosition: {}", result.getNextPosition());
            log.info("Etag: {}", result.getEtag());
            log.info("NextPosition from metadata: {}", result.getNextPosition());
            return result.getNextPosition();
        } else {
            return -1;
        }
    }
    public long appendUploadSync(long position, String objKey, File file, AppendProgressCallback cb) {
        try {
            return appendUpload(position, objKey, file, cb);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return -1;
        }
    }
    public Future<Long> appendUploadAsync(long position, String objKey, File file, AppendProgressCallback cb, AppendAsyncWorkDoneCallback acb) {
        return executor.submit(() -> {
            try {
                Long r = longRetryer.call(() -> appendUploadSync(position, objKey, file, cb));
                if (acb != null) {acb.exec(r);}
                return r;
            } catch (Exception e) {
                log.error(e.getMessage());
                if (acb != null) {acb.exec(-1L);}
                return -1L;
            }
        });
    }


    private boolean checkpointUpload(String objKey, File localFile, CheckpointProgressCallback cb) {
        UploadFileRequest request = new UploadFileRequest(obsConfig.getBucketName(), objKey);
        request.setUploadFile(localFile.getAbsolutePath());
        request.setTaskNum(5);
        request.setPartSize(10 * 1024 * 1024L);
        request.setEnableCheckpoint(true);
        request.setProgressListener(status -> {if (cb != null) {cb.exec(status);}});
        request.setProgressInterval(1024 * 1024L);
        CompleteMultipartUploadResult result = obsClient.uploadFile(request);
        int statusCode = result.getStatusCode();
        return statusCode >= 200 && statusCode < 300;
    }
    public boolean checkpointUploadSync(String objKey, File file, CheckpointProgressCallback cb) {
        try {
            return checkpointUpload(objKey, file, cb);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
    public Future<Boolean> checkpointUploadAsync(String objKey, File file, CheckpointProgressCallback cb, CheckpointAsyncWorkDoneCallback acb) {
        return executor.submit(() -> {
            try {
                retryer.call(() -> checkpointUploadSync(objKey, file, cb));
                if (acb != null) {acb.exec(true);}
                return true;
            } catch (Exception e) {
                log.error(e.getMessage());
                if (acb != null) {acb.exec(false);}
                return false;
            }
        });
    }


    public InputStream streamingDownload(String objKey) {
        try {
            GetObjectRequest request = new GetObjectRequest(obsConfig.getBucketName(), objKey);
            ObsObject obsObject = obsClient.getObject(request);
            return obsObject.getObjectContent();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }
    private boolean streamingDownload(String objKey, OutputStream bos, StreamingProgressCallback cb) {
        try {
            GetObjectRequest request = new GetObjectRequest(obsConfig.getBucketName(), objKey);
            request.setProgressListener(status -> {if (cb != null) {cb.exec(status);}});
            request.setProgressInterval(1024 * 1024L);
            ObsObject obsObject = obsClient.getObject(request);
            InputStream input = obsObject.getObjectContent();
            byte[] b = new byte[1024];
            int len;
            while ((len = input.read(b)) != -1) {
                bos.write(b, 0, len);
            }
            bos.close();
            input.close();
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
    public boolean streamingDownloadSync(String objKey, File file, StreamingProgressCallback cb) {
        try {
            return streamingDownload(objKey, Files.newOutputStream(file.toPath()), cb);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
    public Future<Boolean> streamingDownloadAsync(String objKey, File file, StreamingProgressCallback cb, StreamingAsyncWorkDoneCallback acb) {
        return executor.submit(() -> {
            try {
                retryer.call(() -> streamingDownloadSync(objKey, file, cb));
                if (acb != null) {acb.exec(true);}
                return true;
            } catch (Exception e) {
                log.error(e.getMessage());
                if (acb != null) {acb.exec(false);}
                return false;
            }
        });
    }


    private boolean rangeDownload(String objKey, OutputStream bos, long rangeStart, long rangeEnd, StreamingProgressCallback cb) {
        try {
            GetObjectRequest request = new GetObjectRequest(obsConfig.getBucketName(), objKey);
            request.setRangeStart(rangeStart);
            request.setRangeEnd(rangeEnd);
            request.setProgressListener(status -> {if (cb != null) {cb.exec(status);}});
            request.setProgressInterval(1024 * 1024L);
            ObsObject obsObject = obsClient.getObject(request);
            InputStream input = obsObject.getObjectContent();
            byte[] b = new byte[1024];
            int len;
            while ((len = input.read(b)) != -1) {
                bos.write(b, 0, len);
            }
            bos.close();
            input.close();
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
    public boolean rangeDownloadSync(String objKey, File file, long rangeStart, long rangeEnd, StreamingProgressCallback cb) {
        try {
            return rangeDownload(objKey, Files.newOutputStream(file.toPath()), rangeStart, rangeEnd, cb);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
    public Future<Boolean> rangeDownloadAsync(String objKey, File file, long rangeStart, long rangeEnd, StreamingProgressCallback cb, StreamingAsyncWorkDoneCallback acb) {
        return executor.submit(() -> {
            try {
                retryer.call(() -> rangeDownloadSync(objKey, file, rangeStart, rangeEnd, cb));
                if (acb != null) {acb.exec(true);}
                return true;
            } catch (Exception e) {
                log.error(e.getMessage());
                if (acb != null) {acb.exec(false);}
                return false;
            }
        });
    }


    private boolean checkpointDownload(String objKey, File localFile, CheckpointProgressCallback cb) {
        DownloadFileRequest request = new DownloadFileRequest(obsConfig.getBucketName(), objKey);
        request.setDownloadFile(localFile.getAbsolutePath());
        request.setTaskNum(5);
        request.setPartSize(10 * 1024 * 1024L);
        request.setEnableCheckpoint(true);
        request.setProgressListener(status -> {
            if (cb != null) {
                cb.exec(status);
            }
        });
        request.setProgressInterval(1024 * 1024L);
        DownloadFileResult result = obsClient.downloadFile(request);
        int statusCode = result.getObjectMetadata().getStatusCode();
        return statusCode >= 200 && statusCode < 300;
    }
    public boolean checkpointDownloadSync(String objKey, File file, CheckpointProgressCallback cb) {
        try {
            return checkpointDownload(objKey, file, cb);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
    public Future<Boolean> checkpointDownloadAsync(String objKey, File file, CheckpointProgressCallback cb, CheckpointAsyncWorkDoneCallback acb) {
        return executor.submit(() -> {
            try {
                retryer.call(() -> checkpointDownloadSync(objKey, file, cb));
                if (acb != null) {acb.exec(true);}
                return true;
            } catch (Exception e) {
                log.error(e.getMessage());
                if (acb != null) {acb.exec(false);}
                return false;
            }
        });
    }

    public boolean delete(String objKey) {
        try {
            DeleteObjectResult deleteObjectResult = obsClient.deleteObject(obsConfig.getBucketName(), objKey);
            log.info("deleteObjectResult: {}", deleteObjectResult);
            int statusCode = deleteObjectResult.getStatusCode();
            return statusCode >= 200 && statusCode < 300;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public boolean exist(String objKey) {
        try {
            return obsClient.doesObjectExist(obsConfig.getBucketName(), objKey);
        } catch (Exception e) {
            return false;
        }
    }

    public Optional<ObjectMetadata> getObjMetaData(String objKey) {
        try {
            ObjectMetadata metadata = obsClient.getObjectMetadata(obsConfig.getBucketName(), objKey);
            return Optional.of(metadata);
        } catch (Exception e) {
            return Optional.empty();
        }
    }
}
