package jylink.cpds.utils;

import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.*;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;
import org.bouncycastle.util.encoders.Hex;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SM2Utils {

    /**
     * 获取椭圆曲线
     * @return
     */
    public static synchronized ECDomainParameters getECDomainParameters() {
        X9ECParameters sm2ECParameters = GMNamedCurves.getByName("sm2p256v1");
        return new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());
    }

    /**
     * get key pair
     */
    public static Map<String, String> createKeyPair() throws NoSuchAlgorithmException {
        ECKeyPairGenerator keyPairGenerator = new ECKeyPairGenerator();
        keyPairGenerator.init(new ECKeyGenerationParameters(getECDomainParameters(), SecureRandom.getInstance("SHA1PRNG")));
        AsymmetricCipherKeyPair asymmetricCipherKeyPair = keyPairGenerator.generateKeyPair();
        Map<String, String> map = new HashMap<>();
        BigInteger bigInteger = ((ECPrivateKeyParameters) asymmetricCipherKeyPair.getPrivate()).getD();
        map.put("pveky", ByteUtils.toHexString(bigInteger.toByteArray()));
        // 把公钥放入map中,默认压缩公钥
        // 公钥前面的02或者03表示是压缩公钥,04表示未压缩公钥,04的时候,可以去掉前面的04
        ECPoint ecPoint = ((ECPublicKeyParameters) asymmetricCipherKeyPair.getPublic()).getQ();
        map.put("pbcky", ByteUtils.toHexString(ecPoint.getEncoded(false)));
        return map;
    }

    /**
     * 加密
     * @param plainText 需加密的明文字符串
     * @param publicKey 公钥
     */
    public static String encrypt(String plainText, String publicKey) throws IOException, InvalidCipherTextException {
        return encrypt(plainText.getBytes(), publicKey, SM2Engine.Mode.C1C2C3);
    }

    /**
     * 加密
     * @param plainByte 需加密的明文字节数组
     * @param publicKey 公钥
     * @param mode 加密模式 ModeTypeEnum
     */
    public static String encrypt(byte[] plainByte, String publicKey, SM2Engine.Mode mode) throws IOException, InvalidCipherTextException {
        ECDomainParameters domainParameters = getECDomainParameters();
        //提取公钥点
        ECPoint ecPoint = domainParameters.getCurve().decodePoint(ByteUtils.fromHexString(publicKey));
        // 公钥前面的02或者03表示是压缩公钥，04表示未压缩公钥, 04的时候，可以去掉前面的04
        ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(ecPoint, domainParameters);
        SM2Engine sm2Engine = new SM2Engine(mode);
        sm2Engine.init(true, new ParametersWithRandom(publicKeyParameters, new SecureRandom()));
        return ByteUtils.toHexString(sm2Engine.processBlock(plainByte, 0, plainByte.length));
    }

    /**
     * 解密
     * @param cipherText 需加密的字符串
     * @param privateKey 私钥
     */
    public static String decrypt(String cipherText, String privateKey) throws InvalidCipherTextException, UnsupportedEncodingException {
        return decrypt(Hex.decode(cipherText), privateKey, SM2Engine.Mode.C1C2C3);
    }

    /**
     * 解密
     * @param cipherDataByte 密文字节数组
     * @param privateKeyHex 私钥
     * @param mode 解密模式 ModeTypeEnum
     */
    public static String decrypt(byte[] cipherDataByte, String privateKeyHex, SM2Engine.Mode mode) throws InvalidCipherTextException, UnsupportedEncodingException {
        BigInteger bigInteger = new BigInteger(privateKeyHex, 16);
        ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(bigInteger, getECDomainParameters());
        SM2Engine sm2Engine = new SM2Engine(mode);
        sm2Engine.init(false, privateKeyParameters);
        return new String(sm2Engine.processBlock(cipherDataByte, 0, cipherDataByte.length), "utf-8");
    }


    public static void main(String[] args) {
        String encrypt = null;
        try {
            encrypt = encrypt("Jykj@2271055","046 ced3a96a0b0f896268b51406a66f153f56bf90d8846ce083c7dc3b41a6cd26090292aec19e10932788402e14179198e34b4d5dc5ce1756a6d6bb1b12c3ceb7e");
//            String decrypt = decrypt("04b4a1898ea582bda68ce3fa933689d1621a5b181dd61030778b5961717466da6266ab981193698eced5eff7945a03d87069fdac511e8109d6a790b29461c7dacd5fa9e862b21c18da3b7c8af40cb33f93bfd065aab2aea9859cf368a24efe5ce9ac37e33b0d0847c3dc74bc", "040c6a7c933e3b4ed70d38aa6dfaa250899495aca2dba3bab9f4dde3d9eca356f41fc26f97fc5501e685e7ef57aee2ac850b8eeea912dc27505ec266b20b15078f");
//            System.out.println(decrypt);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvalidCipherTextException e) {
            e.printStackTrace();
        }
        System.out.println(encrypt);
    }

}
