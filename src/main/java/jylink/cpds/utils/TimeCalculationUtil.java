package jylink.cpds.utils;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @Author: hupf
 * @Data:2023/6/9 16:39
 * @Description: 时间计算工具类
 */
public class TimeCalculationUtil {

    public static String getHoursBetween(Date startTime, Date endTime)  {

        long diffInMillies = endTime.getTime() - startTime.getTime();

        long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillies);

        if (diffInHours < 1) {

            return "1小时";

        } else if (diffInHours <= 24) {

            return String.format("%d小时", diffInHours);

        } else {

            int days = (int) (diffInHours / 24);

            int remainingHours = (int) (diffInHours % 24);

            return String.format("%d天%d小时", days, remainingHours);

        }
    }
}
