package jylink.cpds.filter;

import jylink.cpds.serviceModel.ActionResult;
import jylink.cpds.serviceModel.BindExceptionResult;
import jylink.cpds.serviceModel.JsonResponse;
import jylink.cpds.serviceModel.ListItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;

@SuppressWarnings("all")
@ControllerAdvice
public class ExceptionFilter {
    /**
     * Spring Boot环境变量
     */
    private final Environment environment;

    /**
     * 日志对象
     */
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    public ExceptionFilter(Environment environment) {
        this.environment = environment;
    }

    @ExceptionHandler(Exception.class)
    public ActionResult onException(HttpServletRequest request, Exception e) {
        logger.warn(MessageFormat.format("系统出错！requestUrl:{0}", request.getRequestURI()));
        logger.error(e.getMessage(), e);

        JsonResponse response = new JsonResponse();
        response.setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
        response.setMessage("系统开小差了，请稍后再试");

        return new ActionResult(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ActionResult onException(HttpServletRequest request, MethodArgumentNotValidException e) {
        logger.warn(MessageFormat.format("系统出错！requestUrl:{0}", request.getRequestURI()));
        logger.error(e.getMessage(), e);

        BindingResult bindingResult = e.getBindingResult();
        BindExceptionResult result = new BindExceptionResult();
        result.setObjectName(bindingResult.getObjectName());

        for (ObjectError allError : bindingResult.getAllErrors()) {
            FieldError error = (FieldError) allError;
            result.getKeyValuePairs().add(new ListItem(error.getField(), error.getField() + "字段:" + error.getDefaultMessage()));
        }

        JsonResponse response = new JsonResponse();
        response.setStatusCode(HttpStatus.BAD_REQUEST.value());
        response.setData(result);
        return new ActionResult(response, HttpStatus.BAD_REQUEST);
    }
}