package jylink.cpds.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
public class PersonConfig {

    @Value("${personFlag}")
    private boolean personFlag;
    @Value("${queryPersonType:0}")
    private Integer queryPersonType;
    @Value("${personUrl}")
    private String personUrl;
}
