package jylink.cpds.config;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description: 加解密
 * @date 2020/1/16 15:39
 * @version: V1.0
 */
@Slf4j
public class UrlUtf8Util {
    public static final String utf8UrlEncode(String text) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c >= 0 && c <= 255) {
                result.append(c);
            }else {
                byte[] b = new byte[0];
                try {
                    b = Character.toString(c).getBytes("UTF-8");
                }catch (Exception ex) {
                    log.error(ex.getMessage());
                }
                for (int j = 0; j < b.length; j++) {
                    int k = b[j];
                    if (k < 0) {
                        k += 256;
                    }
                    result.append("%" + Integer.toHexString(k).toUpperCase());
                }
            }
        }
        return result.toString();
    }

}
