package jylink.cpds.feign;

import com.ada.jykjcloudx.sdk.api.dto.UserInfo;
import com.ada.jykjcloudx.sdk.api.entity.OrgTree;
import com.ada.jykjcloudx.sdk.core.constant.enums.Enumondition;
import com.ada.jykjcloudx.sdk.core.util.R;
import jylink.cpds.serviceModel.RegionMessage;
import jylink.cpds.serviceModel.eadp.SysOrgOffLine;

import java.util.List;

public interface SecurtyUtilsService {
    UserInfo getUser(String token, String pathInfo );
    UserInfo getUserInfo();

    List<RegionMessage> getAllRegion();

    R<List<OrgTree>>  getRsListOrgUserTree(String token, String root, Enumondition yes, Enumondition no);

    /**
     * 获取所有的机构信息
     * @param param  参数
     * @return  查询结果
     */
    List<SysOrgOffLine> getAllOrg(String param);

}
