package jylink.cpds.feign;

import com.ada.jykjcloudx.sdk.api.entity.McMessageNormalDTO;
import jylink.cpds.serviceModel.JsonResponse;
import jylink.cpds.serviceModel.JsonResponseGeneric;
import jylink.cpds.serviceModel.dto.*;
import jylink.cpds.serviceModel.params.ApprovalReasonEditModel;
import jylink.cpds.serviceModel.params.ApprovalWorkflowParamsModel;

import java.util.List;

/**
 * 审批微服务交互feign
 *
 * <AUTHOR>
 */
public interface ApprovingFeignService {
    /**
     * 根据业务数据Id获取审批数据
     *
     * @param dataId 业务数据Id
     * @return 请求结果
     */
    JsonResponseGeneric<List<List<ApprovalWorkflowDto>>> getByDataId(String dataId);
    /**
     * 根据业务数据Id获取审批数据忽略token
     *
     * @param dataId 业务数据Id
     * @return 请求结果
     */
    JsonResponseGeneric<ApprovalWorkflowDto> getByDataIdIngoreToken(String dataId);
    /**
    * 各模块编辑时,获取当前审批流程与配置审批流程的不同处,做处理
    * <AUTHOR>
    * @date 2020/1/20 15:13
    * @return  jylink.cpds.serviceModel.JsonResponseGeneric<java.util.List<java.util.List<jylink.cpds.serviceModel.dto.ApprovalUserDto>>>
    */
    public JsonResponseGeneric<List<List<ApprovalUserDto>>> getFlowForEdit(String dataId, String moduleName);

    /**
     * 根据业务数据Id获取审批数据
     *
     * @param dataIds 业务数据Ids
     * @return 请求结果
     */
    JsonResponseGeneric<List<ApprovalWorkflowDto>> getByDataIds(List<String> dataIds);

    /**
     * 保存审批数据
     *
     * @param moduleName 模块名称
     * @param dataId     数据Id
     * @param msg        平台消息服务对象
     * @param flows      审批数据
     * @return 请求结果
     */
    JsonResponse save(String moduleName, String dataId, McMessageNormalDTO msg, List<List<ApprovalWorkflowParamsModel>> flows);

    /**
     * 判断用户是否已审批
     *
     * @param dataId 业务数据Id
     * @param userId 用户Id
     * @return 请求结果
     */
    boolean checkApproved(String dataId, String userId);

    /**
     * 保存审批数据
     *
     * @param moduleName 模块名称
     * @param dataId     数据Id
     * @param msg        平台消息服务对象
     * @param flows      审批数据
     * @return 请求结果
     */
    JsonResponse saveApproving(String moduleName, String dataId, McMessageNormalDTO msg, List<List<ApprovalWorkflowParamsModel>> flows);

    /**
     * 审批通过
     *
     * @param model 参数模型
     * @return 审批状态
     */
    JsonResponse approved(ApprovalReasonEditModel model);


    /**
     * 进入审批流程
     *
     * @param dataId 业务数据Id
     * @return 是否执行成功
     */
    JsonResponse approving(String dataId);

    /**
     * 拒绝或撤回
     * @param model
     * @return
     */
    JsonResponse refuseOrBack(ApprovalReasonEditModel model);

    /**
     * 查询待某个用户审批的全部数据
     * @param model  参数实体
     * @return  查询结果
     */
    JsonResponseGeneric<List<ApprovalWorkflowDto>> getApproving(ApprovalflowUserDto model);

    /**
     * 根据模块名称查询审批
     * @param moduleName  模块名称
     * @return  查询结果
     */
    JsonResponseGeneric<List<List<ApprovalUserDto>>> getByModuleName(String moduleName);

    /**
     * 获取各个模块待审批个数
     * @param orgCode  机构编码
     * @param userId  用户id
     * @return  查询结果
     */
    JsonResponseGeneric<List<ApprovalCountDto>> getCounts(String orgCode, String userId);
    /**
     * 获取用户配置信息
     * <AUTHOR>
     * @date 2020/7/22 9:02
     * @param userId
     * @return  jylink.cpds.serviceModel.JsonResponseGeneric<java.util.List<jylink.cpds.serviceModel.dto.ApprovalUserConfigDto>>
     */
    JsonResponseGeneric<ApprovalUserConfigDto> getUserConfig(String userId);

    /**
     *
     * @param orgCode
     * @param moduleNames
     * @return
     */
    List<ApprovalWorkflowDto> getLastApprovedData(String orgCode , List<String> moduleNames);

}
