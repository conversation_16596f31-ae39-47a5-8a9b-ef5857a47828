package jylink.cpds.rule.threeSpecial;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@SuppressWarnings("all")
@Slf4j
public class GreaterThreeSpecialExpression implements ThreeSpecialExpression {

    private final Object operateObj;

    private final Map<Field, List<String>> refByEnumCond = Maps.newHashMap();
    private final Map<Field, List<String>> refByNormalCond = Maps.newHashMap();

    private final Field checkF;
    private final String value;


    private String splitMark;


    private Map<String, Field> getFRefMap() {
        Map<String, Field> map = Maps.newHashMap();
        Field[] declaredFields = this.operateObj.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            if (field.isAnnotationPresent(ApiModelProperty.class)) {
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                map.put(annotation.notes(), field);
            }
        }
        return map;
    }

    public GreaterThreeSpecialExpression(Object operateObj, String strExpression) {
        this.operateObj = operateObj;
        if (strExpression.contains(">")) {
            splitMark = ">";
        }
        if (strExpression.contains(">=")) {
            splitMark = ">=";
        }
        Preconditions.checkArgument(!Strings.isNullOrEmpty(splitMark),
                "Expression is invalid: " + strExpression);
        String[] elements = strExpression.trim().split(splitMark);
        Preconditions.checkArgument(elements.length == 2,
                "Expression is invalid: " + strExpression);

        Pattern regex = Pattern.compile("^(.+?)\\[(.+?)\\]$");
        Matcher matcher = regex.matcher(elements[0].trim());
        if (matcher.find()) {

            String[] parts1 = matcher.group(1).trim().split(":");

            String[] strings = parts1[1].trim().split(",");

            Field f = getFRefMap().get(parts1[0]);
            if (f.isAnnotationPresent(CheckRuleHelper.class) && f.getAnnotation(CheckRuleHelper.class).fieldType().isEnum()) {
                Class<?> eType = f.getAnnotation(CheckRuleHelper.class).fieldType();
                try {
                    Method cnToEnMapM = eType.getDeclaredMethod(defaultCnConvertMethodName);
                    Map<String, String> cnToEnMap = (Map<String, String>) cnToEnMapM.invoke(null);
                    this.refByEnumCond.putIfAbsent(f, Lists.newArrayList());
                    for (String ss : strings) {
                        List<String> ssL = this.refByEnumCond.get(f);
                        String ssV = cnToEnMap.get(ss.trim());
                        ssL.add(ssV.trim());
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } else {
                this.refByNormalCond.putIfAbsent(f, Lists.newArrayList());
                for (String ss : strings) {
                    List<String> ssL = this.refByNormalCond.get(f);
                    ssL.add(ss.trim());
                }
            }

            this.checkF = getFRefMap().get(matcher.group(2).trim());
        } else {
            throw new IllegalArgumentException("Expression is invalid: " + strExpression);
        }
        this.value = elements[1].trim();
    }

    @Override
    public boolean interpret(Map<String, Object> recordMap, String recordKey) {
        try {

            for (Map.Entry<Field, List<String>> entry : this.refByNormalCond.entrySet()) {
                entry.getKey().setAccessible(true);
                String fv = entry.getKey().get(this.operateObj).toString();
                if (!entry.getValue().contains(fv)) {
                    return false;
                }
            }

            for (Map.Entry<Field, List<String>> entry : this.refByEnumCond.entrySet()) {
                entry.getKey().setAccessible(true);
                String fv = entry.getKey().get(this.operateObj).toString();
                if (!entry.getValue().contains(fv)) {
                    return false;
                }
            }

            long toValue = ValueExpression.v.getValue(this.value, this.operateObj);

            this.checkF.setAccessible(true);
            long fromValue = Long.parseLong(this.checkF.get(this.operateObj).toString());
            if (splitMark.equals(">")) {
                return fromValue > toValue;
            }
            boolean lastRs = fromValue >= toValue;
            if (!lastRs) {
                recordMap.put(recordKey+"Diff", toValue - fromValue);
            } else {
                recordMap.put(recordKey+"Diff", 0L);
            }
            return lastRs;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
}
