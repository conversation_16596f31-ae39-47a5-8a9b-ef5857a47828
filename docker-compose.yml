version: '3.5'

services: 
  cpds-kuang-supervise: 
    image: jylink/cpds.kuang.supervise:1.2
    container_name: cpds-kuang-supervise
    build: 
      context: ./supervise
      dockerfile: Dockerfile
    volumes:
      - /home/<USER>/static:/app/static
    restart: always
    network_mode: host
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "3"
        
  cpds-web-supervise:
    image: jylink/cpds.web.supervise:1.0
    container_name: cpds-web-supervise
    environment:
      LANG: C.UTF-8
    build:
      context: ./web
      dockerfile: Dockerfile
    restart: always
    network_mode: host
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "3"
